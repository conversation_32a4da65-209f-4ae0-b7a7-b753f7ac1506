<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>
<?import javafx.scene.image.*?>

<VBox xmlns="http://javafx.com/javafx/11.0.1"
      xmlns:fx="http://javafx.com/fxml/1"
      fx:controller="Controllers.PaymentFormController"
      spacing="20"
      alignment="CENTER"
      style="-fx-background-color: #f5f5f5; -fx-padding: 30; -fx-border-color: #2ecc71; -fx-border-width: 2; -fx-border-radius: 5;">

    <ImageView fitWidth="80" fitHeight="80">
    </ImageView>

    <Text text="Paiement confirmé!" style="-fx-font-size: 24px; -fx-fill: #2ecc71; -fx-font-weight: bold;" />

    <VBox spacing="10" alignment="CENTER">
        <Text fx:id="transactionIdText" style="-fx-font-size: 16px; -fx-fill: #333;" />
        <Text fx:id="amountText" style="-fx-font-size: 16px; -fx-fill: #333;" />
    </VBox>

    <Button text="Fermer"
            style="-fx-background-color: #2ecc71; -fx-text-fill: white; -fx-font-weight: bold; -fx-padding: 8 16;"
            onAction="#handleClose" />
</VBox>