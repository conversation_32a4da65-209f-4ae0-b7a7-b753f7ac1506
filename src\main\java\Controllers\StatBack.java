package Controllers;

import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.scene.chart.*;
import javafx.scene.control.Label;
import javafx.scene.layout.HBox;
import javafx.scene.layout.VBox;
import javafx.scene.shape.Rectangle;
import service.StaticService;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

public class StatBack {
    // For Count by Type
    @FXML private PieChart countPieChart;
    @FXML private BarChart<String, Number> countChartVertical;
    @FXML private BarChart<Number, String> countChartHorizontal;
    @FXML private VBox countLegendBox;

    // For Average Price
    @FXML private PieChart pricePieChart;
    @FXML private BarChart<String, Number> priceChartVertical;
    @FXML private BarChart<Number, String> priceChartHorizontal;
    @FXML private VBox priceLegendBox;

    // For Total Stock
    @FXML private PieChart stockPieChart;
    @FXML private BarChart<String, Number> stockChartVertical;
    @FXML private BarChart<Number, String> stockChartHorizontal;
    @FXML private VBox stockLegendBox;

    // For Price Range
    @FXML private PieChart rangePieChart;
    @FXML private BarChart<String, Number> rangeChartVertical;
    @FXML private BarChart<Number, String> rangeChartHorizontal;
    @FXML private VBox rangeLegendBox;

    private StaticService statsService = new StaticService();

    // Map pour associer chaque type de produit à une couleur spécifique
    private final Map<String, String> typeColors = new HashMap<>();
    {
        typeColors.put("Artisanat", "#FFA500");  // Orange
        typeColors.put("Artistique", "#32CD32"); // Vert
        typeColors.put("Livre", "#FFD700");      // Jaune doré
    }

    private final String[] customColors = {
            "#c49b63", // Artisanat
            "#8b6914", // Artistique
            "#282828", // Livre (gris foncé)
            "#2a2a2a",
            "#1e1e1e",
            "#000000",
            "#ffffff"  // Blanc (sera utilisé en dernier recours)
    };
    @FXML
    public void initialize() {
        try {
            // Initialiser les couleurs pour chaque type de produit
            initializeTypeColors();

            loadCountData();
            loadPriceData();
            loadStockData();
            loadRangeData();
            applyDarkTheme();
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }


    private void initializeTypeColors() throws SQLException {
        // Récupérer tous les types de produits depuis la base de données
        Map<String, Integer> productTypes = statsService.getProductCountByType();

        // Associer chaque type à une couleur dans l'ordre
        int colorIndex = 0;
        for (String type : productTypes.keySet()) {
            if (colorIndex < customColors.length) {
                typeColors.put(type, customColors[colorIndex]);
                colorIndex++;
            } else {
                // Si plus de types que de couleurs, recommencer depuis le début
                typeColors.put(type, customColors[colorIndex % customColors.length]);
                colorIndex++;
            }
        }
    }

    private void applyDarkTheme() {
        String darkBackground = "-fx-background-color: #1a1a1a;";
        String textColor = "-fx-text-fill: #ffffff;";

        // Appliquer le fond sombre à tous les graphiques
        PieChart[] pieCharts = {countPieChart, pricePieChart, stockPieChart, rangePieChart};
        for (PieChart pieChart : pieCharts) {
            pieChart.setStyle(darkBackground);
            pieChart.setLegendVisible(false);
        }

        BarChart<?, ?>[] barCharts = {
                countChartVertical, countChartHorizontal,
                priceChartVertical, priceChartHorizontal,
                stockChartVertical, stockChartHorizontal,
                rangeChartVertical, rangeChartHorizontal
        };

        for (BarChart<?, ?> barChart : barCharts) {
            barChart.setStyle(darkBackground);
            // [...] (le reste de la configuration des axes reste identique)
            barChart.setLegendVisible(false);
        }

        // Créer les légendes
        createLegends();
    }

    private void createLegends() {
        VBox[] legendBoxes = {countLegendBox, priceLegendBox, stockLegendBox, rangeLegendBox};

        for (VBox legendBox : legendBoxes) {
            if (legendBox == null) continue;

            legendBox.getChildren().clear();
            legendBox.setStyle("-fx-font-size: 14px; -fx-spacing: 5px;");

            Label title = new Label("Légende des couleurs:");
            title.setStyle("-fx-text-fill: white; -fx-font-weight: bold;");
            legendBox.getChildren().add(title);

            for (Map.Entry<String, String> entry : typeColors.entrySet()) {
                HBox legendItem = new HBox(5);
                Rectangle colorBox = new Rectangle(15, 15);
                colorBox.setStyle("-fx-fill: " + entry.getValue() + ";");

                Label typeLabel = new Label(entry.getKey());
                typeLabel.setStyle("-fx-text-fill: white;");

                legendItem.getChildren().addAll(colorBox, typeLabel);
                legendBox.getChildren().add(legendItem);
            }
        }
    }

    private void loadCountData() throws SQLException {
        Map<String, Integer> data = statsService.getProductCountByType();
        ObservableList<PieChart.Data> countPieData = FXCollections.observableArrayList();
        XYChart.Series<String, Number> countVerticalSeries = new XYChart.Series<>();
        XYChart.Series<Number, String> countHorizontalSeries = new XYChart.Series<>();

        countVerticalSeries.setName("Count");
        countHorizontalSeries.setName("Count");

        for (Map.Entry<String, Integer> entry : data.entrySet()) {
            String type = entry.getKey();
            PieChart.Data pieData = new PieChart.Data(type, entry.getValue());
            countPieData.add(pieData);

            XYChart.Data<String, Number> verticalData = new XYChart.Data<>(type, entry.getValue());
            XYChart.Data<Number, String> horizontalData = new XYChart.Data<>(entry.getValue(), type);

            countVerticalSeries.getData().add(verticalData);
            countHorizontalSeries.getData().add(horizontalData);
        }

        countPieChart.setData(countPieData);
        countChartVertical.getData().add(countVerticalSeries);
        countChartHorizontal.getData().add(countHorizontalSeries);

        // Apply colors
        applyColorsToCharts(countPieChart, countChartVertical, countChartHorizontal);
    }

    private void loadPriceData() throws SQLException {
        Map<String, Double> data = statsService.getAveragePriceByType();
        ObservableList<PieChart.Data> pricePieData = FXCollections.observableArrayList();
        XYChart.Series<String, Number> priceVerticalSeries = new XYChart.Series<>();
        XYChart.Series<Number, String> priceHorizontalSeries = new XYChart.Series<>();

        priceVerticalSeries.setName("Average Price");
        priceHorizontalSeries.setName("Average Price");

        for (Map.Entry<String, Double> entry : data.entrySet()) {
            String type = entry.getKey();
            PieChart.Data pieData = new PieChart.Data(type, entry.getValue());
            pricePieData.add(pieData);

            XYChart.Data<String, Number> verticalData = new XYChart.Data<>(type, entry.getValue());
            XYChart.Data<Number, String> horizontalData = new XYChart.Data<>(entry.getValue(), type);

            priceVerticalSeries.getData().add(verticalData);
            priceHorizontalSeries.getData().add(horizontalData);
        }

        pricePieChart.setData(pricePieData);
        priceChartVertical.getData().add(priceVerticalSeries);
        priceChartHorizontal.getData().add(priceHorizontalSeries);

        // Apply colors
        applyColorsToCharts(pricePieChart, priceChartVertical, priceChartHorizontal);
    }

    private void loadStockData() throws SQLException {
        Map<String, Integer> data = statsService.getTotalStockByType();
        ObservableList<PieChart.Data> stockPieData = FXCollections.observableArrayList();
        XYChart.Series<String, Number> stockVerticalSeries = new XYChart.Series<>();
        XYChart.Series<Number, String> stockHorizontalSeries = new XYChart.Series<>();

        stockVerticalSeries.setName("Total Stock");
        stockHorizontalSeries.setName("Total Stock");

        for (Map.Entry<String, Integer> entry : data.entrySet()) {
            String type = entry.getKey();
            PieChart.Data pieData = new PieChart.Data(type, entry.getValue());
            stockPieData.add(pieData);

            XYChart.Data<String, Number> verticalData = new XYChart.Data<>(type, entry.getValue());
            XYChart.Data<Number, String> horizontalData = new XYChart.Data<>(entry.getValue(), type);

            stockVerticalSeries.getData().add(verticalData);
            stockHorizontalSeries.getData().add(horizontalData);
        }

        stockPieChart.setData(stockPieData);
        stockChartVertical.getData().add(stockVerticalSeries);
        stockChartHorizontal.getData().add(stockHorizontalSeries);

        // Apply colors
        applyColorsToCharts(stockPieChart, stockChartVertical, stockChartHorizontal);
    }

    private void loadRangeData() throws SQLException {
        Map<String, Double> data = statsService.getPriceRangeByType();
        ObservableList<PieChart.Data> rangePieData = FXCollections.observableArrayList();
        XYChart.Series<String, Number> rangeVerticalSeries = new XYChart.Series<>();
        XYChart.Series<Number, String> rangeHorizontalSeries = new XYChart.Series<>();

        rangeVerticalSeries.setName("Price Range");
        rangeHorizontalSeries.setName("Price Range");

        for (Map.Entry<String, Double> entry : data.entrySet()) {
            String type = entry.getKey();
            PieChart.Data pieData = new PieChart.Data(type, entry.getValue());
            rangePieData.add(pieData);

            XYChart.Data<String, Number> verticalData = new XYChart.Data<>(type, entry.getValue());
            XYChart.Data<Number, String> horizontalData = new XYChart.Data<>(entry.getValue(), type);

            rangeVerticalSeries.getData().add(verticalData);
            rangeHorizontalSeries.getData().add(horizontalData);
        }

        rangePieChart.setData(rangePieData);
        rangeChartVertical.getData().add(rangeVerticalSeries);
        rangeChartHorizontal.getData().add(rangeHorizontalSeries);

        // Apply colors
        applyColorsToCharts(rangePieChart, rangeChartVertical, rangeChartHorizontal);
    }

    private void applyColorsToCharts(PieChart pieChart, BarChart<String, Number> verticalChart, BarChart<Number, String> horizontalChart) {
        // Appliquer les couleurs au PieChart
        for (PieChart.Data data : pieChart.getData()) {
            String color = typeColors.getOrDefault(data.getName(), "#FFFFFF");
            data.getNode().setStyle("-fx-pie-color: " + color + ";");
        }

        // Appliquer les couleurs au BarChart vertical
        for (XYChart.Series<String, Number> series : verticalChart.getData()) {
            for (XYChart.Data<String, Number> data : series.getData()) {
                String color = typeColors.getOrDefault(data.getXValue(), "#FFFFFF");
                data.getNode().setStyle("-fx-bar-fill: " + color + ";");
            }
        }

        // Appliquer les couleurs au BarChart horizontal
        for (XYChart.Series<Number, String> series : horizontalChart.getData()) {
            for (XYChart.Data<Number, String> data : series.getData()) {
                String color = typeColors.getOrDefault(data.getYValue(), "#FFFFFF");
                data.getNode().setStyle("-fx-bar-fill: " + color + ";");
            }
        }
    }
}
