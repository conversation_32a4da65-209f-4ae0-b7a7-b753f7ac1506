<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.ScrollPane?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.layout.HBox?>
<?import javafx.geometry.Insets?>

<AnchorPane prefHeight="600.0" prefWidth="800.0" xmlns="http://javafx.com/javafx/8"
            xmlns:fx="http://javafx.com/fxml/1"
            fx:controller="Controllers.AfficherCommentaireController">

   <children>
      <VBox layoutX="20.0" layoutY="20.0" spacing="20.0" AnchorPane.bottomAnchor="20.0" 
            AnchorPane.leftAnchor="20.0" AnchorPane.rightAnchor="20.0" AnchorPane.topAnchor="20.0">
         <children>
            <HBox alignment="CENTER_LEFT" spacing="15.0">
               <children>
                  <Label text="Commentaires" style="-fx-font-size: 24px; -fx-font-weight: bold;" />
                  <Button fx:id="btnRetour" text="Retour" onAction="#retour" 
                          style="-fx-background-color: #2196F3; -fx-text-fill: white;" 
                          HBox.hgrow="ALWAYS" />
               </children>
            </HBox>

            <ScrollPane fitToWidth="true" VBox.vgrow="ALWAYS">
               <content>
                  <VBox fx:id="commentContainer" spacing="15.0">
                     <padding>
                        <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
                     </padding>
                  </VBox>
               </content>
            </ScrollPane>
         </children>
      </VBox>
   </children>
</AnchorPane> 