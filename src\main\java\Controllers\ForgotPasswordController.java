package Controllers;

import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.control.Alert;
import javafx.scene.control.TextField;
import javafx.stage.Stage;
import service.UserService;

import java.sql.SQLException;

public class ForgotPasswordController {

    @FXML
    private TextField emailField;

    private final UserService userService = new UserService();

    @FXML
    public void handleSendCode() throws SQLException {
        String email = emailField.getText().trim();

        if (email.isEmpty()) {
            showAlert("Erreur", "Veuillez saisir votre adresse email.");
            return;
        }

        boolean success = userService.sendResetCode(email);
        if (success) {
            showAlert("Succès", "Un code de réinitialisation a été envoyé.");

            // Rediriger vers TokenVerification.fxml
            try {
                FXMLLoader loader = new FXMLLoader(getClass().getResource("/ResetVerification.fxml"));
                Parent root = loader.load();
                Stage stage = new Stage();
                stage.setTitle("Vérification du code");
                stage.setScene(new Scene(root));
                stage.show();

                // Fermer la fenêtre actuelle
                ((Stage) emailField.getScene().getWindow()).close();
            } catch (Exception e) {
                e.printStackTrace();
                showAlert("Erreur", "Impossible d’ouvrir l’écran de vérification.");
            }

        } else {
            showAlert("Erreur", "Email introuvable.");
        }
    }

    private void showAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setContentText(message);
        alert.showAndWait();
    }
}
