package Controllers;

import entity.Commentaire;
import entity.Post;
import entity.User;
import entity.UserSession;
import javafx.event.ActionEvent;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import javafx.scene.control.Alert;
import javafx.scene.control.Button;
import javafx.scene.control.TextArea;
import service.CommentaireService;

import java.io.IOException;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

public class AjouterCommentaireController {
    private Post post;
    private final CommentaireService cs = new CommentaireService();

    @FXML
    private TextArea contenuTxt;

    @FXML
    private Button btnAjouter;

    @FXML
    private Button btnAnnuler;

    public void setPost(Post post) {
        this.post = post;
    }

    @FXML
    void addCommentaire(ActionEvent event) {

        // Récupérer l'utilisateur connecté
        User user = UserSession.getInstance().getUser();
        if (user == null) {
            showAlert(Alert.AlertType.ERROR, "Erreur", "Vous devez être connecté pour créer un post");
            return;
        }

        // Validation du contenu du commentaire
        String contenu = contenuTxt.getText().trim();
        if (contenu.isEmpty()) {
            showAlert(Alert.AlertType.WARNING, "Erreur de validation", "Le commentaire ne peut pas être vide");
            return;
        }
        
        if (contenu.length() < 3) {
            showAlert(Alert.AlertType.WARNING, "Erreur de validation", "Le commentaire doit contenir au moins 3 caractères");
            return;
        }

        try {
            // Create a Commentaire object
            Commentaire commentaire = new Commentaire();
            commentaire.setPost(post);
            commentaire.setContenu(contenu);
            commentaire.setDate_commentaire(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            commentaire.setNbr_like_commentaire(0);
            commentaire.getUser();
            // Save to database
            cs.ajouterCommentaire(commentaire);
            
            // Show success message
            showAlert(Alert.AlertType.INFORMATION, "Succès", "Commentaire ajouté avec succès");

            // Navigate back to the post view
            navigateBackToPost();
            
        } catch (SQLException e) {
            showAlert(Alert.AlertType.ERROR, "Erreur", "Erreur lors de l'ajout du commentaire: " + e.getMessage());
        }
    }

    @FXML
    void annuler(ActionEvent event) {
        navigateBackToPost();
    }

    private void navigateBackToPost() {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getClassLoader().getResource("afficherPost.fxml"));
            Parent root = loader.load();
            
            // Get the controller and refresh the posts list
            AfficherPostController controller = loader.getController();
            controller.refreshPosts();
            
            // Update the scene
            btnAjouter.getScene().setRoot(root);
            
        } catch (IOException e) {
            showAlert(Alert.AlertType.ERROR, "Navigation Error", 
                     "Impossible de revenir à la vue des posts: " + e.getMessage());
        }
    }

    private void showAlert(Alert.AlertType alertType, String title, String content) {
        Alert alert = new Alert(alertType);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(content);
        alert.showAndWait();
    }
} 