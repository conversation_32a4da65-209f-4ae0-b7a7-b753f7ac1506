<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.TextArea?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.layout.HBox?>
<?import javafx.geometry.Insets?>
<?import javafx.scene.effect.DropShadow?>

<AnchorPane prefHeight="600.0" prefWidth="800.0" style="-fx-background-color: #F5F0E6;" 
            xmlns="http://javafx.com/javafx/8" 
            xmlns:fx="http://javafx.com/fxml/1" 
            fx:controller="Controllers.AjouterPostController">
   <children>
      <VBox layoutX="20.0" layoutY="20.0" spacing="20.0" 
            AnchorPane.bottomAnchor="20.0" AnchorPane.leftAnchor="20.0" 
            AnchorPane.rightAnchor="20.0" AnchorPane.topAnchor="20.0">
         <padding>
            <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
         </padding>
         
         <!-- Header -->
         <HBox alignment="CENTER_LEFT" spacing="15.0" 
               style="-fx-background-color: #3E2723; -fx-padding: 15; -fx-background-radius: 10;">
            <Label style="-fx-font-size: 24px; -fx-font-weight: bold; -fx-text-fill: #F5F0E6; -fx-font-family: 'Segoe UI';" 
                   text="Créer un nouveau post" />
         </HBox>
         
         <!-- Description -->
         <VBox spacing="5">
            <Label style="-fx-font-size: 16px; -fx-text-fill: #3E2723; -fx-font-weight: bold;" 
                   text="Description du post:" />
            <TextArea fx:id="descriptionTxt" 
                     style="-fx-background-color: white; -fx-border-color: #8D6E63; -fx-border-radius: 8; -fx-padding: 10; -fx-font-size: 14px; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 5, 0, 0, 1);"
                     prefHeight="100.0" />
         </VBox>
         
         <!-- Image Section -->
         <VBox spacing="5">
            <Label style="-fx-font-size: 16px; -fx-text-fill: #3E2723; -fx-font-weight: bold;" 
                   text="Image:" />
            <HBox spacing="10" alignment="CENTER_LEFT">
               <Button fx:id="btnChoisirImage" onAction="#choisirImage"
                       style="-fx-background-color: #8D6E63; -fx-text-fill: white; -fx-font-weight: bold; -fx-padding: 10 20; -fx-background-radius: 8; -fx-font-size: 14px; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 10, 0, 0, 2);"
                       text="Choisir une image" />
               <ImageView fx:id="imageView" fitHeight="150.0" fitWidth="200.0" 
                         style="-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 10, 0, 0, 2);" />
            </HBox>
         </VBox>
         
         <!-- Buttons -->
         <HBox alignment="CENTER" spacing="20" style="-fx-padding: 20;">
            <Button fx:id="btnAnnuler" onAction="#handleAnnuler"
                    style="-fx-background-color: #D32F2F; -fx-text-fill: white; -fx-font-weight: bold; -fx-padding: 12 30; -fx-background-radius: 8; -fx-font-size: 16px; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 10, 0, 0, 2);"
                    text="Annuler" />
            <Button fx:id="btnAjouter" onAction="#addPost"
                    style="-fx-background-color: #3E2723; -fx-text-fill: white; -fx-font-weight: bold; -fx-padding: 12 30; -fx-background-radius: 8; -fx-font-size: 16px; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 10, 0, 0, 2);"
                    text="Ajouter le Post" />
         </HBox>
      </VBox>
   </children>
</AnchorPane>
