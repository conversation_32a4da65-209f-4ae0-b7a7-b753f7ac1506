package Controllers;

import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.scene.Node;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.control.Label;
import javafx.scene.control.TextField;
import javafx.stage.Stage;
import service.UserService;

public class TokenVerificationController {

    @FXML
    private TextField codeField;

    @FXML
    private Label errorLabel;

    private final UserService userService = new UserService();

    @FXML
    public void handleCodeVerification() {
        String code = codeField.getText().trim();

        if (code.length() != 6 || !code.matches("\\d+")) {
            errorLabel.setText("Code invalide.");
            return;
        }

        try {
            if (userService.verifyResetCode(code)) {
                // Créer le FXMLLoader
                FXMLLoader loader = new FXMLLoader(getClass().getResource("/ResetPassword.fxml"));

                // Créer une instance du contrôleur et injecter le code
                ResetPasswordController controller = new ResetPasswordController();
                controller.setResetCode(code);
                loader.setController(controller);

                // Charger la vue avec le contrôleur personnalisé
                Parent root = loader.load();
                Stage stage = new Stage();
                stage.setScene(new Scene(root));
                stage.setTitle("Nouveau mot de passe");
                stage.show();

                codeField.getScene().getWindow().hide();

            } else {
                errorLabel.setText("Code invalide ou expiré.");
            }
        } catch (Exception e) {
            e.printStackTrace();
            errorLabel.setText("Erreur lors de la vérification.");
        }
    }

}
