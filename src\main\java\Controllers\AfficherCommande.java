package Controllers;

import entity.Commande;
import entity.Produit;
import entity.User;
import entity.UserSession;
import javafx.application.Platform;
import javafx.concurrent.Worker;
import javafx.event.ActionEvent;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.scene.Node;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.scene.layout.HBox;
import javafx.scene.layout.TilePane;
import javafx.scene.layout.VBox;
import javafx.scene.text.Text;
import javafx.scene.web.WebEngine;
import javafx.stage.Modality;
import javafx.stage.Stage;
import service.CommandeService;
import service.ProduitService;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.sql.SQLException;
import java.util.List;
import java.util.Optional;
import java.util.ResourceBundle;
import com.stripe.exception.StripeException;
import javafx.scene.web.WebView;
import javafx.stage.Stage;
import service.PaymentService;
public class AfficherCommande  implements Initializable {
    private static final String DEFAULT_IMAGE = "/images/default-product.png";
    // Navigation methods
    @FXML
    private void navigateToAccueil(ActionEvent event) { loadPage("/Accueil.fxml", event); }
    @FXML
    private void navigateToRencontres(ActionEvent event) { loadPage("/Rencontres.fxml", event); }
    @FXML
    private void navigateToEvenements(ActionEvent event) { loadPage("/AfficherEvenementUser.fxml", event); }
    @FXML
    private void navigateToForum(ActionEvent event) { loadPage("/Forum.fxml", event); }
    @FXML
    private void navigateToBoutique(ActionEvent event) { loadPage("/AfficherProduit.fxml", event); }
    @FXML
    private void navigateToConnexion(ActionEvent event) { loadPage("/Connexion.fxml", event); }

    private void loadPage(String fxmlPath, ActionEvent event) {
        try {
            Parent root = FXMLLoader.load(getClass().getResource(fxmlPath));
            Stage stage = (Stage)((Node)event.getSource()).getScene().getWindow();
            stage.setScene(new Scene(root));
            stage.show();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
    @FXML private ScrollPane scrollPane;
    @FXML private VBox commandesContainer;

    private final CommandeService commandeService = new CommandeService();
    private final ProduitService produitService = new ProduitService();
    @FXML private ImageView logoImageView;
    @FXML private Button connexionButton;
    private User currentUser;

    // Initialisation unique

    private void checkUserSession() {
        if (connexionButton == null) {
            System.err.println("Warning: connexionButton is null");
            return;
        }

        currentUser = UserSession.getInstance().getUser();
        if (currentUser != null) {
            connexionButton.setText("Profil");
            connexionButton.setOnAction(this::navigateToProfile);
        } else {
            connexionButton.setText("Connexion");
            connexionButton.setOnAction(this::navigateToConnexion);
        }
    }

    // Méthodes de navigation
    @FXML
    private void navigateToProfile(ActionEvent event) {
        loadPage("/ProfilUser.fxml", event);
    }
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        try {
            // Initialisation du logo
            if (logoImageView != null) {
                Image logo = new Image(getClass().getResource("/logofront.png").toExternalForm());
                logoImageView.setImage(logo);
            }

            // Configuration du style du ScrollPane
            scrollPane.setStyle("-fx-background: transparent; -fx-background-color: transparent;");

            // Vérification de la session utilisateur
            checkUserSession();

            // Chargement des commandes
            loadCommandes();

        } catch (Exception e) {
            System.err.println("Error initializing controller: " + e.getMessage());
        }

    }


    private User getCurrentUser() {
        return UserSession.getInstance().getUser();
    }


    /*public void loadCommandes() {
        try {
            commandesContainer.getChildren().clear();
            User currentUser = getCurrentUser();

            // Vérification de la connexion
            if (currentUser == null) {
                showErrorText("Veuillez vous connecter pour voir vos commandes");
                return;
            }

            // Récupération des commandes spécifiques à l'utilisateur
            List<Commande> commandes = commandeService.recupererCommandesParUser(currentUser.getId());

            if (commandes.isEmpty()) {
                showInfoText("Vous n'avez aucune commande");
            } else {
                for (Commande commande : commandes) {
                    try {
                        Produit produit = commande.getProduit();
                        // Si le produit n'est pas complet, on le récupère
                        if (produit.getNom_produit() == null) {
                            produit = produitService.recupererProduitParId(produit.getId_produit());
                        }
                        commandesContainer.getChildren().add(createCommandeCard(commande, produit));
                    } catch (SQLException e) {
                        e.printStackTrace();
                        showErrorText("Erreur lors du chargement d'une commande");
                    }
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
            showErrorText("Erreur lors du chargement des commandes");
        }
    }*/

    private void showErrorText(String message) {
        Text errorText = new Text(message);
        errorText.setStyle("-fx-font-size: 16; -fx-fill: red;");
        commandesContainer.getChildren().add(errorText);
    }

    private void showInfoText(String message) {
        Text infoText = new Text(message);
        infoText.setStyle("-fx-font-size: 16; -fx-fill: white;");
        commandesContainer.getChildren().add(infoText);
    }

    // Modifiez la méthode createCommandeCard pour retourner un HBox au lieu d'un VBox
    private HBox createCommandeCard(Commande commande, Produit produit) {
        HBox card = new HBox(20);
        card.setStyle("-fx-background-color: rgba(40,40,40,0.9); -fx-padding: 15; -fx-border-radius: 10; -fx-background-radius: 10; -fx-border-color: #c49b63; -fx-border-width: 1;");
        card.setPrefSize(800, 200);

        // Image du produit
        ImageView imageView = createProductImageView(produit);
        imageView.setFitWidth(180);
        imageView.setFitHeight(180);

        // VBox pour les détails
        VBox detailsBox = new VBox(10);
        detailsBox.setStyle("-fx-padding: 0 0 0 10;");

        // Header avec ID de commande
        Text commandeId = new Text("Commande #" + commande.getId_commande());
        commandeId.setStyle("-fx-font-weight: bold; -fx-font-size: 18; -fx-fill: #c49b63;");

        // Détails du produit
        Text nomProduit = new Text(produit.getNom_produit());
        nomProduit.setStyle("-fx-font-weight: bold; -fx-font-size: 16; -fx-fill: white;");

        Text typeProduit = new Text("Type: " + produit.getType_produit());
        typeProduit.setStyle("-fx-font-size: 14; -fx-fill: white;");

        Text quantite = new Text("Quantité: " + commande.getQuantite_produit());
        quantite.setStyle("-fx-font-size: 14; -fx-fill: white;");

        Text prixUnitaire = new Text("Prix unitaire: " + String.format("%.2f", produit.getPrix_produit()) + " TND");
        prixUnitaire.setStyle("-fx-font-size: 14; -fx-fill: white;");

        // Prix total en évidence
        Text prixTotal = new Text("Total: " + String.format("%.2f", commande.getPrix_total_commande()) + " TND");
        prixTotal.setStyle("-fx-font-weight: bold; -fx-font-size: 16; -fx-fill: #c49b63;");

        // Boutons d'actions
        HBox buttonsContainer = createActionButtons(commande);

        detailsBox.getChildren().addAll(commandeId, nomProduit, typeProduit,
                quantite, prixUnitaire, prixTotal, buttonsContainer);

        card.getChildren().addAll(imageView, detailsBox);
        return card;
    }

    // Modifiez la méthode createActionButtons pour un style plus cohérent
    private HBox createActionButtons(Commande commande) {
        HBox buttonsContainer = new HBox(10);
        buttonsContainer.setStyle("-fx-alignment: center-left; -fx-padding: 10 0 0 0;");

        Button modifyButton = new Button("Modifier");
        modifyButton.setStyle("-fx-background-color: #c49b63; -fx-text-fill: black; -fx-font-weight: bold; -fx-padding: 5 15; -fx-background-radius: 20;");
        modifyButton.setOnAction(event -> openModifyPopup(commande));

        Button deleteButton = new Button("Supprimer");
        deleteButton.setStyle("-fx-background-color: #383838; -fx-text-fill: #c49b63; -fx-font-weight: bold; -fx-padding: 5 15; -fx-background-radius: 20; -fx-border-color: #c49b63; -fx-border-width: 1;");
        deleteButton.setOnAction(event -> openDeleteConfirmation(commande));

        buttonsContainer.getChildren().addAll(modifyButton, deleteButton);
        return buttonsContainer;
    }

    private ImageView createProductImageView(Produit produit) {
        ImageView imageView = new ImageView();
        try {
            Image productImage = loadProductImage(produit);
            imageView.setImage(productImage);
            imageView.setPreserveRatio(true);
            imageView.setFitWidth(200);
            imageView.setFitHeight(120);
        } catch (Exception e) {
            System.err.println("Erreur chargement image: " + e.getMessage());
            try {
                imageView.setImage(new Image(getClass().getResourceAsStream(DEFAULT_IMAGE), 200, 120, true, true));
            } catch (Exception ex) {
                System.err.println("Échec chargement image par défaut: " + ex.getMessage());
            }
        }
        return imageView;
    }



    private void openModifyPopup(Commande commande) {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/ModifierCommande.fxml"));
            Parent root = loader.load();

            ModifierCommande controller = loader.getController();
            controller.setCommande(commande);
            controller.setRefreshCallback(this::loadCommandes);

            Stage stage = new Stage();
            stage.initModality(Modality.APPLICATION_MODAL);
            stage.setScene(new Scene(root));
            stage.setTitle("Modifier Commande #" + commande.getId_commande());
            stage.showAndWait();
        } catch (IOException e) {
            e.printStackTrace();
            showAlert(Alert.AlertType.ERROR, "Erreur", "Impossible d'ouvrir la fenêtre de modification");
        }
    }

    private void openDeleteConfirmation(Commande commande) {
        Alert alert = new Alert(Alert.AlertType.CONFIRMATION);
        alert.setTitle("Confirmation de suppression");
        alert.setHeaderText("Supprimer la commande #" + commande.getId_commande() + "?");
        alert.setContentText("Cette action est irréversible. Confirmer la suppression?");

        Optional<ButtonType> result = alert.showAndWait();
        if (result.isPresent() && result.get() == ButtonType.OK) {
            handleDeleteCommand(commande);
        }
    }

    private void handleDeleteCommand(Commande commande) {
        try {
            commandeService.supprimerCommande(commande.getId_commande());
            loadCommandes();
            showAlert(Alert.AlertType.INFORMATION, "Succès", "Commande supprimée avec succès");
        } catch (SQLException e) {
            e.printStackTrace();
            showAlert(Alert.AlertType.ERROR, "Erreur", "Échec de la suppression: " + e.getMessage());
        }
    }

    private void showAlert(Alert.AlertType type, String title, String message) {
        Alert alert = new Alert(type);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    private Image loadProductImage(Produit produit) throws Exception {
        String imagePath = produit.getImage_produit();

        if (imagePath == null || imagePath.isEmpty()) {
            return loadDefaultImage();
        }

        if (imagePath.startsWith("http://") || imagePath.startsWith("https://")) {
            return new Image(imagePath, 200, 120, true, true);
        }

        if (imagePath.startsWith("file:") || imagePath.startsWith("C:") || imagePath.startsWith("D:")) {
            String cleanPath = imagePath.replace("file:", "").trim();
            File file = new File(cleanPath);
            if (file.exists()) {
                return new Image(file.toURI().toString(), 200, 120, true, true);
            }
            throw new Exception("Fichier image introuvable: " + cleanPath);
        }

        InputStream resourceStream = getClass().getResourceAsStream(
                imagePath.startsWith("/") ? imagePath : "/images/" + imagePath);

        if (resourceStream != null) {
            return new Image(resourceStream, 200, 120, true, true);
        }

        return loadDefaultImage();
    }

    private Image loadDefaultImage() throws Exception {
        InputStream defaultStream = getClass().getResourceAsStream(DEFAULT_IMAGE);
        if (defaultStream != null) {
            return new Image(defaultStream, 200, 120, true, true);
        }
        throw new Exception("Image par défaut non trouvée");
    }

    @FXML
    public void refreshCommandes() {
        loadCommandes();
    }

    @FXML private Label totalLabel;
    @FXML private Button payerButton;

    private double calculateTotal(List<Commande> commandes) {
        return commandes.stream()
                .mapToDouble(Commande::getPrix_total_commande)
                .sum();
    }

    public void loadCommandes() {
        try {
            commandesContainer.getChildren().clear();
            User currentUser = getCurrentUser();

            // Vérification de la connexion
            if (currentUser == null) {
                showErrorText("Veuillez vous connecter pour voir vos commandes");
                return;
            }

            // Récupération des commandes spécifiques à l'utilisateur
            List<Commande> commandes = commandeService.recupererCommandesParUser(currentUser.getId());

            if (commandes.isEmpty()) {
                showInfoText("Vous n'avez aucune commande");
                totalLabel.setText("Total: 0.00 TND");
                payerButton.setDisable(true);
            } else {
                double total = calculateTotal(commandes);
                totalLabel.setText(String.format("Total: %.2f TND", total));
                payerButton.setDisable(false);

                for (Commande commande : commandes) {
                    try {
                        Produit produit = commande.getProduit();
                        // Si le produit n'est pas complet, on le récupère
                        if (produit.getNom_produit() == null) {
                            produit = produitService.recupererProduitParId(produit.getId_produit());
                        }
                        commandesContainer.getChildren().add(createCommandeCard(commande, produit));
                    } catch (SQLException e) {
                        e.printStackTrace();
                        showErrorText("Erreur lors du chargement d'une commande");
                    }
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
            showErrorText("Erreur lors du chargement des commandes");
        }
    }
    @FXML
    private void handlePayment(ActionEvent event) {
        try {
            User currentUser = getCurrentUser();
            if (currentUser == null) {
                showAlert(Alert.AlertType.ERROR, "Erreur", "Vous devez être connecté pour effectuer un paiement");
                return;
            }

            List<Commande> commandes = commandeService.recupererCommandesParUser(currentUser.getId());
            if (commandes.isEmpty()) {
                showAlert(Alert.AlertType.ERROR, "Erreur", "Aucune commande à payer");
                return;
            }

            double total = calculateTotal(commandes);

            // Création de l'intention de paiement
            PaymentService paymentService = new PaymentService();
            String clientSecret = paymentService.createPaymentIntent(
                    total,
                    "eur", // ou "usd" selon votre devise
                    "Paiement pour " + commandes.size() + " commande(s)"



            );

            // Afficher l'interface de paiement Stripe
            showStripePaymentForm(clientSecret, total);

        } catch (SQLException e) {
            showAlert(Alert.AlertType.ERROR, "Erreur", "Erreur base de données: " + e.getMessage());
        } catch (StripeException e) {
            showAlert(Alert.AlertType.ERROR, "Erreur Stripe", "Échec du paiement: " + e.getMessage());
        }
    }

    // Ajoutez cette variable de classe
    private Stage paymentStage;

    private void showStripePaymentForm(String clientSecret, double amount) {
        try {
            if (paymentStage == null) {
                paymentStage = new Stage();
                paymentStage.initModality(Modality.APPLICATION_MODAL);
                paymentStage.setOnCloseRequest(e -> {
                    paymentStage = null;
                    showAlert(Alert.AlertType.WARNING, "Paiement annulé", "Vous avez fermé la fenêtre de paiement");
                });
            }

            WebView webView = new WebView();
            WebEngine engine = webView.getEngine();
            engine.setJavaScriptEnabled(true);

            String stripeHtml = """
<!DOCTYPE html>
<html>
<head>
    <title>Paiement</title>
    <script src="https://js.stripe.com/v3/"></script>
    <style>
        body { font-family: Arial; padding: 20px; }
        #payment-element { margin: 20px 0; }
        button {
            background: #5469d4;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        .success-message {
            color: green;
            font-weight: bold;
            margin-top: 20px;
        }
        .error-message {
            color: red;
            font-weight: bold;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <h1>Paiement de %.2f TND</h1>
    <div id="payment-element"></div>
    <button id="submit">Payer maintenant</button>
    <div id="messages"></div>

    <script>
        const stripe = Stripe('%s');
        const elements = stripe.elements({
            clientSecret: '%s',
            appearance: {theme: 'stripe'}
        });

        const paymentElement = elements.create('payment');
        paymentElement.mount('#payment-element');

        document.getElementById('submit').addEventListener('click', async () => {
            const {error, paymentIntent} = await stripe.confirmPayment({
                elements,
                // Suppression du return_url
                redirect: 'if_required' // Garder pour les cas où une redirection est nécessaire (ex: 3D Secure)
            });

            if (error) {
                document.getElementById('messages').innerHTML =
                    `<p class="error-message">Erreur: ${error.message}</p>`;
                window.java.onPaymentFailed(error.message);
            } else if (paymentIntent && paymentIntent.status === 'succeeded') {
                document.getElementById('messages').innerHTML =
                    `<p class="success-message">Paiement réussi!</p>`;
                window.java.onPaymentSuccess();
            }
        });

        window.java = {
            onPaymentSuccess: function() {
                window.paymentCompleted = true;
                window.paymentStatus = 'succeeded';
            },
            onPaymentFailed: function(error) {
                window.paymentCompleted = true;
                window.paymentStatus = 'failed';
                window.paymentError = error;
            }
        };
    </script>
</body>
</html>
""".formatted(amount, "pk_test_51RJhZfFLKMXwd88yk4r9kM6096s9uMI3GiJ7pthVglvSOcsGD71XtMhhJxQamIhvBKs0T7OxGGcOqFynlgGWdzCm00yhexWWbP", clientSecret);

            engine.loadContent(stripeHtml);

            engine.getLoadWorker().stateProperty().addListener((obs, oldState, newState) -> {
                if (newState == Worker.State.SUCCEEDED) {
                    new Thread(() -> {
                        try {
                            while (paymentStage != null && paymentStage.isShowing()) {
                                Boolean completed = (Boolean) engine.executeScript("window.paymentCompleted");
                                if (completed != null && completed) {
                                    String status = (String) engine.executeScript("window.paymentStatus");

                                    Platform.runLater(() -> { // C'EST ICI QUE VOUS GÉREZ L'UI
                                        paymentStage.close();
                                        paymentStage = null;
                                        if ("succeeded".equals(status)) {
                                            showPaymentConfirmationPopup("Paiement réussi!", "Votre paiement a été effectué avec succès.");
                                            loadCommandes();
                                        } else if ("failed".equals(status)) {
                                            String error = (String) engine.executeScript("window.paymentError");
                                            showPaymentErrorPopup("Échec du paiement", "Une erreur s'est produite lors du paiement: " + (error != null ? error : "inconnue."));
                                        }
                                    });
                                    break;
                                }
                                Thread.sleep(1000);
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }).start();
                }
            });

            Scene scene = new Scene(webView, 600, 600);
            paymentStage.setScene(scene);
            paymentStage.setTitle("Paiement sécurisé");
            paymentStage.show();
        } catch (Exception e) {
            e.printStackTrace();
            showAlert(Alert.AlertType.ERROR, "Erreur", "Impossible d'initialiser le paiement: " + e.getMessage());
        }
    }

    private void showPaymentConfirmationPopup(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);

        DialogPane dialogPane = alert.getDialogPane();
        dialogPane.getStylesheets().add(
                getClass().getResource("/styles.css").toExternalForm()); // Si vous avez une feuille de style

        alert.showAndWait();
    }

    private void showPaymentErrorPopup(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.ERROR);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);

        DialogPane dialogPane = alert.getDialogPane();
        dialogPane.getStylesheets().add(
                getClass().getResource("/styles.css").toExternalForm()); // Si vous avez une feuille de style

        alert.showAndWait();
    }
}