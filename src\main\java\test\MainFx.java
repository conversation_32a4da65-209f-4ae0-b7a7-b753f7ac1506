package test;

import Controllers.AfficherReservationController;
import Controllers.AjouterReservationController;
import javafx.application.Application;
import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.stage.Stage;


import java.io.IOException;

import static javafx.application.Application.launch;

public class MainFx extends Application {

 public static void main(String[] args) {
        launch(args);
    }

    @Override
    public void start(Stage primaryStage) {
        FXMLLoader fxmlLoader = new FXMLLoader(getClass().getResource("/Login.fxml"));

        try {
            Parent root = fxmlLoader.load();
            Scene scene = new Scene(root);
            primaryStage.setScene(scene);
            primaryStage.setTitle("Ajouter evenement");

            primaryStage.show();
        } catch (IOException e) {
            System.out.println(e.getMessage());
        }


    }
}
/*
 private static final int TEST_USER_ID = 1; // Use valid ID

    public static void main(String[] args) {
        launch(args);
    }

    @Override
    public void start(Stage primaryStage) {
        try {
            System.out.println("Loading FXML...");
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/AfficherReservationController.fxml"));
            Parent root = loader.load();

            System.out.println("Getting controller...");
            AfficherReservationController controller = loader.getController();

            System.out.println("Setting user ID...");
            controller.setUserId(TEST_USER_ID); // This will trigger the load

            primaryStage.setScene(new Scene(root));
            primaryStage.setTitle("Mes Réservations");
            primaryStage.show();

        } catch (IOException e) {
            System.err.println("FXML Error: " + e.getMessage());
            e.printStackTrace();
        }
    }
}*/

/*public class MainFx extends Application {

   // TEMPORARY - Replace with actual logged-in user ID
    private static final int TEST_USER_ID = 1; // Use an ID that exists in your database

    public static void main(String[] args) {
        launch(args);
    }

    @Override
    public void start(Stage primaryStage) {
        try {
            FXMLLoader fxmlLoader = new FXMLLoader(getClass().getResource("/AfficherReservation.fxml"));
            Parent root = fxmlLoader.load();

            // GET THE CONTROLLER AND SET THE USER ID
            AfficherReservationController controller = fxmlLoader.getController();
            //controller.setCurrentUser(TEST_USER_ID); Ajouter
            controller.setUserId(TEST_USER_ID);
            System.out.println("Setting user ID to: " + TEST_USER_ID);

            Scene scene = new Scene(root);
            primaryStage.setScene(scene);
            primaryStage.setTitle("Ajouter Reservation");
            primaryStage.show();

        } catch (IOException e) {
            System.out.println("Error loading FXML: " + e.getMessage());
            e.printStackTrace();
        }
    }
}*/











