-- Create user table if not exists
CREATE TABLE IF NOT EXISTS `user` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `username` VARCHAR(50) NOT NULL,
    `email` VARCHAR(100) NOT NULL,
    `password` VARCHAR(255) NOT NULL
);

-- Create post table if not exists
CREATE TABLE IF NOT EXISTS `post` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `user_id` INT NOT NULL,
    `description_post` TEXT NOT NULL,
    `date_post` DATE NOT NULL,
    `nbr_likes` INT DEFAULT 0,
    `image` VARCHAR(255),
    `is_validated` BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (`user_id`) REFERENCES `user`(`id`)
);

-- Create commentaire table if not exists
CREATE TABLE IF NOT EXISTS `commentaire` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `post_id` INT NOT NULL,
    `user_id` INT NOT NULL,
    `contenu` TEXT NOT NULL,
    `date_commentaire` VARCHAR(50) NOT NULL,
    `nbr_like_commentaire` INT DEFAULT 0,
    FOREIGN KEY (`post_id`) REFERENCES `post`(`id`),
    FOREIGN KEY (`user_id`) REFERENCES `user`(`id`)
);

-- Insert test user if not exists
INSERT INTO `user` (`id`, `username`, `email`, `password`) 
VALUES (1, 'test_user', '<EMAIL>', 'password123')
ON DUPLICATE KEY UPDATE `id` = `id`; 