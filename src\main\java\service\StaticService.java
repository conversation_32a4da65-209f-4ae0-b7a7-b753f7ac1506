package service;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;
import tools.MyDataBase;

public class StaticService {
    private Connection cnx;

    public StaticService() {
        cnx = MyDataBase.getInstance().getCnx();
    }

    public Map<String, Integer> getProductCountByType() throws SQLException {
        Map<String, Integer> stats = new HashMap<>();
        String sql = "SELECT type_produit, COUNT(*) as count FROM Produit GROUP BY type_produit";

        try (PreparedStatement ste = cnx.prepareStatement(sql);
             ResultSet rs = ste.executeQuery()) {

            while (rs.next()) {
                stats.put(rs.getString("type_produit"), rs.getInt("count"));
            }
        }
        return stats;
    }

    public Map<String, Double> getAveragePriceByType() throws SQLException {
        Map<String, Double> stats = new HashMap<>();
        String sql = "SELECT type_produit, AVG(prix_produit) as avg_price FROM Produit GROUP BY type_produit";

        try (PreparedStatement ste = cnx.prepareStatement(sql);
             ResultSet rs = ste.executeQuery()) {

            while (rs.next()) {
                stats.put(rs.getString("type_produit"), rs.getDouble("avg_price"));
            }
        }
        return stats;
    }

    public Map<String, Integer> getTotalStockByType() throws SQLException {
        Map<String, Integer> stats = new HashMap<>();
        String sql = "SELECT type_produit, SUM(stock_produit) as total_stock FROM Produit GROUP BY type_produit";

        try (PreparedStatement ste = cnx.prepareStatement(sql);
             ResultSet rs = ste.executeQuery()) {

            while (rs.next()) {
                stats.put(rs.getString("type_produit"), rs.getInt("total_stock"));
            }
        }
        return stats;
    }

    public Map<String, Double> getPriceRangeByType() throws SQLException {
        Map<String, Double> stats = new HashMap<>();
        String sql = "SELECT type_produit, MAX(prix_produit) - MIN(prix_produit) as price_range FROM Produit GROUP BY type_produit";

        try (PreparedStatement ste = cnx.prepareStatement(sql);
             ResultSet rs = ste.executeQuery()) {

            while (rs.next()) {
                stats.put(rs.getString("type_produit"), rs.getDouble("price_range"));
            }
        }
        return stats;
    }
}