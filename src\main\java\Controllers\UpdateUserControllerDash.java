package Controllers;

import javafx.event.ActionEvent;
import javafx.fxml.FXML;
import javafx.scene.control.ComboBox;
import javafx.scene.control.TextField;
import javafx.stage.FileChooser;
import javafx.stage.Stage;
import entity.User;
import service.UserService;

import java.io.File;
import java.sql.SQLException;

public class UpdateUserControllerDash {
    @FXML private TextField nameField;
    @FXML private TextField lastnameField;
    @FXML private TextField emailField;
    @FXML private ComboBox<String> rolesComboBox;
    @FXML private ComboBox<String> bannedComboBox;

    private User userToUpdate;

    private Controllers.AfficherUser afficherUserController;

    public void setAfficherUserController(Controllers.AfficherUser controller) {
        this.afficherUserController = controller;
    }

    public void setUserToUpdateDash(User user) {
        this.userToUpdate = user;

        // Fill fields
        nameField.setText(user.getNom_user());
        lastnameField.setText(user.getPrenom_user());
        emailField.setText(user.getEmail_user());

        // Set the role in combo box based on user's current role
        String role = user.getRole_user();
        if (role.contains("ADMIN")) {
            rolesComboBox.setValue("admin");
        } else if (role.contains("CLIENT")) {
            rolesComboBox.setValue("client");
        }
    }

    @FXML
    private void initialize() {
        rolesComboBox.getItems().addAll("admin", "client");
        bannedComboBox.getItems().addAll("Yes", "No");
    }

    @FXML
    private void handleUpdateDash(ActionEvent event) {
        userToUpdate.setNom_user(nameField.getText());
        userToUpdate.setPrenom_user(lastnameField.getText());
        userToUpdate.setEmail_user(emailField.getText());

        // Get selected role
        String selectedRole = rolesComboBox.getValue();
        if ("admin".equals(selectedRole)) {
            userToUpdate.setRole_user("[\"ROLE_ADMIN\"]");
        } else if ("client".equals(selectedRole)) {
            userToUpdate.setRole_user("[\"ROLE_CLIENT\"]");
        }

        try {
            // Update user in the database
            UserService us = new UserService();
            us.modifier(userToUpdate);

            // Refresh user list if necessary
            if (afficherUserController != null) {
                afficherUserController.refreshGrid();
            }

            // Close the current window
            ((Stage) nameField.getScene().getWindow()).close();
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    @FXML
    private void handleChoosePhoto(ActionEvent event) {
        FileChooser fileChooser = new FileChooser();
        fileChooser.setTitle("Choose Photo");
        File file = fileChooser.showOpenDialog(null);

        // If a photo is selected, update the user's photo
        if (file != null) {
            userToUpdate.setPhoto_user(file.getAbsolutePath()); // Update photo path
        }
    }
}
