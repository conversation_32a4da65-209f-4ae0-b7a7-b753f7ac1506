package service;

import entity.Evenement;
import entity.Reservation;
import entity.User;
import tools.MyDataBase;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

public class ReservationService implements IReservation<Reservation> {

    Connection cnx;
    String sql;
    private Statement st;
    private PreparedStatement ste;

    public ReservationService() {
        cnx = MyDataBase.getInstance().getCnx();
    }

    @Override
    public void ajouterReservation(Reservation reservation) throws SQLException {
        String sql = "INSERT INTO reservation (nbr_places, evenement_id, user_id, statut_booking, moyen_payement_booking, date_booking) VALUES (?, ?, ?, ?, ?, ?)";
        PreparedStatement ste = cnx.prepareStatement(sql);

        ste.setInt(1, reservation.getNbr_places());
        ste.setInt(2, reservation.getEvenement().getId());
        ste.setInt(3, reservation.getUser().getId());
        ste.setString(4, reservation.getStatut_booking());
        ste.setString(5, reservation.getMoyen_payement_booking());
        ste.setDate(6, reservation.getDate_booking());
        ste.executeUpdate();
    }


    @Override
    public void supprimerReservation(int id) throws SQLException {
        String sql = "DELETE FROM reservation WHERE id=?";
        ste = cnx.prepareStatement(sql);
        ste.setInt(1, id);
        ste.executeUpdate();
    }


   /* @Override
    public void modifierReservation(Reservation reservation) throws SQLException {
        String sql = "UPDATE reservation SET nbr_places=?, evenement_id=?, user_id=?, statut_booking=?, " +
                "moyen_payement_booking=?, date_booking=? WHERE id=?";

        try (PreparedStatement ste = cnx.prepareStatement(sql)) {
            ste.setInt(1, reservation.getNbr_places());
            ste.setInt(2, reservation.getEvenement().getId());
            ste.setInt(3, reservation.getUser().getId());
            ste.setString(4, reservation.getStatut_booking());
            ste.setString(5, reservation.getMoyen_payement_booking());
            ste.setDate(6, reservation.getDate_booking());
            ste.setInt(7, reservation.getId());

            ste.executeUpdate();
        }
    }*/

    public void modifierReservation(Reservation reservation) throws SQLException {
        // First verify the event exists
        String checkEventSql = "SELECT COUNT(*) FROM evenement WHERE id = ?";
        try (PreparedStatement checkPs = cnx.prepareStatement(checkEventSql)) {
            checkPs.setInt(1, reservation.getEvenement().getId());
            ResultSet rs = checkPs.executeQuery();
            if (rs.next() && rs.getInt(1) == 0) {
                throw new SQLException("Event with ID " + reservation.getEvenement().getId() + " does not exist");
            }
        }

        // Then proceed with update
        String sql = "UPDATE reservation SET nbr_places=?, evenement_id=?, statut_booking=?, " +
                "moyen_payement_booking=?, date_booking=? WHERE id=?";

        try (PreparedStatement ste = cnx.prepareStatement(sql)) {
            ste.setInt(1, reservation.getNbr_places());
            ste.setInt(2, reservation.getEvenement().getId());
            ste.setString(3, reservation.getStatut_booking());
            ste.setString(4, reservation.getMoyen_payement_booking());
            ste.setDate(5, reservation.getDate_booking());
            ste.setInt(6, reservation.getId());

            int rowsAffected = ste.executeUpdate();
            if (rowsAffected == 0) {
                throw new SQLException("Updating reservation failed, no rows affected");
            }
        }
    }



    @Override
    public List<Reservation> recupererReservation() throws SQLException {
        List<Reservation> reservations = new ArrayList<>();

        String sql = "SELECT r.id, r.nbr_places, r.statut_booking, r.moyen_payement_booking, r.date_booking, " +
                "e.id AS evenement_id, e.titre_evenement, e.image_event, e.type_event, e.description_event, e.date_event, e.capacite_max, e.prix_event, " +
                "u.id AS user_id, u.nom_user, u.prenom_user, u.email_user, u.telephone_user, u.adresse, u.role_user, u.photo_user, u.date_naissance_user " +
                "FROM reservation r " +
                "JOIN evenement e ON r.evenement_id = e.id " +
                "JOIN user u ON r.user_id = u.id";

        try (Statement statement = cnx.createStatement();
             ResultSet rs = statement.executeQuery(sql)) {

            while (rs.next()) {
                // Créer l'objet Evenement
                Evenement evenement = new Evenement();
                evenement.setId(rs.getInt("evenement_id"));
                evenement.setTitre_evenement(rs.getString("titre_evenement"));
                evenement.setImage_event(rs.getString("image_event"));
                evenement.setType_event(rs.getString("type_event"));
                evenement.setDescription_event(rs.getString("description_event"));
                evenement.setDate_event(rs.getDate("date_event"));
                evenement.setCapacite_max(rs.getInt("capacite_max"));
                evenement.setPrix_event(rs.getDouble("prix_event"));

                // Créer l'objet User
                User user = new User();
                user.setId(rs.getInt("user_id"));
                user.setNom_user(rs.getString("nom_user"));
                user.setPrenom_user(rs.getString("prenom_user"));
                user.setEmail_user(rs.getString("email_user"));
                user.setTelephone_user(rs.getInt("telephone_user"));
                user.setAdresse(rs.getString("adresse"));
                user.setRole_user(rs.getString("role_user"));
                user.setPhoto_user(rs.getString("photo_user"));
                user.setDate_naissance_user(rs.getDate("date_naissance_user"));

                // Créer l'objet Reservation
                Reservation reservation = new Reservation(
                        rs.getInt("id"),
                        rs.getInt("nbr_places"),
                        evenement,
                        user,
                        rs.getString("statut_booking"),
                        rs.getString("moyen_payement_booking"),
                        rs.getDate("date_booking")
                );

                reservations.add(reservation);
            }
        } catch (SQLException e) {
            e.printStackTrace();
            throw new SQLException("Erreur lors de la récupération des réservations", e);
        }

        return reservations;
    }

  /*  public List<Reservation> getReservationsByUser(int userId) throws SQLException {
        String sql = "SELECT r.*, e.*, u.nom_user, u.prenom_user " +
                "FROM reservation r " +
                "JOIN evenement e ON r.evenement_id = e.id " +
                "JOIN user u ON r.user_id = u.id " +
                "WHERE r.user_id = ? " +
                "ORDER BY r.date_booking DESC";

        List<Reservation> reservations = new ArrayList<>();
        try (PreparedStatement ps = cnx.prepareStatement(sql)) {
            ps.setInt(1, userId);
            ResultSet rs = ps.executeQuery();

            while (rs.next()) {
                Reservation reservation = new Reservation();
                // Set reservation fields
                reservation.setId(rs.getInt("id"));
                reservation.setDate_booking(rs.getDate("date_booking"));
                reservation.setNbr_places(rs.getInt("nbr_places"));
                reservation.setMoyen_payement_booking(rs.getString("moyen_payement_booking"));
                reservation.setStatut_booking(rs.getString("statut_booking"));

                // Set event
                Evenement event = new Evenement();
                event.setId(rs.getInt("evenement_id"));
                event.setTitre_evenement(rs.getString("titre_evenement"));
                event.setDate_event(rs.getDate("date_event"));
                event.setImage_event(rs.getString("image_event"));
                event.setPrix_event(rs.getDouble("prix_event"));
                reservation.setEvenement(event);

                // Set user
                User user = new User();
                user.setNom_user(rs.getString("nom_user"));
                user.setPrenom_user(rs.getString("prenom_user"));
                reservation.setUser(user);

                reservations.add(reservation);
            }
        }
        return reservations;
    }
*/

    public Reservation getReservationById(int id) throws SQLException {
        String query = "SELECT r.*, e.id AS event_id, e.titre_evenement, e.image_event, " +
                "e.type_event, e.description_event, e.date_event, e.prix_event, e.capacite_max " +
                "FROM reservation r JOIN evenement e ON r.evenement_id = e.id WHERE r.id = ?";

        try (PreparedStatement stmt = cnx.prepareStatement(query)) {
            stmt.setInt(1, id);
            ResultSet rs = stmt.executeQuery();

            if (rs.next()) {
                Reservation res = new Reservation();
                res.setId(rs.getInt("id"));
                res.setDate_booking(rs.getDate("date_booking"));
                res.setNbr_places(rs.getInt("nbr_places"));
                res.setMoyen_payement_booking(rs.getString("moyen_payement_booking"));
                res.setStatut_booking(rs.getString("statut_booking"));

                // Load complete event data
                Evenement event = new Evenement();
                event.setId(rs.getInt("event_id"));
                event.setTitre_evenement(rs.getString("titre_evenement"));
                event.setImage_event(rs.getString("image_event"));
                event.setType_event(rs.getString("type_event"));
                event.setDescription_event(rs.getString("description_event"));
                event.setDate_event(rs.getDate("date_event"));
                event.setPrix_event(rs.getDouble("prix_event"));
                event.setCapacite_max(rs.getInt("capacite_max"));

                res.setEvenement(event);
                return res;
            }
        }
        return null;
    }
    public List<Reservation> getReservationsByUser(int userId) throws SQLException {
        String query = "SELECT r.id AS reservation_id, r.date_booking, r.nbr_places, " +
                "r.moyen_payement_booking, r.statut_booking, " +
                "e.id AS event_id, e.titre_evenement, e.image_event, " +
                "e.type_event, e.description_event, e.date_event, e.prix_event, e.capacite_max " +
                "FROM reservation r " +
                "JOIN evenement e ON r.evenement_id = e.id " +
                "JOIN user u ON r.user_id = u.id " +
                "WHERE r.user_id = ? " +
                "ORDER BY r.date_booking DESC";

        List<Reservation> reservations = new ArrayList<>();

        try (PreparedStatement stmt = cnx.prepareStatement(query)) {
            stmt.setInt(1, userId);
            ResultSet rs = stmt.executeQuery();

            while (rs.next()) {
                Reservation res = new Reservation();
                // Load ALL reservation fields
                res.setId(rs.getInt("reservation_id"));
                res.setDate_booking(rs.getDate("date_booking"));
                res.setNbr_places(rs.getInt("nbr_places"));
                res.setMoyen_payement_booking(rs.getString("moyen_payement_booking"));
                res.setStatut_booking(rs.getString("statut_booking"));

                // Load COMPLETE event data
                Evenement event = new Evenement();
                event.setId(rs.getInt("event_id"));
                event.setTitre_evenement(rs.getString("titre_evenement"));
                event.setImage_event(rs.getString("image_event"));
                event.setType_event(rs.getString("type_event"));
                event.setDescription_event(rs.getString("description_event"));
                event.setDate_event(rs.getDate("date_event"));
                event.setPrix_event(rs.getDouble("prix_event"));
                event.setCapacite_max(rs.getInt("capacite_max"));

                res.setEvenement(event);
                reservations.add(res);
            }
        }
        return reservations;
    }

    public void updateReservationStatus(int reservationId, String status) throws SQLException {
        String sql = "UPDATE reservation SET statut_booking = ? WHERE id = ?";
        try (PreparedStatement ps = cnx.prepareStatement(sql)) {
            ps.setString(1, status);
            ps.setInt(2, reservationId);
            ps.executeUpdate();
        }
    }
    public void deleteReservation(int reservationId) throws SQLException {
        String sql = "DELETE FROM reservation WHERE id = ?";
        try (PreparedStatement ps = cnx.prepareStatement(sql)) {
            ps.setInt(1, reservationId);
            ps.executeUpdate();
        }
    }

    public int getReservedPlacesForEvent(int eventId) throws SQLException {
        String sql = "SELECT COALESCE(SUM(nbr_places), 0) AS total_reserved FROM reservation WHERE evenement_id = ? AND statut_booking != 'Annulée'";
        try (PreparedStatement ps = cnx.prepareStatement(sql)) {
            ps.setInt(1, eventId);
            ResultSet rs = ps.executeQuery();
            if (rs.next()) {
                return rs.getInt("total_reserved");
            }
        }
        return 0;
    }

    public List<Reservation> searchReservations(String searchTerm) throws SQLException {
        String query = "SELECT r.*, e.*, u.* FROM reservation r " +
                "JOIN evenement e ON r.evenement_id = e.id " +
                "JOIN user u ON r.user_id = u.id " +
                "WHERE e.titre_evenement LIKE ? OR " +
                "u.nom_user LIKE ? OR " +
                "u.prenom_user LIKE ? OR " +
                "r.moyen_payement_booking LIKE ? " +
                "ORDER BY r.date_booking DESC";

        List<Reservation> reservations = new ArrayList<>();

        try (PreparedStatement stmt = cnx.prepareStatement(query)) {
            String likeTerm = "%" + searchTerm + "%";
            stmt.setString(1, likeTerm);
            stmt.setString(2, likeTerm);
            stmt.setString(3, likeTerm);
            stmt.setString(4, likeTerm);

            ResultSet rs = stmt.executeQuery();
            while (rs.next()) {
                Reservation reservation = new Reservation();
                // Map your reservation fields from ResultSet
                reservation.setId(rs.getInt("id"));
                // ... set other fields ...

                reservations.add(reservation);
            }
        }
        return reservations;
    }





}
