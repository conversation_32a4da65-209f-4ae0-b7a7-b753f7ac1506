package Controllers;

import entity.Evenement;
import entity.User;
import entity.UserSession;
import javafx.event.ActionEvent;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.Node;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.effect.DropShadow;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.scene.layout.AnchorPane;
import javafx.scene.layout.HBox;
import javafx.scene.layout.VBox;
import javafx.scene.paint.Color;
import javafx.scene.text.Font;
import javafx.scene.text.Text;
import javafx.stage.Modality;
import javafx.stage.Stage;
import service.EvenementService;

import java.io.IOException;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Optional;

public class AfficherEvenementUserController {

    @FXML
    private AnchorPane scrollPane;
    @FXML
    private Button btnAjout;
    @FXML
    private Button btnSearch;
    @FXML
    private Button btnReservations;
    @FXML
    private VBox eventsContainer;
    private EvenementService evenementService = new EvenementService();
    private SimpleDateFormat dateFormat = new SimpleDateFormat("dd MMMM yyyy");
    @FXML
    private Button connexionButton;
    private Evenement event;

    @FXML
    public void initialize() {
        checkUserSession(); // Vérifie l'état de connexion
        loadEvents();
    }

    // Méthode pour vérifier la session utilisateur
    private void checkUserSession() {
        if (connexionButton == null) return;

        User currentUser = UserSession.getInstance().getUser();
        if (currentUser != null) {
            connexionButton.setText("Profil");
            connexionButton.setOnAction(this::navigateToProfile);
        } else {
            connexionButton.setText("Connexion");
            connexionButton.setOnAction(this::navigateToConnexion);
        }
    }

    // Navigation vers le profil
    @FXML
    private void navigateToProfile(ActionEvent event) {
        loadPage("/ProfilUser.fxml", event);
    }
    @FXML
    private void consulterReservations(ActionEvent event) {
        try {
            // Load the FXML file for displaying reservations
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/AfficherReservation.fxml"));
            Parent root = loader.load();

            // Get the current stage
            Stage stage = (Stage)((Node)event.getSource()).getScene().getWindow();

            // Set the new scene
            stage.setScene(new Scene(root));
            stage.show();
        } catch (IOException e) {
            showAlert("Erreur", "Impossible d'ouvrir la page des réservations: " + e.getMessage(), Alert.AlertType.ERROR);
            e.printStackTrace();
        }
    }

    private void loadEvents() {
        try {
            List<Evenement> events = evenementService.recupererEvenement();
            displayEvents(events);
        } catch (SQLException e) {
            showAlert("Erreur de base de données", "Échec du chargement des événements: " + e.getMessage(), Alert.AlertType.ERROR);
            e.printStackTrace();
        }
    }
    private void displayEvents(List<Evenement> events) {
        eventsContainer.getChildren().clear();
        if (events.isEmpty()) {
            Label noEventsLabel = new Label("Aucun événement disponible");
            noEventsLabel.setStyle("-fx-font-size: 16px; -fx-text-fill: #666;");
            eventsContainer.getChildren().add(noEventsLabel);
            return;
        }
        for (Evenement event : events) {
            VBox eventCard = createEventCard(event);
            eventsContainer.getChildren().add(eventCard);

        }

    }


    private VBox createEventCard(Evenement event) {
        // Main container using HBox for side-by-side layout
        HBox card = new HBox(20);
        card.setStyle("-fx-background-color: #282828; -fx-border-color: #c49b63; -fx-border-width: 1; -fx-border-radius: 0; -fx-background-radius: 0;");
        card.setPadding(new Insets(20));
        card.setAlignment(Pos.TOP_LEFT);

        // Remove the shadow effect to prevent visual separation
        card.setEffect(null);

        // Left side - Image
        VBox imageContainer = new VBox();
        imageContainer.setAlignment(Pos.TOP_LEFT);
        imageContainer.setPrefWidth(250);
        imageContainer.setStyle("-fx-background-color: transparent;");

        if (event.getImage_event() != null && !event.getImage_event().isEmpty()) {
            try {
                ImageView imageView = new ImageView(new Image(event.getImage_event()));
                imageView.setFitWidth(250);
                imageView.setFitHeight(180);
                imageView.setPreserveRatio(true);
                imageContainer.getChildren().add(imageView);
            } catch (Exception e) {
                Label imageError = new Label("Image non disponible");
                imageError.setStyle("-fx-text-fill: white;");
                imageContainer.getChildren().add(imageError);
            }
        }

        // Right side - Text content
        VBox contentContainer = new VBox(12);
        contentContainer.setAlignment(Pos.TOP_LEFT);
        contentContainer.setPrefWidth(550);
        contentContainer.setStyle("-fx-background-color: transparent;");

        // Event title
        Label titleLabel = new Label(event.getTitre_evenement());
        titleLabel.setFont(Font.font("Segoe UI Semibold", 20));
        titleLabel.setStyle("-fx-font-weight: bold; -fx-text-fill: #c49b63;");
        titleLabel.setWrapText(true);

        // Event description
        Label descriptionLabel = new Label(event.getDescription_event());
        descriptionLabel.setFont(Font.font("Segoe UI", 14));
        descriptionLabel.setStyle("-fx-text-fill: #e0e0e0;");
        descriptionLabel.setWrapText(true);
        descriptionLabel.setMaxWidth(550);

        // Event date
        Label dateLabel = new Label();
        styleLabel(dateLabel, "📅 Date: ", dateFormat.format(event.getDate_event()));

        // Reserve button
        Button reserveButton = createButton("Réserver maintenant", "#c49b63", e -> openEditEventForm(event));
        reserveButton.setStyle("-fx-background-color: #c49b63; -fx-text-fill: #f4f4f4; -fx-font-weight: bold; -fx-font-size: 14px; -fx-padding: 8 20; -fx-background-radius: 20;");

        // Add all content elements
        contentContainer.getChildren().addAll(
                titleLabel,
                descriptionLabel,
                dateLabel,
                reserveButton
        );

        // Combine image and content
        card.getChildren().addAll(
                imageContainer,
                contentContainer
        );

        // Create separator between events
        Separator separator = new Separator();
        separator.setStyle("-fx-background-color: #333333;");

        // Wrap in VBox with separator
        VBox wrapper = new VBox(card, separator);
        wrapper.setStyle("-fx-background-color: transparent;");

        return wrapper;
    }

    private void styleLabel(Label label, String prefix, String value) {
        Text prefixText = new Text(prefix);
        prefixText.setStyle("-fx-fill: #c49b63; -fx-font-weight: bold; -fx-font-size: 14px;");
        Text valueText = new Text(value);
        valueText.setStyle("-fx-fill: #ffffff; -fx-font-size: 14px;");

        HBox container = new HBox(prefixText, valueText);
        container.setSpacing(5);

        label.setGraphic(container);
        label.setContentDisplay(ContentDisplay.GRAPHIC_ONLY);
    }


    private Button createButton(String text, String color, javafx.event.EventHandler<ActionEvent> handler) {

        Button button = new Button(text);

        button.setStyle(String.format(

                "-fx-background-color: %s; -fx-text-fill: white; -fx-font-weight: bold; -fx-padding: 5 10;",

                color
        ));
        button.setOnAction(handler);

        return button;

    }


    private void openEditEventForm(Evenement event) {

        try {

            // Load the FXML file for adding events
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/AjouterReservation.fxml"));
            Parent root = loader.load();
// Create a new stage for the add event window
            Stage stage = new Stage();
            stage.setTitle("Ajouter un Événement");
            stage.setScene(new Scene(root));
// Set the owner to prevent clicking behind the modal
            //Stage currentStage = (Stage)((Node)event.getSource()).getScene().getWindow();
            //stage.initOwner(currentStage);
// Optional: Make it modal
            stage.initModality(Modality.WINDOW_MODAL);
// Refresh the event list when the add window closes
            stage.setOnHidden(e -> loadEvents());
             stage.show();

        } catch (IOException e) {
            showAlert("Erreur", "Impossible d'ouvrir le formulaire d'ajout: " + e.getMessage(), Alert.AlertType.ERROR);
            e.printStackTrace();
        }

    }

    @FXML

    void Ajouter(ActionEvent event) {

        try {

// Load the FXML file for adding events

            FXMLLoader loader = new FXMLLoader(getClass().getResource("/Crud.FXML.fxml"));

            Parent root = loader.load();



// Create a new stage for the add event window

            Stage stage = new Stage();

            stage.setTitle("Ajouter un Événement");

            stage.setScene(new Scene(root));



// Set the owner to prevent clicking behind the modal

            Stage currentStage = (Stage)((Node)event.getSource()).getScene().getWindow();

            stage.initOwner(currentStage);



// Optional: Make it modal

            stage.initModality(Modality.WINDOW_MODAL);



// Refresh the event list when the add window closes

            stage.setOnHidden(e -> loadEvents());



            stage.show();



        } catch (IOException e) {

            showAlert("Erreur", "Impossible d'ouvrir le formulaire d'ajout: " + e.getMessage(), Alert.AlertType.ERROR);

            e.printStackTrace();

        }

    }



    private void showAlert(String title, String message, Alert.AlertType type) {

        Alert alert = new Alert(type);

        alert.setTitle(title);

        alert.setHeaderText(null);

        alert.setContentText(message);

        alert.showAndWait();

    }

    // Navigation methods
    @FXML
    private void navigateToAccueil(ActionEvent event) { loadPage("/AcceuilFront.fxml", event); }
    @FXML
    private void navigateToRencontres(ActionEvent event) { loadPage("/Rencontres.fxml", event); }
    @FXML
    private void navigateToEvenements(ActionEvent event) { loadPage("/AfficherEvenementUser.fxml", event); }
    @FXML
    private void navigateToForum(ActionEvent event) { loadPage("/AfficherPost.fxml", event); }
    @FXML
    private void navigateToBoutique(ActionEvent event) { loadPage("/AfficherProduit.fxml", event); }
    @FXML
    private void navigateToConnexion(ActionEvent event) { loadPage("/AjouterUser.fxml", event); }

    private void loadPage(String fxmlPath, ActionEvent event) {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource(fxmlPath));
            Parent root = loader.load();

            // Highlight the current page button in the new scene
            if (loader.getController() instanceof AfficherEvenementUserController) {
                // This is the events page, no need to do anything special
            }

            Stage stage = (Stage)((Node)event.getSource()).getScene().getWindow();
            stage.setScene(new Scene(root));
            stage.show();
        } catch (IOException e) {
            showAlert("Erreur de navigation", "Impossible de charger la page: " + e.getMessage(), Alert.AlertType.ERROR);
            e.printStackTrace();
        }
    }


}