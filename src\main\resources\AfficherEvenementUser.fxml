<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.image.Image?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.paint.Color?>
<?import javafx.scene.text.*?>

 <AnchorPane prefHeight="600.0" prefWidth="900.0" style="-fx-background-color: linear-gradient(to bottom, #2a2a2a, #1e1e1e);" xmlns="http://javafx.com/javafx/23.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="Controllers.AfficherEvenementUserController">    <children>
        <!-- Top Navigation Bar -->
        <HBox alignment="CENTER_LEFT" prefHeight="70.0" prefWidth="900.0" style="-fx-background-color: #000000; -fx-padding: 0 30 0 30;">
            <children>
                <ImageView fx:id="logoImageView" fitHeight="67.0" fitWidth="195.0" pickOnBounds="true" preserveRatio="true">
                    <image>
                        <Image url="@logofront.png" />
                    </image>
                </ImageView>

                <Pane HBox.hgrow="ALWAYS" />

                <Button text="Accueil" onAction="#navigateToAccueil" style="-fx-background-color: transparent; -fx-text-fill: #ffffff; -fx-font-size: 14px; -fx-cursor: hand;">
                    <font>
                        <Font name="Segoe UI" size="14.0" />
                    </font>
                </Button>
                <Button text="Rencontres" onAction="#navigateToRencontres" style="-fx-background-color: transparent; -fx-text-fill: #ffffff; -fx-font-size: 14px; -fx-cursor: hand;">
                    <font>
                        <Font name="Segoe UI" size="14.0" />
                    </font>
                </Button>
                <Button text="Événements" onAction="#navigateToEvenements" style="-fx-background-color: transparent; -fx-text-fill: #c49b63; -fx-font-size: 14px; -fx-cursor: hand; -fx-font-weight: bold;">
                    <font>
                        <Font name="Segoe UI" size="14.0" />
                    </font>
                </Button>
                <Button text="Forum" onAction="#navigateToForum" style="-fx-background-color: transparent; -fx-text-fill: #ffffff; -fx-font-size: 14px; -fx-cursor: hand;">
                    <font>
                        <Font name="Segoe UI" size="14.0" />
                    </font>
                </Button>
                <Button text="Boutique" onAction="#navigateToBoutique" style="-fx-background-color: transparent; -fx-text-fill: #ffffff; -fx-font-size: 14px; -fx-cursor: hand;">
                    <font>
                        <Font name="Segoe UI" size="14.0" />
                    </font>
                </Button>
                <Button fx:id="connexionButton" onAction="#navigateToConnexion"
                        style="-fx-background-color: #c49b63; -fx-text-fill: #000000; -fx-font-size: 14px; -fx-cursor: hand; -fx-padding: 5 15; -fx-background-radius: 20;"
                        text="Connexion">
                    <font>
                        <Font name="Segoe UI Bold" size="14.0" />
                    </font>
                </Button>
            </children>
        </HBox>

        <!-- Main Content -->
        <VBox layoutY="70.0" prefHeight="530.0" prefWidth="900.0" style="-fx-background-color: transparent;">
            <children>
                <!-- Page Title -->
                <HBox alignment="CENTER_LEFT" spacing="20" style="-fx-padding: 20 20 10 20;">
                    <children>
                        <Label text="Nos Événements" style="-fx-text-fill: #c49b63; -fx-font-size: 32px; -fx-font-weight: bold;">
                            <font>
                                <Font name="Segoe UI Light" size="32.0" />
                            </font>
                        </Label>
                        <Pane HBox.hgrow="ALWAYS" />
                        <Button fx:id="btnReservations" text="Consulter vos réservations" onAction="#consulterReservations" style="-fx-background-color: #c49b63; -fx-text-fill: #000000; -fx-font-size: 14px; -fx-cursor: hand; -fx-padding: 5 15; -fx-background-radius: 20;">
                            <font>
                                <Font name="Segoe UI Bold" size="14.0" />
                            </font>
                        </Button>
                    </children>
                </HBox>

                <!-- Events Scroll Pane -->
                <ScrollPane fitToWidth="true" style="-fx-background-color: transparent; -fx-padding: 0 20 20 20;">
                    <content>
                        <VBox fx:id="eventsContainer" spacing="0" alignment="TOP_CENTER" style="-fx-background-color: transparent;">
                        </VBox>
                    </content>
                </ScrollPane>
            </children>
        </VBox>
    </children>
</AnchorPane>