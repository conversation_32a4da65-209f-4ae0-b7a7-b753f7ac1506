<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>
<?import javafx.scene.effect.*?>

<?import javafx.scene.shape.Line?>
<AnchorPane xmlns="http://javafx.com/javafx/23.0.1"
            xmlns:fx="http://javafx.com/fxml/1"
            fx:controller="Controllers.AjouterCommande"
            style="-fx-background-color: #282828; -fx-border-color: #c49b63; -fx-border-width: 1; -fx-background-radius: 5; -fx-border-radius: 5;"
            prefWidth="400"
            prefHeight="300">

    <!-- Titre -->
    <Label text="AJOUTER AU PANIER"
           style="-fx-text-fill: #c49b63; -fx-font-size: 20px; -fx-font-weight: bold;"
           layoutX="20" layoutY="20">
        <font>
            <Font name="Segoe UI Light" size="20.0" />
        </font>
        <effect>
            <DropShadow color="#c49b63" radius="0" spread="0.1" />
        </effect>
    </Label>

    <Line startX="20" endX="380" startY="50" endY="50" stroke="#c49b63" strokeWidth="1" />

    <!-- Contenu principal -->
    <VBox layoutX="20" layoutY="60" spacing="15">
        <!-- Contrôle de quantité -->
        <VBox spacing="5">
            <Label text="QUANTITÉ:" style="-fx-text-fill: #c49b63; -fx-font-size: 14px;"/>
            <HBox spacing="10" alignment="CENTER_LEFT">
                <Button text="-"
                        onAction="#decrementQuantity"
                        style="-fx-background-color: #383838; -fx-text-fill: #c49b63; -fx-font-weight: bold; -fx-min-width: 30; -fx-min-height: 30; -fx-background-radius: 15;"/>

                <TextField fx:id="quantityField"
                           text="1"
                           style="-fx-background-color: #383838; -fx-text-fill: white; -fx-pref-width: 60; -fx-alignment: CENTER;"
                           onKeyReleased="#calculateTotal"/>

                <Button text="+"
                        onAction="#incrementQuantity"
                        style="-fx-background-color: #383838; -fx-text-fill: #c49b63; -fx-font-weight: bold; -fx-min-width: 30; -fx-min-height: 30; -fx-background-radius: 15;"/>
            </HBox>

            <!-- Message de validation -->
            <VBox fx:id="validationContainer" visible="false">
                <Label fx:id="validationMessage"
                       style="-fx-text-fill: #ff4444; -fx-font-size: 12px;"/>
            </VBox>
        </VBox>

        <!-- Informations de prix -->
        <VBox spacing="10">
            <HBox spacing="10">
                <Label text="Prix unitaire:" style="-fx-text-fill: #e0e0e0;"/>
                <Label fx:id="unitPriceLabel" text="0 TND" style="-fx-text-fill: #c49b63; -fx-font-weight: bold;"/>
            </HBox>

            <HBox spacing="10">
                <Label text="Prix total:" style="-fx-text-fill: #e0e0e0;"/>
                <Label fx:id="totalPriceLabel" text="0 TND" style="-fx-text-fill: #c49b63; -fx-font-weight: bold;"/>
            </HBox>
        </VBox>
    </VBox>

    <!-- Boutons -->
    <HBox spacing="20" alignment="CENTER_RIGHT" layoutX="20" layoutY="230">
        <Button text="ANNULER"
                onAction="#closePopup"
                style="-fx-background-color: transparent; -fx-text-fill: #c49b63; -fx-border-color: #c49b63; -fx-border-width: 1; -fx-background-radius: 20; -fx-border-radius: 20; -fx-padding: 5 20;"/>

        <Button text="CONFIRMER"
                onAction="#confirmOrder"
                style="-fx-background-color: #c49b63; -fx-text-fill: black; -fx-font-weight: bold; -fx-background-radius: 20; -fx-padding: 5 20;">
            <effect>
                <DropShadow color="#00000080" radius="3" />
            </effect>
        </Button>
    </HBox>
</AnchorPane>