<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.*?>
<?import javafx.scene.image.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<AnchorPane prefHeight="600.0" prefWidth="900.0" style="-fx-background-color: linear-gradient(to bottom, #2a2a2a, #1e1e1e);"
            xmlns="http://javafx.com/javafx/17" xmlns:fx="http://javafx.com/fxml/1"
            fx:controller="Controllers.AffichageProduitBack">

    <children>

        <!-- Main Content -->
        <VBox layoutY="0.0" prefHeight="630.0" prefWidth="1000.0" style="-fx-background-color: transparent;">
            <children>
                <!-- En-tête section produits -->
                <StackPane prefHeight="100.0" prefWidth="1000.0">
                    <children>
                        <VBox alignment="CENTER" spacing="10" style="-fx-background-color: rgba(0, 0, 0, 0.5); -fx-padding: 20;">
                            <children>
                                <Label style="-fx-text-fill: #c49b63; -fx-font-size: 28px; -fx-font-weight: bold;" text="LISTE DES PRODUITS">
                                    <font>
                                        <Font name="Segoe UI Light" size="28.0" />
                                    </font>
                                </Label>
                            </children>
                        </VBox>
                    </children>
                </StackPane>

                <!-- Conteneur des produits -->
                <ScrollPane fx:id="scrollPane" fitToWidth="true" hbarPolicy="NEVER"
                            style="-fx-background: transparent; -fx-background-color: transparent; -fx-padding: 0 20 20 20;">

                    <content>
                        <TilePane fx:id="productsContainer" hgap="20" vgap="20"
                                  style="-fx-background-color: transparent; -fx-padding: 10;"
                                  prefColumns="2" maxWidth="Infinity" />

                    </content>
                </ScrollPane>
            </children>
        </VBox>
    </children>
</AnchorPane>