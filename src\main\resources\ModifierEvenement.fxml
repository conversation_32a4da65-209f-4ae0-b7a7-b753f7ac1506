<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.ChoiceBox?>
<?import javafx.scene.control.DatePicker?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.TextArea?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.ColumnConstraints?>
<?import javafx.scene.layout.GridPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.Region?>
<?import javafx.scene.layout.RowConstraints?>
<?import javafx.scene.layout.VBox?>

<VBox prefHeight="538.0" prefWidth="900" spacing="20" style="-fx-padding: 25; -fx-background-color: #f8f8f8;" xmlns="http://javafx.com/javafx/23.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="Controllers.ModifierEvenementController">

   <Label style="-fx-font-size: 24; -fx-font-weight: bold; -fx-text-fill: #915f3a;" text="✏ Modifier un Événement">
      <padding>
         <Insets bottom="10" />
      </padding>
   </Label>

   <GridPane hgap="15" style="-fx-background-color: white; -fx-background-radius: 8; -fx-padding: 20; -fx-border-color: #915f3a; -fx-border-radius: 8; -fx-border-width: 2;" vgap="15">
      <padding>
         <Insets bottom="15" left="15" right="15" top="15" />
      </padding>

      <!-- Titre Événement -->
      <Label style="-fx-font-weight: bold;" text="Titre de l'événement:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
      <TextArea fx:id="TitreField" prefHeight="35" prefWidth="250" promptText="Entrez le titre" style="-fx-background-radius: 4;" GridPane.columnIndex="1" GridPane.rowIndex="0" />

      <!-- Type -->
      <Label style="-fx-font-weight: bold;" text="Type d'événement:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
      <ChoiceBox fx:id="TypeField" prefWidth="250" GridPane.columnIndex="1" GridPane.rowIndex="1" />

      <!-- Date -->
      <Label style="-fx-font-weight: bold;" text="Date de l'événement:" GridPane.columnIndex="0" GridPane.rowIndex="2" />
      <DatePicker fx:id="DateField" prefWidth="250" style="-fx-background-radius: 4;" GridPane.columnIndex="1" GridPane.rowIndex="2" />

      <!-- Prix -->
      <Label style="-fx-font-weight: bold;" text="Prix (DT):" GridPane.columnIndex="0" GridPane.rowIndex="3" />
      <TextArea fx:id="PrixField" prefHeight="35" prefWidth="250" promptText="00.00" style="-fx-background-radius: 4;" GridPane.columnIndex="1" GridPane.rowIndex="3" />

      <!-- Description -->
      <Label style="-fx-font-weight: bold;" text="Description:" GridPane.columnIndex="2" GridPane.rowIndex="0" />
      <TextArea fx:id="descField" prefHeight="80" prefWidth="250" promptText="Description..." style="-fx-background-radius: 4;" wrapText="true" GridPane.columnIndex="3" GridPane.rowIndex="0" />

      <!-- Capacité -->
      <Label style="-fx-font-weight: bold;" text="Capacité Maximale:" GridPane.columnIndex="2" GridPane.rowIndex="1" />
      <TextArea fx:id="capFrield" prefHeight="35" prefWidth="250" promptText="Nombre maximum" style="-fx-background-radius: 4;" GridPane.columnIndex="3" GridPane.rowIndex="1" />

      <!-- Image -->
      <Label style="-fx-font-weight: bold;" text="Image URL:" GridPane.columnIndex="2" GridPane.rowIndex="2" />
      <TextArea fx:id="ImageField" prefHeight="35" prefWidth="250" promptText="URL de l'image" style="-fx-background-radius: 4;" GridPane.columnIndex="3" GridPane.rowIndex="2" />

      <!-- Image Preview -->
      <Label style="-fx-font-weight: bold;" text="Aperçu de l'image:" GridPane.columnIndex="2" GridPane.rowIndex="3" />
      <ImageView fx:id="imagePreview" fitHeight="100" fitWidth="150" preserveRatio="true" style="-fx-border-color: #e0e0e0; -fx-border-radius: 4;" GridPane.columnIndex="3" GridPane.rowIndex="3" />

      <columnConstraints>
         <ColumnConstraints hgrow="NEVER" minWidth="120" />
         <ColumnConstraints hgrow="SOMETIMES" minWidth="250" />
         <ColumnConstraints hgrow="NEVER" minWidth="120" />
         <ColumnConstraints hgrow="SOMETIMES" minWidth="250" />
      </columnConstraints>

      <rowConstraints>
         <RowConstraints minHeight="35" vgrow="NEVER" />
         <RowConstraints minHeight="35" vgrow="NEVER" />
         <RowConstraints minHeight="35" vgrow="NEVER" />
         <RowConstraints minHeight="35" vgrow="NEVER" />
      </rowConstraints>
   </GridPane>

   <!-- Button Panel -->
   <HBox spacing="20" style="-fx-padding: 20 0 0 0;">
      <children>
         <Button fx:id="btnReturn" onAction="#RetourListe" style="-fx-background-color: #e0e0e0; -fx-text-fill: #333; -fx-font-weight: bold; -fx-padding: 8 20; -fx-background-radius: 4;" text="⬅ Retour" />
         <Region HBox.hgrow="ALWAYS" />
         <Button fx:id="btnsave" onAction="#Enregistrer" style="-fx-background-color: #915f3a; -fx-text-fill: white; -fx-font-weight: bold; -fx-padding: 8 25; -fx-background-radius: 4;" text="✓ Enregistrer" />
         <Region HBox.hgrow="ALWAYS" />
         <Button fx:id="btnclear" onAction="#clear" style="-fx-background-color: #f0f0f0; -fx-text-fill: #333; -fx-font-weight: bold; -fx-padding: 8 20; -fx-background-radius: 4; -fx-border-color: #ccc; -fx-border-radius: 4;" text="♻ Réinitialiser" />
      </children>
   </HBox>
</VBox>
