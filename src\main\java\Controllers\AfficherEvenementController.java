package Controllers;



import entity.Evenement;

import javafx.event.ActionEvent;

import javafx.fxml.FXML;

import javafx.fxml.FXMLLoader;

import javafx.geometry.Insets;

import javafx.scene.Node;

import javafx.scene.Parent;

import javafx.scene.Scene;

import javafx.scene.control.*;

import javafx.scene.image.Image;

import javafx.scene.image.ImageView;

import javafx.scene.layout.AnchorPane;

import javafx.scene.layout.HBox;

import javafx.scene.layout.VBox;

import javafx.scene.paint.Color;

import javafx.scene.text.Font;

import javafx.scene.text.Text;

import javafx.stage.Modality;

import javafx.stage.Stage;

import service.EvenementService;



import java.io.IOException;

import java.sql.SQLException;

import java.text.SimpleDateFormat;

import java.util.List;

import java.util.Optional;



public class AfficherEvenementController {

    @FXML

    private AnchorPane scrollPane;



    @FXML

    private Button btnAjout;



    @FXML

    private Button btnSearch;



    @FXML

    private VBox eventsContainer;



    private EvenementService evenementService = new EvenementService();

    private SimpleDateFormat dateFormat = new SimpleDateFormat("dd MMMM yyyy");



    private Evenement event;







    @FXML

    public void initialize() {

        loadEvents();

    }



    private void loadEvents() {

        try {

            List<Evenement> events = evenementService.recupererEvenement();

            displayEvents(events);

        } catch (SQLException e) {

            showAlert("Erreur de base de données", "Échec du chargement des événements: " + e.getMessage(), Alert.AlertType.ERROR);

            e.printStackTrace();

        }

    }



    private void displayEvents(List<Evenement> events) {

        eventsContainer.getChildren().clear();



        if (events.isEmpty()) {

            Label noEventsLabel = new Label("Aucun événement disponible");

            noEventsLabel.setStyle("-fx-font-size: 16px; -fx-text-fill: #666;");

            eventsContainer.getChildren().add(noEventsLabel);

            return;

        }



        for (Evenement event : events) {

            VBox eventCard = createEventCard(event);

            eventsContainer.getChildren().add(eventCard);

        }

    }

    private void styleLabel(Label label, String prefix, String value) {

// Create text nodes for each part

        Text prefixText = new Text(prefix);

        prefixText.setStyle("-fx-fill: #915f3a; -fx-font-weight: bold;");



        Text valueText = new Text(value);

        valueText.setStyle("-fx-fill: black;");



// Combine them in an HBox

        HBox container = new HBox(prefixText, valueText);

        container.setSpacing(0);



// Set as the label's graphic

        label.setGraphic(container);

        label.setContentDisplay(ContentDisplay.GRAPHIC_ONLY);

    }



    private VBox createEventCard(Evenement event) {

        VBox card = new VBox(10);

        card.setStyle("-fx-background-color: #f8f9fa; -fx-border-color: #915f3a; -fx-border-radius: 5; -fx-padding: 15;");

        card.setPadding(new Insets(15));



// Event image

        if (event.getImage_event() != null && !event.getImage_event().isEmpty()) {

            try {

                ImageView imageView = new ImageView(new Image(event.getImage_event()));

                imageView.setFitWidth(300);

                imageView.setFitHeight(200);

                imageView.setPreserveRatio(true);

                card.getChildren().add(imageView);

            } catch (Exception e) {

                System.err.println("Erreur lors du chargement de l'image: " + e.getMessage());

            }

        }



// Event title

        Label titleLabel = new Label(event.getTitre_evenement());

        titleLabel.setFont(Font.font(18));

        titleLabel.setStyle("-fx-font-weight: bold; -fx-text-fill: #915f3a;");



// Event details

        Label descriptionLabel = new Label(event.getDescription_event());

        descriptionLabel.setWrapText(true);













        Label dateLabel = new Label();

        styleLabel(dateLabel, "📅 Date: ", dateFormat.format(event.getDate_event()));



        Label capacityLabel = new Label();

        styleLabel(capacityLabel, "👥 Capacité: ", String.valueOf(event.getCapacite_max()));



        Label priceLabel = new Label();

        styleLabel(priceLabel, "💵 Prix: ", String.format("%.2f DT", event.getPrix_event()));



        Label typeLabel = new Label();

        styleLabel(typeLabel, "🎭 Type: ", event.getType_event());













// Action buttons

        HBox buttonBox = new HBox(10);

        buttonBox.setPadding(new Insets(10, 0, 0, 0));



        Button editButton = createButton("✏️ Modifier", "#b97b4c", e -> openEditEventForm(event));

        Button deleteButton = createButton("❌ Supprimer", "#b94e4e", e -> confirmAndDeleteEvent(event.getId()));



        buttonBox.getChildren().addAll(editButton, deleteButton);



// Add all components to the card

        card.getChildren().addAll(

                titleLabel,

                descriptionLabel,

                dateLabel,

                capacityLabel,

                priceLabel,

                typeLabel,

                buttonBox

        );



        return card;

    }



    private Button createButton(String text, String color, javafx.event.EventHandler<ActionEvent> handler) {

        Button button = new Button(text);

        button.setStyle(String.format(

                "-fx-background-color: %s; -fx-text-fill: white; -fx-font-weight: bold; -fx-padding: 5 10;",

                color

        ));

        button.setOnAction(handler);

        return button;

    }



    private void confirmAndDeleteEvent(int eventId) {

        Alert alert = new Alert(Alert.AlertType.CONFIRMATION);

        alert.setTitle("Confirmer la suppression");

        alert.setHeaderText("Supprimer l'événement");

        alert.setContentText("êtes-vous sûr de supprimer cet événement?");



        Optional<ButtonType> result = alert.showAndWait();

        if (result.isPresent() && result.get() == ButtonType.OK) {

            try {

                evenementService.supprimerEvenement(eventId);

                loadEvents(); // Refresh the list

                showAlert("Succés", "Evenement supprimé avec succés", Alert.AlertType.INFORMATION);

            } catch (SQLException e) {

                showAlert("Erreur", "Échec de la suppression de l'événement: " + e.getMessage(), Alert.AlertType.ERROR);

            }

        }

    }



    private void openEditEventForm(Evenement event) {

        try {

            FXMLLoader loader = new FXMLLoader(getClass().getResource("/ModifierEvenement.fxml"));

            Parent root = loader.load(); // Load FIRST



// THEN get the controller

            ModifierEvenementController controller = loader.getController();

            controller.setEvent(event); // Set the data after loading



            Stage stage = new Stage();

            stage.setTitle("Modifier Evénement: " + event.getTitre_evenement());

            stage.setScene(new Scene(root));

            stage.show();



            stage.setOnHidden(e -> loadEvents());

        } catch (IOException e) {

            showAlert("Erreur", "Échec du chargement du formulaire d'édition: " + e.getMessage(), Alert.AlertType.ERROR);

            e.printStackTrace();

        }

    }



    private void showAlert(String title, String message, Alert.AlertType type) {

        Alert alert = new Alert(type);

        alert.setTitle(title);

        alert.setHeaderText(null);

        alert.setContentText(message);

        alert.showAndWait();

    }



    @FXML

    private void handleRefresh(ActionEvent event) {

        loadEvents();

    }



    @FXML

    void Ajouter(ActionEvent event) {

        try {

// Load the FXML file for adding events

            FXMLLoader loader = new FXMLLoader(getClass().getResource("/Crud.FXML.fxml"));

            Parent root = loader.load();



// Create a new stage for the add event window

            Stage stage = new Stage();

            stage.setTitle("Ajouter un Événement");

            stage.setScene(new Scene(root));



// Set the owner to prevent clicking behind the modal

            Stage currentStage = (Stage)((Node)event.getSource()).getScene().getWindow();

            stage.initOwner(currentStage);



// Optional: Make it modal

            stage.initModality(Modality.WINDOW_MODAL);



// Refresh the event list when the add window closes

            stage.setOnHidden(e -> loadEvents());



            stage.show();



        } catch (IOException e) {

            showAlert("Erreur", "Impossible d'ouvrir le formulaire d'ajout: " + e.getMessage(), Alert.AlertType.ERROR);

            e.printStackTrace();

        }

    }





    @FXML

    void Rechercher(ActionEvent event) {



    }

}