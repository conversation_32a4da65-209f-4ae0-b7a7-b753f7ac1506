<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.TextArea?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.layout.HBox?>

<AnchorPane prefHeight="300.0" prefWidth="500.0" xmlns="http://javafx.com/javafx/8"
            xmlns:fx="http://javafx.com/fxml/1"
            fx:controller="Controllers.ModifierCommentaireController">

   <children>
      <VBox layoutX="20.0" layoutY="20.0" spacing="20.0" AnchorPane.bottomAnchor="20.0" 
            AnchorPane.leftAnchor="20.0" AnchorPane.rightAnchor="20.0" AnchorPane.topAnchor="20.0">
         <children>
            <Label text="Modifier le commentaire" style="-fx-font-size: 20px; -fx-font-weight: bold;" />
            
            <Label text="Contenu du commentaire:" />
            <TextArea fx:id="contenuTxt" prefHeight="100.0" prefWidth="200.0" 
                     promptText="Entrez votre commentaire ici..." />
            
            <HBox spacing="10.0" alignment="CENTER_RIGHT">
               <Button fx:id="btnAnnuler" mnemonicParsing="false" onAction="#annuler" 
                       text="Annuler" style="-fx-background-color: #f44336; -fx-text-fill: white;" />
               <Button fx:id="btnModifier" mnemonicParsing="false" onAction="#modifierCommentaire" 
                       text="Modifier" style="-fx-background-color: #2196F3; -fx-text-fill: white;" />
            </HBox>
         </children>
      </VBox>
   </children>
</AnchorPane> 