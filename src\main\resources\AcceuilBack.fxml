<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.image.Image?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.Pane?>
<?import javafx.scene.layout.StackPane?>
<?import javafx.scene.layout.VBox?>

<?import javafx.scene.control.ListView?>
<?import javafx.scene.control.Separator?>
<?import javafx.scene.control.Tooltip?>
<AnchorPane style="-fx-background-color: #2a2a2a;" xmlns="http://javafx.com/javafx/23.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="Controllers.AcceuilBackController">

    <!-- Background Image (optional) -->
    <ImageView fitHeight="800.0" fitWidth="1200.0" opacity="0.3" preserveRatio="false">
        <image>
            <Image url="@/Back.jpg" />
        </image>
    </ImageView>

    <!-- Main Container -->
    <HBox prefHeight="800.0" prefWidth="1200.0" style="-fx-background-color: rgba(0,0,0,0.7);">

        <!-- Sidebar -->
        <VBox prefWidth="200.0" style="-fx-background-color: #1a1a1a; -fx-border-color: #333333; -fx-border-width: 0 1 0 0;">
            <padding>
                <Insets bottom="20" left="10" right="10" top="20" />
            </padding>

            <!-- Logo -->
            <ImageView fitHeight="73.0" fitWidth="180.0" preserveRatio="true">
                <image>
                    <Image url="@/logofront.png" />
                </image>
            </ImageView>

            <Label alignment="CENTER" prefHeight="67.0" prefWidth="198.0" style="-fx-text-fill: #c49b63; -fx-font-size: 18px; -fx-padding: 20 0 20 0; -fx-font-weight: bold;" text="Espace Admin" />

            <!-- Navigation Buttons -->
            <VBox spacing="10">
                <Button onAction="#showEvents" prefHeight="42.0" prefWidth="178.0"
                        style="-fx-background-color: #2a2a2a; -fx-text-fill: white; -fx-font-size: 14px;
                               -fx-border-color: #c49b63; -fx-border-width: 1px; -fx-border-radius: 5px;
                               -fx-padding: 10 15; -fx-cursor: hand;"
                        text="Gestion Événements" />

                <Button onAction="#showReservations" prefHeight="42.0" prefWidth="178.0"
                        style="-fx-background-color: #2a2a2a; -fx-text-fill: white; -fx-font-size: 14px;
                               -fx-border-color: #c49b63; -fx-border-width: 1px; -fx-border-radius: 5px;
                               -fx-padding: 10 15; -fx-cursor: hand;"
                        text="Gestion Réservations" />

                <Button onAction="#showProduits" prefHeight="42.0" prefWidth="178.0"
                        style="-fx-background-color: #2a2a2a; -fx-text-fill: white; -fx-font-size: 14px;
                               -fx-border-color: #c49b63; -fx-border-width: 1px; -fx-border-radius: 5px;
                               -fx-padding: 10 15; -fx-cursor: hand;"
                        text="Gestion Produits" />

                <Button onAction="#showProductStats" prefHeight="42.0" prefWidth="178.0"
                        style="-fx-background-color: #2a2a2a; -fx-text-fill: white; -fx-font-size: 14px;
                               -fx-border-color: #c49b63; -fx-border-width: 1px; -fx-border-radius: 5px;
                               -fx-padding: 10 15; -fx-cursor: hand;"
                        text="Product Statistics" />

                <Button onAction="#showPosts" prefHeight="42.0" prefWidth="178.0"
                        style="-fx-background-color: #2a2a2a; -fx-text-fill: white; -fx-font-size: 14px;
                               -fx-border-color: #c49b63; -fx-border-width: 1px; -fx-border-radius: 5px;
                               -fx-padding: 10 15; -fx-cursor: hand;"
                        text="Gestion Posts" />

                <Button onAction="#showCommantaire" prefHeight="42.0" prefWidth="178.0"
                        style="-fx-background-color: #2a2a2a; -fx-text-fill: white; -fx-font-size: 14px;
                               -fx-border-color: #c49b63; -fx-border-width: 1px; -fx-border-radius: 5px;
                               -fx-padding: 10 15; -fx-cursor: hand;"
                        text="Gestion Commentaires" />
            </VBox>

            <!-- Logout Button at the bottom -->
            <Pane VBox.vgrow="ALWAYS" />
            <Button onAction="#logout" prefHeight="42.0" prefWidth="178.0"
                    style="-fx-background-color: #3a1f1f; -fx-text-fill: white; -fx-font-size: 14px;
                           -fx-border-color: #c49b63; -fx-border-width: 1px; -fx-border-radius: 5px;
                           -fx-padding: 10 15; -fx-cursor: hand;"
                    text="Se Déconnecter" />
        </VBox>

        <!-- Content Area -->
        <StackPane fx:id="contentPane" style="-fx-background-color: transparent;" HBox.hgrow="ALWAYS">
            <!-- Default content -->
            <VBox alignment="CENTER" spacing="20">
                <Label style="-fx-text-fill: #c49b63; -fx-font-size: 24px; -fx-font-weight: bold;" text="Bienvenue dans l'interface d'administration" />
                <ImageView fitHeight="300.0" fitWidth="300.0" preserveRatio="true">
                    <image>
                    </image>
                </ImageView>
            </VBox>
        </StackPane>
    </HBox>
    <!-- Dans AcceuilBack.fxml -->
    <!-- Notification System - Version finale intégrée -->
    <HBox alignment="CENTER_RIGHT" layoutX="950" layoutY="20" spacing="5">
        <!-- Bouton Notification avec icône 🔔 existante -->
        <Button fx:id="notificationButton"
                style="-fx-background-color: transparent; -fx-padding: 0; -fx-font-size: 24px;"
                text="🔔"
                onAction="#toggleNotifications">
            <tooltip>
                <Tooltip text="Produits en attente" style="-fx-font-size: 12px; -fx-text-fill: #c49b63;"/>
            </tooltip>
        </Button>

        <!-- Badge de notification - Style café -->
        <Label fx:id="notificationBadge"
               style="-fx-background-color: #c49b63; -fx-text-fill: #1a1a1a; -fx-font-weight: bold;
                 -fx-background-radius: 10; -fx-padding: 1 5; -fx-font-size: 11px;"
               minWidth="18" text="0" visible="false"/>
    </HBox>

    <!-- Panel des notifications - Style café -->
    <VBox fx:id="notificationPanel" layoutX="830" layoutY="60" prefWidth="320" prefHeight="350"
          style="-fx-background-color: #2a2a2a; -fx-border-color: #c49b63; -fx-border-width: 1;
             -fx-border-radius: 5; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.3), 8, 0, 0, 0);"
          visible="false">
        <children>
            <Label text="🔔 Produits en attente"
                   style="-fx-text-fill: #c49b63; -fx-font-size: 15px;
                     -fx-font-weight: bold; -fx-padding: 12 10 8 10;"/>
            <Separator style="-fx-border-color: #c49b63; -fx-padding: 0 5;"/>

            <ListView fx:id="notificationList"
                      style="-fx-background-color: #1a1a1a; -fx-text-fill: #e0e0e0; -fx-font-size: 13px;
                       -fx-border-width: 0; -fx-padding: 5;">
                <placeholder>
                    <Label text="Aucun produit en attente" style="-fx-text-fill: #c49b63;"/>
                </placeholder>
            </ListView>

            <Button text="Tout voir" onAction="#showAllPending"
                    style="-fx-background-color: #c49b63; -fx-text-fill: #1a1a1a; -fx-font-weight: bold;
                      -fx-font-size: 13px; -fx-padding: 8; -fx-cursor: hand;"/>
        </children>
    </VBox>
</AnchorPane>