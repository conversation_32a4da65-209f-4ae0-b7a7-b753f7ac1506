package Controllers;

import entity.Commentaire;
import entity.Post;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import javafx.scene.control.Alert;
import javafx.scene.control.Button;
import javafx.scene.control.Label;
import javafx.scene.layout.VBox;
import service.CommentaireService;
import service.PostService;

import java.io.IOException;
import java.sql.SQLException;
import java.util.List;

public class ForumBackendController {
    @FXML
    private VBox postContainer;
    @FXML
    private Button btnRetour;

    private final PostService postService = new PostService();
    private final CommentaireService commentaireService = new CommentaireService();

    @FXML
    public void initialize() {
        displayPosts();
    }

    private void displayPosts() {
        try {
            postContainer.getChildren().clear();
            List<Post> posts = postService.recuperer();

            for (Post post : posts) {
                VBox postBox = new VBox(10);
                postBox.setStyle("-fx-background-color: #FFFFFF; -fx-padding: 15; -fx-border-radius: 10; -fx-background-radius: 10; -fx-border-color: #800000; -fx-border-width: 2;");

                // Post content
                Label postContent = new Label("Post ID: " + post.getId() + "\n" +
                        "Description: " + post.getDescription_post() + "\n" +
                        "Date: " + post.getDate_post() + "\n" +
                        "Likes: " + post.getNbr_likes());
                postContent.setStyle("-fx-font-size: 14px; -fx-text-fill: #800000;");
                postContent.setWrapText(true);

                // Comments section
                VBox commentsBox = new VBox(5);
                commentsBox.setStyle("-fx-padding: 10; -fx-background-color: #FFF5F5; -fx-background-radius: 5; -fx-border-color: #A52A2A; -fx-border-width: 1;");
                
                Label commentsTitle = new Label("Commentaires:");
                commentsTitle.setStyle("-fx-font-weight: bold; -fx-text-fill: #800000; -fx-font-size: 16px;");
                commentsBox.getChildren().add(commentsTitle);

                List<Commentaire> comments = commentaireService.recupererCommentaireParPost(post.getId());
                if (comments.isEmpty()) {
                    Label noComments = new Label("Aucun commentaire");
                    noComments.setStyle("-fx-font-style: italic; -fx-text-fill: #A52A2A;");
                    commentsBox.getChildren().add(noComments);
                } else {
                    for (Commentaire comment : comments) {
                        VBox commentBox = new VBox(5);
                        commentBox.setStyle("-fx-background-color: #FFFFFF; -fx-padding: 8; -fx-background-radius: 5; -fx-border-color: #A52A2A; -fx-border-width: 1;");
                        
                        Label commentContent = new Label("Comment ID: " + comment.getId() + "\n" +
                                "Content: " + comment.getContenu() + "\n" +
                                "Date: " + comment.getDate_commentaire() + "\n" +
                                "User ID: " + comment.getUser().getId());
                        commentContent.setStyle("-fx-text-fill: #800000;");
                        commentContent.setWrapText(true);
                        
                        Button deleteCommentBtn = new Button("Supprimer");
                        deleteCommentBtn.setStyle("-fx-background-color: #A52A2A; -fx-text-fill: white; -fx-font-weight: bold; -fx-padding: 5 10;");
                        deleteCommentBtn.setOnAction(e -> supprimerCommentaire(comment, commentBox));
                        
                        commentBox.getChildren().addAll(commentContent, deleteCommentBtn);
                        commentsBox.getChildren().add(commentBox);
                    }
                }

                // Delete post button
                Button deletePostBtn = new Button("Supprimer le post");
                deletePostBtn.setStyle("-fx-background-color: #A52A2A; -fx-text-fill: white; -fx-font-weight: bold; -fx-padding: 8 15;");
                deletePostBtn.setOnAction(e -> supprimerPost(post, postBox));

                postBox.getChildren().addAll(postContent, commentsBox, deletePostBtn);
                postContainer.getChildren().add(postBox);
            }
        } catch (SQLException e) {
            showAlert(Alert.AlertType.ERROR, "Erreur", "Erreur lors du chargement des posts: " + e.getMessage());
        }
    }

    private void supprimerPost(Post post, VBox postBox) {
        try {
            postService.supprimer(post);
            postContainer.getChildren().remove(postBox);
            showAlert(Alert.AlertType.INFORMATION, "Succès", "Post supprimé avec succès");
        } catch (SQLException e) {
            showAlert(Alert.AlertType.ERROR, "Erreur", "Erreur lors de la suppression du post: " + e.getMessage());
        }
    }

    private void supprimerCommentaire(Commentaire commentaire, VBox commentBox) {
        try {
            commentaireService.supprimerCommentaire(commentaire);
            commentBox.getParent().getChildrenUnmodifiable().remove(commentBox);
            showAlert(Alert.AlertType.INFORMATION, "Succès", "Commentaire supprimé avec succès");
        } catch (SQLException e) {
            showAlert(Alert.AlertType.ERROR, "Erreur", "Erreur lors de la suppression du commentaire: " + e.getMessage());
        }
    }

    @FXML
    private void retour() {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/adminDashboard.fxml"));
            Parent root = loader.load();
            btnRetour.getScene().setRoot(root);
        } catch (IOException e) {
            showAlert(Alert.AlertType.ERROR, "Erreur", "Erreur lors de la navigation: " + e.getMessage());
        }
    }

    private void showAlert(Alert.AlertType alertType, String title, String content) {
        Alert alert = new Alert(alertType);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(content);
        alert.showAndWait();
    }
} 