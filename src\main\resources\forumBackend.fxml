<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.ScrollPane?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.VBox?>
<?import javafx.geometry.Insets?>
<?import javafx.scene.paint.Color?>
<?import javafx.scene.text.Font?>

<AnchorPane prefHeight="800.0" prefWidth="1000.0" style="-fx-background-color: #F5F5F5;" 
            xmlns="http://javafx.com/javafx/8"
            xmlns:fx="http://javafx.com/fxml/1"
            fx:controller="Controllers.ForumBackendController">

   <children>
      <VBox layoutX="20.0" layoutY="20.0" spacing="20.0" AnchorPane.bottomAnchor="20.0" 
            AnchorPane.leftAnchor="20.0" AnchorPane.rightAnchor="20.0" AnchorPane.topAnchor="20.0">
         <children>
            <HBox alignment="CENTER_LEFT" spacing="15.0" style="-fx-background-color: #b83232; -fx-padding: 15; -fx-background-radius: 10;">
               <children>
                  <Label text="Gestion du Forum" style="-fx-font-size: 28px; -fx-font-weight: bold; -fx-text-fill: white;" />
                  <Button fx:id="btnRetour" text="Retour" onAction="#retour" 
                          style="-fx-background-color: rgba(106,72,24,0.46); -fx-text-fill: white; -fx-font-weight: bold; -fx-padding: 10 20;"
                          HBox.hgrow="ALWAYS" />
               </children>
            </HBox>

            <ScrollPane fitToWidth="true" VBox.vgrow="ALWAYS" style="-fx-background: #F5F5F5; -fx-border-color: rgba(87,50,4,0.7);">
               <content>
                  <VBox fx:id="postContainer" spacing="15.0">
                     <padding>
                        <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
                     </padding>
                  </VBox>
               </content>
            </ScrollPane>
         </children>
      </VBox>
   </children>
</AnchorPane> 