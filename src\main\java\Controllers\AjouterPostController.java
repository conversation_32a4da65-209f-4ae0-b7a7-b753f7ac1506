package Controllers;

import entity.Post;
import entity.User;
import entity.UserSession;
import javafx.event.ActionEvent;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.control.Alert;
import javafx.scene.control.Button;
import javafx.scene.control.TextArea;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.stage.FileChooser;
import javafx.stage.Stage;
import service.PostService;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.sql.Date;
import java.sql.SQLException;
import java.time.LocalDate;
import java.util.UUID;

public class AjouterPostController {

    private final PostService ps = new PostService();
    private File selectedImageFile;
    private String imageName = "default.jpg"; // Default image name if none selected

    @FXML
    private Button btnAjouter;

    @FXML
    private Button btnChoisirImage;

    @FXML
    private Button btnAnnuler;

    @FXML
    private TextArea descriptionTxt;

    @FXML
    private ImageView imageView;

    @FXML
    public void initialize() {
        System.out.println("==== Initializing AjouterPostController ====");
        System.out.println("Checking components...");
        
        if (descriptionTxt == null) {
            System.out.println("ERROR: descriptionTxt is null");
        } else {
            System.out.println("descriptionTxt is initialized");
        }
        
        if (btnAjouter == null) {
            System.out.println("ERROR: btnAjouter is null");
        } else {
            System.out.println("btnAjouter is initialized");
            System.out.println("btnAjouter text: " + btnAjouter.getText());
        }
        
        if (btnChoisirImage == null) {
            System.out.println("ERROR: btnChoisirImage is null");
        } else {
            System.out.println("btnChoisirImage is initialized");
            System.out.println("btnChoisirImage text: " + btnChoisirImage.getText());
        }
        
        if (imageView == null) {
            System.out.println("ERROR: imageView is null");
        } else {
            System.out.println("imageView is initialized");
        }
        
        System.out.println("==== Initialization complete ====");
    }

    @FXML
    void choisirImage(ActionEvent event) {
        System.out.println("==== Choosing image ====");
        FileChooser fileChooser = new FileChooser();
        fileChooser.setTitle("Choisir une image");
        fileChooser.getExtensionFilters().addAll(
                new FileChooser.ExtensionFilter("Images", "*.png", "*.jpg", "*.jpeg", "*.gif")
        );
        
        System.out.println("Opening file chooser...");
        selectedImageFile = fileChooser.showOpenDialog(btnChoisirImage.getScene().getWindow());

        if (selectedImageFile != null) {
            System.out.println("Image selected: " + selectedImageFile.getAbsolutePath());
            try {
                Image image = new Image(selectedImageFile.toURI().toString());
                imageView.setImage(image);
                
                // Generating a unique filename using UUID
                String originalFilename = selectedImageFile.getName();
                String fileExtension = originalFilename.substring(originalFilename.lastIndexOf("."));
                imageName = UUID.randomUUID().toString() + fileExtension;
                System.out.println("Generated image name: " + imageName);
            } catch (Exception e) {
                System.out.println("Error loading image: " + e.getMessage());
                e.printStackTrace();
                showAlert(Alert.AlertType.ERROR, "Error loading image", e.getMessage());
            }
        } else {
            System.out.println("No image selected");
        }
    }
    @FXML
    void addPost(ActionEvent event) {
        // Vérifie si l'utilisateur est connecté
        User user = UserSession.getInstance().getUser();
        if (user == null) {
            showAlert(Alert.AlertType.ERROR, "Erreur", "Vous devez être connecté pour créer un post");
            return;
        }

        // Validation de la description
        String description = descriptionTxt.getText().trim();
        if (description.isEmpty()) {
            showAlert(Alert.AlertType.WARNING, "Erreur de validation", "La description ne peut pas être vide");
            return;
        }

        if (description.length() < 3) {
            showAlert(Alert.AlertType.WARNING, "Erreur de validation", "La description doit contenir au moins 3 caractères");
            return;
        }

        try {
            // Création de l'objet Post
            Post post = new Post(
                    user,
                    description,
                    Date.valueOf(LocalDate.now()),
                    imageName,
                    true
            );
            post.setNbr_likes(0);

            // Gestion de l'image
            if (selectedImageFile != null) {
                String destDir = "src/main/resources/images/";
                Path destPath = Paths.get(destDir);

                if (!Files.exists(destPath)) {
                    Files.createDirectories(destPath);
                }

                Files.copy(
                        selectedImageFile.toPath(),
                        destPath.resolve(imageName),
                        StandardCopyOption.REPLACE_EXISTING
                );
            }

            // Sauvegarde en base de données
            ps.ajouter(post);

            // Reset formulaire
            descriptionTxt.clear();
            imageView.setImage(null);
            selectedImageFile = null;

            showAlert(Alert.AlertType.INFORMATION, "Succès", "Post ajouté avec succès");
            navigateToAfficherPost();

        } catch (SQLException e) {
            showAlert(Alert.AlertType.ERROR, "Erreur Base de Données", "Erreur lors de l'ajout du post: " + e.getMessage());
        } catch (IOException e) {
            showAlert(Alert.AlertType.ERROR, "Erreur Fichier", "Impossible de copier l'image: " + e.getMessage());
        }
    }

    
    private void navigateToAfficherPost() {
        try {
            System.out.println("Navigating to afficherPost view...");
            
            // Try different class loaders to find the FXML
            FXMLLoader loader = null;
            
            // Try with ClassLoader - most reliable cross-platform approach
            loader = new FXMLLoader(getClass().getClassLoader().getResource("afficherPost.fxml"));
            
            if (loader.getLocation() == null) {
                // Try with getClass().getResource
                loader = new FXMLLoader(getClass().getResource("/afficherPost.fxml"));
            }
            
            if (loader.getLocation() == null) {
                throw new IOException("Cannot find afficherPost.fxml resource");
            }
            
            System.out.println("Found afficherPost.fxml at: " + loader.getLocation());
            Parent root = loader.load();
            
            // Get controller and refresh posts list
            AfficherPostController controller = loader.getController();
            if (controller != null) {
                System.out.println("Controller loaded, refreshing posts...");
                controller.refreshPosts();
            } else {
                System.out.println("WARNING: Controller is null");
            }
            
            // Update the scene in the same window
            btnAjouter.getScene().setRoot(root);
            System.out.println("Navigation complete");
            
        } catch (IOException e) {
            System.out.println("Navigation error: " + e.getMessage());
            e.printStackTrace();
            showAlert(Alert.AlertType.ERROR, "Navigation Error", 
                    "Could not navigate to posts view: " + e.getMessage());
        }
    }
    
    private void showAlert(Alert.AlertType alertType, String title, String content) {
        Alert alert = new Alert(alertType);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(content);
        alert.showAndWait();
    }

    @FXML
    private void handleAnnuler(ActionEvent event) {
        try {
            // Load the main posts view
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/afficherPost.fxml"));
            Parent root = loader.load();
            
            // Get the current stage
            Stage stage = (Stage) btnAnnuler.getScene().getWindow();
            stage.setScene(new Scene(root));
            stage.show();
        } catch (IOException e) {
            System.out.println("Error navigating back to posts view: " + e.getMessage());
            showAlert(Alert.AlertType.ERROR, "Erreur", 
                     "Impossible de revenir à la vue des posts: " + e.getMessage());
        }
    }
}
