<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.ComboBox?>
<?import javafx.scene.control.DatePicker?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.PasswordField?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.image.Image?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.Region?>
<?import javafx.scene.layout.StackPane?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.text.Font?>

<StackPane style="-fx-background-color: linear-gradient(to bottom right, #2193b0, #6dd5ed);" xmlns="http://javafx.com/javafx/23.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="Controllers.AddUserControllerDash">
     <VBox alignment="CENTER" maxHeight="777.0" maxWidth="729.0" prefHeight="777.0" prefWidth="506.0" spacing="16" style="-fx-background-color: white;                      -fx-padding: 30;                      -fx-background-radius: 20;                      -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.15), 20, 0, 0, 10);">

         <!-- Header -->
         <VBox alignment="CENTER" prefHeight="73.0" prefWidth="231.0" spacing="5">
             <ImageView fitHeight="46.0" fitWidth="39.0" preserveRatio="true">
                 <Image url="@images/add-user-icon.png" />
             </ImageView>
             <Label prefHeight="35.0" prefWidth="238.0" style="-fx-font-size: 26px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;" text="Add/Edit New User">
            <font>
               <Font size="9.0" />
            </font>
         </Label>
             <Label style="-fx-font-size: 13px; -fx-text-fill: #95a5a6;" text="Fill in the information below" />
         <VBox.margin>
            <Insets top="-6.0" />
         </VBox.margin>
         </VBox>

         <!-- Form Fields -->
         <VBox alignment="CENTER" maxWidth="-Infinity" spacing="18">
             <!-- Personal Info -->
             <Label style="-fx-font-size: 15px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;" text="Personal Information">
            <font>
               <Font size="11.0" />
            </font>
         </Label>
         <HBox>
            <children>
                   <TextField fx:id="nameField" prefHeight="46.0" prefWidth="196.0" promptText="First Name" styleClass="input" />
               <Region prefHeight="11.0" prefWidth="37.0" />
                   <TextField fx:id="lastnameField" prefHeight="46.0" prefWidth="196.0" promptText="Last Name" styleClass="input" />
            </children>
            <VBox.margin>
               <Insets top="-6.0" />
            </VBox.margin>
         </HBox>
         <HBox>
            <children>
                   <TextField fx:id="emailField" prefHeight="46.0" prefWidth="196.0" promptText="Email Address" styleClass="input" />
               <Region prefHeight="11.0" prefWidth="37.0" />
                   <TextField fx:id="adresseField" prefHeight="46.0" prefWidth="196.0" promptText="Address" styleClass="input" />
            </children>
            <VBox.margin>
               <Insets top="-6.0" />
            </VBox.margin>
         </HBox>
             <TextField fx:id="telephoneField" prefHeight="46.0" prefWidth="340.0" promptText="Phone Number" styleClass="input">
            <VBox.margin>
               <Insets top="-6.0" />
            </VBox.margin></TextField>
             <DatePicker fx:id="birthDatePicker" prefHeight="46.0" prefWidth="340.0" promptText="Date of Birth" styleClass="input">
            <VBox.margin>
               <Insets top="-6.0" />
            </VBox.margin></DatePicker>

             <!-- Account Settings -->
             <Label style="-fx-font-size: 15px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;" text="Account Settings">
            <VBox.margin>
               <Insets top="-6.0" />
            </VBox.margin></Label>
         <HBox>
            <children>
                   <PasswordField fx:id="passwordField" prefHeight="46.0" prefWidth="196.0" promptText="Password" styleClass="input" />
               <Region prefHeight="11.0" prefWidth="37.0" />
                   <PasswordField fx:id="confirmPasswordField" prefHeight="46.0" prefWidth="196.0" promptText="Confirm Password" styleClass="input" />
            </children>
            <VBox.margin>
               <Insets top="-6.0" />
            </VBox.margin>
         </HBox>
             <ComboBox fx:id="roleComboBox" prefHeight="39.0" prefWidth="340.0" promptText="Select Role" styleClass="input">
            <VBox.margin>
               <Insets top="-6.0" />
            </VBox.margin></ComboBox>

             <!-- Profile Photo -->
             <Label style="-fx-font-size: 15px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;" text="Profile Photo">
            <VBox.margin>
               <Insets top="-6.0" />
            </VBox.margin></Label>
             <HBox alignment="CENTER_LEFT" spacing="10">
                 <TextField fx:id="photoField" editable="false" promptText="Choose a profile photo" style="-fx-background-radius: 8; -fx-padding: 12; -fx-background-color: #f8f9fa; -fx-border-color: #e9ecef; -fx-border-radius: 8;" HBox.hgrow="ALWAYS" />
                 <Button onAction="#handleBrowse" style="-fx-background-color: #f8f9fa; -fx-text-fill: #2c3e50; -fx-font-weight: bold;                                    -fx-background-radius: 8; -fx-padding: 10; -fx-cursor: hand;                                    -fx-border-color: #e9ecef; -fx-border-radius: 8;" text="Browse">
                     <graphic>
                         <ImageView fitHeight="16" fitWidth="16" preserveRatio="true">
                             <Image url="@images/upload-icon.png" />
                         </ImageView>
                     </graphic>
                 </Button>
            <VBox.margin>
               <Insets top="-6.0" />
            </VBox.margin>
             </HBox>

             <!-- Submit -->
             <Button fx:id="submitButton" maxWidth="320" onAction="#handleAddUser" style="-fx-background-color: #3498db; -fx-text-fill: white; -fx-font-weight: bold;                                -fx-background-radius: 8; -fx-padding: 10; -fx-cursor: hand;                                -fx-effect: dropshadow(gaussian, rgba(52,152,219,0.3), 10, 0, 0, 4);" text="Create Account">
            <VBox.margin>
               <Insets top="-6.0" />
            </VBox.margin></Button>
         </VBox>
     </VBox>
</StackPane>
