<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.*?>
<?import javafx.scene.image.*?>
<?import javafx.scene.layout.*?>

<AnchorPane prefHeight="600.0" prefWidth="800.0" xmlns="http://javafx.com/javafx/23.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="Controllers.MatchingController">
    <children>
        <ListView fx:id="matchingListView" layoutX="364.0" layoutY="67.0" prefHeight="400.0" prefWidth="350.0" />

        <Label layoutX="14.0" layoutY="60.0" text="Matching Details" />
        <TextField fx:id="NameSub" layoutX="14.0" layoutY="80.0" promptText="Name" />
        <TextField fx:id="sujet" layoutX="14.0" layoutY="126.0" promptText="Subject" />
        <TextField fx:id="numTable" layoutX="14.0" layoutY="167.0" promptText="Table Number" />
        <TextField fx:id="nbrPersonneMatchy" layoutX="14.0" layoutY="207.0" promptText="Number of People" />

        <Label layoutX="14.0" layoutY="240.0" text="Participants" />
        <TextField fx:id="user" layoutX="14.0" layoutY="260.0" promptText="Host User" editable="false" />
        <MenuButton fx:id="UaserButton" layoutX="187.0" layoutY="260.0" mnemonicParsing="false" text="Select Host" />

        <TextField fx:id="assessors" layoutX="14.0" layoutY="300.0" promptText="Assessors" editable="false" />
        <MenuButton fx:id="AssessortButton" layoutX="187.0" layoutY="300.0" mnemonicParsing="false" text="Add Assessors" />

        <TextField fx:id="imagePathField" GridPane.columnIndex="1" GridPane.rowIndex="4" />
        <Button layoutX="14.0" layoutY="500.0" mnemonicParsing="false" onAction="#handleImageUpload" text="Upload Image" />

        <HBox layoutX="14.0" layoutY="540.0" spacing="10.0">
            <children>
                <Button mnemonicParsing="false" onAction="#handleSave" text="Save" />
                <Button mnemonicParsing="false" onAction="#handleUpdate" text="Update" />
                <Button mnemonicParsing="false" onAction="#handleDelete" text="Delete" />
            </children>
        </HBox>
    </children>
</AnchorPane>