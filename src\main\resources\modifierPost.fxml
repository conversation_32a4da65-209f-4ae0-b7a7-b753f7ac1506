<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.AnchorPane?>

<AnchorPane prefHeight="400.0" prefWidth="600.0" xmlns="http://javafx.com/javafx/8"
            xmlns:fx="http://javafx.com/fxml/1"
            fx:controller="Controllers.ModifierPostController">

   <children>
      <Label layoutX="40.0" layoutY="25.0" text="Description du post:" />
      <TextField fx:id="descriptionTxt" layoutX="180.0" layoutY="20.0" prefWidth="350.0" />

      <Label layoutX="40.0" layoutY="70.0" text="Image actuelle:" />
      <Label fx:id="currentImageLabel" layoutX="180.0" layoutY="70.0" text="Aucune image" />

      <Button fx:id="btnChoisirImage" layoutX="180.0" layoutY="100.0"
              mnemonicParsing="false" onAction="#choisirImage"
              text="Changer l'image" />

      <ImageView fx:id="imageView" fitHeight="150.0" fitWidth="200.0"
                 layoutX="180.0" layoutY="140.0" pickOnBounds="true" preserveRatio="true" />

      <Button fx:id="btnModifier" layoutX="180.0" layoutY="320.0"
              mnemonicParsing="false" onAction="#modifierPost" text="Modifier le Post" />
              
      <Button fx:id="btnAnnuler" layoutX="320.0" layoutY="320.0"
              mnemonicParsing="false" onAction="#annuler" text="Annuler" />
   </children>
</AnchorPane> 