package Controllers;

import entity.Commentaire;
import entity.Post;
import javafx.event.ActionEvent;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.control.Alert;
import javafx.scene.control.Button;
import javafx.scene.control.TextArea;
import javafx.stage.Stage;
import service.CommentaireService;
import service.PostService;

import java.util.List;

public class ModifierCommentaireController {
    @FXML
    private TextArea contenuTxt;
    @FXML
    private Button btnModifier;
    @FXML
    private Button btnAnnuler;

    private Commentaire commentaire;
    private CommentaireService commentaireService;
    private PostService postService;
    private int postId; // Store the post ID separately

    public void initialize() {
        commentaireService = new CommentaireService();
        postService = new PostService();
    }

    public void setCommentaire(Commentaire commentaire) {
        this.commentaire = commentaire;
        this.postId = commentaire.getPost_id(); // Store the post ID when setting the comment
        contenuTxt.setText(commentaire.getContenu());
    }

    @FXML
    private void modifierCommentaire(ActionEvent event) {
        String nouveauContenu = contenuTxt.getText().trim();
        
        if (nouveauContenu.isEmpty()) {
            showAlert(Alert.AlertType.ERROR, "Erreur", "Le contenu du commentaire ne peut pas être vide.");
            return;
        }

        try {
            commentaireService.modifierCommentaire(commentaire.getId(), nouveauContenu);
            showAlert(Alert.AlertType.INFORMATION, "Succès", "Commentaire modifié avec succès.");
            navigateBackToComments();
        } catch (Exception e) {
            showAlert(Alert.AlertType.ERROR, "Erreur", "Erreur lors de la modification du commentaire: " + e.getMessage());
        }
    }

    @FXML
    private void annuler(ActionEvent event) {
        navigateBackToComments();
    }

    private void navigateBackToComments() {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/afficherCommentaire.fxml"));
            Parent root = loader.load();
            AfficherCommentaireController controller = loader.getController();
            
            // Get the post using the stored postId
            List<Post> posts = postService.recuperer();
            Post targetPost = null;
            
            for (Post post : posts) {
                if (post.getId() == postId) {
                    targetPost = post;
                    break;
                }
            }
            
            if (targetPost == null) {
                // If post not found, try to get it directly from the comment
                targetPost = commentaire.getPost();
            }
            
            if (targetPost == null) {
                throw new Exception("Impossible de trouver le post associé au commentaire. ID du post: " + postId);
            }
            
            controller.setPost(targetPost);
            Stage stage = (Stage) btnAnnuler.getScene().getWindow();
            stage.setScene(new Scene(root));
        } catch (Exception e) {
            showAlert(Alert.AlertType.ERROR, "Erreur", "Erreur lors de la navigation: " + e.getMessage());
        }
    }

    private void showAlert(Alert.AlertType alertType, String title, String content) {
        Alert alert = new Alert(alertType);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(content);
        alert.showAndWait();
    }
} 