<?xml version="1.0" encoding="UTF-8"?>
<?import javafx.scene.control.*?>
<?import javafx.scene.image.Image?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<StackPane prefHeight="600.0" prefWidth="900.0"
           style="-fx-background-color: linear-gradient(to bottom, #2a2a2a, #1e1e1e);"
           xmlns="http://javafx.com/javafx/23.0.1"
           xmlns:fx="http://javafx.com/fxml/1"
           fx:controller="Controllers.ForgotPasswordController">

    <VBox alignment="CENTER" maxHeight="500" maxWidth="400" spacing="20"
          style="-fx-padding: 40; -fx-background-color: #282828; -fx-background-radius: 10;
                 -fx-border-color: #c49b63; -fx-border-width: 1; -fx-border-radius: 10;
                 -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 15, 0, 0, 5);">

        <!-- Logo -->
        <ImageView fitHeight="80" fitWidth="80" preserveRatio="true">
            <Image url="@images/logo.png" />
        </ImageView>

        <!-- Titre -->
        <VBox alignment="CENTER" spacing="5">
            <Label style="-fx-font-size: 24px; -fx-font-weight: bold; -fx-text-fill: #c49b63;" text="Mot de passe oublié ?" />
            <Label style="-fx-font-size: 14px; -fx-text-fill: #95a5a6;" text="Saisissez votre email pour recevoir un code" />
        </VBox>

        <!-- Champ email -->
        <VBox maxWidth="350" spacing="5">
            <Label style="-fx-text-fill: #c49b63; -fx-font-size: 12px;" text="Adresse email" />
            <TextField fx:id="emailField" promptText="Votre adresse email"
                       style="-fx-background-radius: 5; -fx-padding: 12; -fx-background-color: #1e1e1e;
                              -fx-border-color: #c49b63; -fx-border-radius: 5; -fx-text-fill: white;" />
        </VBox>

        <!-- Bouton -->
        <Button maxWidth="350" text="Envoyer le code" onAction="#handleSendCode"
                style="-fx-background-color: #c49b63; -fx-text-fill: black;
                       -fx-font-weight: bold; -fx-background-radius: 5; -fx-padding: 12;" />

    </VBox>
</StackPane>
