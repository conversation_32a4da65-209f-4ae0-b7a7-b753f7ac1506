package Controllers;

import javafx.animation.PauseTransition;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.stage.Stage;
import service.UserService;

public class ResetPasswordController {

    private String resetCode;

    @FXML
    private PasswordField newPasswordField;

    @FXML
    private PasswordField confirmPasswordField;

    @FXML
    private Label messageLabel;

    private final UserService userService = new UserService();

    public void setResetCode(String code) {
        this.resetCode = code;
    }

    @FXML
    public void handleResetPassword() {
        String password = newPasswordField.getText().trim();
        String confirm = confirmPasswordField.getText().trim();

        if (password.isEmpty() || confirm.isEmpty()) {
            messageLabel.setText("Tous les champs sont requis.");
            return;
        }

        if (!password.equals(confirm)) {
            messageLabel.setText("Les mots de passe ne correspondent pas.");
            return;
        }

        try {
            boolean result = userService.resetPasswordWithCode(resetCode, password);
            if (result) {
                // Message de succès
                messageLabel.setText("✅ Mot de passe réinitialisé.");

                // Petite pause visuelle optionnelle (sinon saute directement à la redirection)
                PauseTransition pause = new javafx.animation.PauseTransition(javafx.util.Duration.seconds(1.5));
                pause.setOnFinished(event -> {
                    try {
                        // Charger la vue de connexion
                        FXMLLoader loader = new FXMLLoader(getClass().getResource("/Login.fxml"));
                        Parent root = loader.load();

                        // Ouvrir la scène login
                        Stage stage = new Stage();
                        stage.setTitle("Connexion");
                        stage.setScene(new Scene(root));
                        stage.show();

                        // Fermer la fenêtre actuelle
                        ((Stage) messageLabel.getScene().getWindow()).close();
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                });
                pause.play();
            } else {
                messageLabel.setText("❌ Code invalide ou expiré.");
            }
        } catch (Exception e) {
            e.printStackTrace();
            messageLabel.setText("Erreur serveur.");
        }
    }

}
