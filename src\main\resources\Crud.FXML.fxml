<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>
<?import javafx.geometry.*?>
<?import javafx.scene.image.*?>
<?import javafx.collections.FXCollections?>

<?import java.lang.String?>
<VBox xmlns="http://javafx.com/javafx/23.0.1" xmlns:fx="http://javafx.com/fxml/1"
      fx:controller="Controllers.AjouterEvenementController"
      spacing="20" style="-fx-padding: 25; -fx-background-color: #f8f8f8;" prefWidth="900" prefHeight="700">

   <!-- Header with icon -->
   <Label text="🎉 Ajouter un Événement" style="-fx-font-size: 24; -fx-font-weight: bold; -fx-text-fill: #915f3a;">
      <padding>
         <Insets bottom="10"/>
      </padding>
   </Label>

   <!-- Main content container -->
   <GridPane hgap="15" vgap="15" style="-fx-background-color: white; -fx-background-radius: 8; -fx-padding: 20; -fx-border-color: #915f3a; -fx-border-radius: 8; -fx-border-width: 2;">
      <padding>
         <Insets top="15" right="15" bottom="15" left="15"/>
      </padding>

      <!-- Left Column -->
      <Label text="🔹 Informations de Base" GridPane.rowIndex="0" GridPane.columnIndex="0" style="-fx-font-weight: bold; -fx-font-size: 14; -fx-text-fill: #915f3a;"/>

      <!-- Titre Événement -->
      <Label text="Titre de l'événement:" GridPane.rowIndex="1" GridPane.columnIndex="0" style="-fx-font-weight: bold;"/>
      <TextArea fx:id="TitreEve" GridPane.rowIndex="1" GridPane.columnIndex="1" prefWidth="250" prefHeight="35" promptText="Entrez le titre" style="-fx-background-radius: 4;"/>

      <!-- Type d'événement -->
      <Label text="Type d'événement:" GridPane.rowIndex="2" GridPane.columnIndex="0" style="-fx-font-weight: bold;"/>
      <ComboBox fx:id="TypeEve" GridPane.rowIndex="2" GridPane.columnIndex="1" prefWidth="250">
         <items>
            <FXCollections fx:factory="observableArrayList">
               <String fx:value="Ateliers"/>
               <String fx:value="Conférences"/>
               <String fx:value="Événements culturels"/>
               <String fx:value="Événements éducatifs"/>
               <String fx:value="Événements de réseautage"/>
            </FXCollections>
         </items>
      </ComboBox>

      <!-- Date d'événement -->
      <Label text="Date de l'événement:" GridPane.rowIndex="3" GridPane.columnIndex="0" style="-fx-font-weight: bold;"/>
      <DatePicker fx:id="Dateeve" GridPane.rowIndex="3" GridPane.columnIndex="1" prefWidth="250" style="-fx-background-radius: 4;"/>

      <!-- Prix événement -->
      <Label text="Prix (DT):" GridPane.rowIndex="4" GridPane.columnIndex="0" style="-fx-font-weight: bold;"/>
      <TextArea fx:id="PrixEve" GridPane.rowIndex="4" GridPane.columnIndex="1" prefWidth="250" prefHeight="35" promptText="00.00" style="-fx-background-radius: 4;"/>

      <!-- Right Column -->
      <Label text="🔹 Détails Supplémentaires" GridPane.rowIndex="0" GridPane.columnIndex="2" style="-fx-font-weight: bold; -fx-font-size: 14; -fx-text-fill: #915f3a;"/>

      <!-- Description -->
      <Label text="Description:" GridPane.rowIndex="1" GridPane.columnIndex="2" style="-fx-font-weight: bold;"/>
      <TextArea fx:id="DescEve" GridPane.rowIndex="1" GridPane.columnIndex="3" prefWidth="250" prefHeight="80" wrapText="true" promptText="Description..." style="-fx-background-radius: 4;"/>

      <!-- Capacité Maximale -->
      <Label text="Capacité Maximale:" GridPane.rowIndex="2" GridPane.columnIndex="2" style="-fx-font-weight: bold;"/>
      <TextArea fx:id="CapEve" GridPane.rowIndex="2" GridPane.columnIndex="3" prefWidth="250" prefHeight="35" promptText="Nombre maximum" style="-fx-background-radius: 4;"/>

      <!-- Image événement -->
      <Label text="Image URL:" GridPane.rowIndex="3" GridPane.columnIndex="2" style="-fx-font-weight: bold;"/>
      <TextArea fx:id="ImageEve" GridPane.rowIndex="3" GridPane.columnIndex="3" prefWidth="250" prefHeight="35" promptText="URL de l'image" style="-fx-background-radius: 4;"/>

      <!-- Image Preview -->
      <Label text="Aperçu de l'image:" GridPane.rowIndex="4" GridPane.columnIndex="2" style="-fx-font-weight: bold;"/>
      <ImageView fx:id="imagePreview" GridPane.rowIndex="4" GridPane.columnIndex="3" fitWidth="150" fitHeight="100" preserveRatio="true" style="-fx-border-color: #e0e0e0; -fx-border-radius: 4;"/>

      <!-- Column spans -->
      <columnConstraints>
         <ColumnConstraints hgrow="NEVER" minWidth="120"/>
         <ColumnConstraints hgrow="SOMETIMES" minWidth="250"/>
         <ColumnConstraints hgrow="NEVER" minWidth="120"/>
         <ColumnConstraints hgrow="SOMETIMES" minWidth="250"/>
      </columnConstraints>

      <rowConstraints>
         <RowConstraints vgrow="NEVER" minHeight="30"/>
         <RowConstraints vgrow="NEVER" minHeight="35"/>
         <RowConstraints vgrow="NEVER" minHeight="35"/>
         <RowConstraints vgrow="NEVER" minHeight="35"/>
         <RowConstraints vgrow="NEVER" minHeight="35"/>
         <RowConstraints vgrow="NEVER" minHeight="35"/>
      </rowConstraints>
   </GridPane>

   <!-- Button Panel - using HBox with proper alignment -->
   <HBox spacing="20" style="-fx-padding: 20 0 0 0;">
      <children>
         <Button fx:id="btnRetour" text="⬅ Retour" onAction="#Retour" style="-fx-background-color: #e0e0e0; -fx-text-fill: #333; -fx-font-weight: bold; -fx-padding: 8 20; -fx-background-radius: 4;"/>
         <Region HBox.hgrow="ALWAYS"/>
         <Button fx:id="btnSave" text="✓ Enregistrer" onAction="#Enregistrer" style="-fx-background-color: #915f3a; -fx-text-fill: white; -fx-font-weight: bold; -fx-padding: 8 25; -fx-background-radius: 4;"/>
         <Region HBox.hgrow="ALWAYS"/>
         <Button fx:id="btnclear" text="♻ Réinitialiser" onAction="#Rénitialiser" style="-fx-background-color: #f0f0f0; -fx-text-fill: #333; -fx-font-weight: bold; -fx-padding: 8 20; -fx-background-radius: 4; -fx-border-color: #ccc; -fx-border-radius: 4;"/>
      </children>
   </HBox>
</VBox>