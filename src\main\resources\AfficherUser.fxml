<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.ScrollPane?>
<?import javafx.scene.image.Image?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.GridPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.Region?>
<?import javafx.scene.layout.VBox?>

<AnchorPane prefHeight="600.0" prefWidth="800.0" style="-fx-background-color: linear-gradient(to bottom right, #2193b0, #6dd5ed); -fx-padding: 20;" xmlns="http://javafx.com/javafx/23.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="Controllers.AfficherUser">
    <children>

        <VBox layoutX="20" layoutY="20" spacing="25" AnchorPane.bottomAnchor="20.0" AnchorPane.leftAnchor="20.0" AnchorPane.rightAnchor="20.0" AnchorPane.topAnchor="20.0">

            <!-- Header Bar -->
            <HBox alignment="CENTER_LEFT" spacing="20" style="-fx-background-color: white; -fx-padding: 20; -fx-background-radius: 15; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 15, 0, 0, 5);">
                <ImageView fitHeight="32" fitWidth="32" preserveRatio="true">
                    <Image url="@/images/users-icon.png" />
                </ImageView>
                <VBox spacing="2">
                    <Label style="-fx-font-size: 24px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;" text="User Management" />
                    <Label style="-fx-font-size: 13px; -fx-text-fill: #95a5a6;" text="Manage and monitor user accounts" />
                </VBox>
                <Region fx:id="spacer" HBox.hgrow="ALWAYS" />
                <Button onAction="#openAddUserWindow" style="-fx-background-color: #3498db; -fx-text-fill: white; -fx-font-weight: bold;                                 -fx-background-radius: 8; -fx-padding: 10 20; -fx-cursor: hand;                                 -fx-effect: dropshadow(gaussian, rgba(52,152,219,0.3), 8, 0, 0, 4);" text="Add New User">
                    <graphic>
                        <ImageView fitHeight="24.0" fitWidth="51.0" preserveRatio="true">
                            <Image url="@images/add-user-icon.png" />
                        </ImageView>
                    </graphic>
                </Button>
                <Button onAction="#refrsh" style="-fx-background-color: #f8f9fa; -fx-text-fill: #2c3e50; -fx-font-weight: bold;                                 -fx-background-radius: 8; -fx-padding: 10 20; -fx-cursor: hand;                                 -fx-border-color: #e9ecef; -fx-border-radius: 8;" text="Refresh">
                    <graphic>
                        <ImageView fitHeight="20.0" fitWidth="24.0" preserveRatio="true">
                            <Image url="@images/refresh-icon.png" />
                        </ImageView>
                    </graphic>
                </Button>
            </HBox>

            <!-- Scrollable User Grid -->
            <ScrollPane fitToHeight="true" fitToWidth="true" style="-fx-background-color: transparent; -fx-background: transparent; -fx-border-width: 0;" VBox.vgrow="ALWAYS">
                <content>
                    <GridPane fx:id="userGrid" hgap="20"  style="-fx-padding: 25;    -fx-background-color: white;                                      -fx-background-radius: 15;                                      -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 15, 0, 0, 5);" vgap="20">
                        <padding>
                            <Insets bottom="10" left="10" right="10" top="10" />
                        </padding>
                    </GridPane>
                </content>
                <padding>
                    <Insets bottom="5" left="5" right="5" top="5" />
                </padding>
            </ScrollPane>
        </VBox>
    </children>
</AnchorPane>
