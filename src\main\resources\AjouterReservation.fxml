<?xml version="1.0" encoding="UTF-8"?>

<?import java.lang.String?>
<?import javafx.collections.FXCollections?>
<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.ComboBox?>
<?import javafx.scene.control.DatePicker?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.Spinner?>
<?import javafx.scene.control.SpinnerValueFactory.IntegerSpinnerValueFactory?>
<?import javafx.scene.layout.ColumnConstraints?>
<?import javafx.scene.layout.GridPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.RowConstraints?>
<?import javafx.scene.layout.VBox?>

<VBox prefHeight="342.0" prefWidth="682.0" spacing="15" style="-fx-padding: 20; -fx-background-color: #282828;" xmlns="http://javafx.com/javafx/23.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="Controllers.AjouterReservationController">

    <Label style="-fx-font-size: 20; -fx-font-weight: bold; -fx-text-fill: #c98a56;" text="📝 Nouvelle Réservation" />

    <GridPane hgap="10" style="-fx-background-color: #282828; -fx-background-radius: 8; -fx-padding: 20; -fx-border-color: #c98a56; -fx-border-radius: 8; -fx-border-width: 1.5;" vgap="15">
        <padding><Insets bottom="15" left="15" right="15" top="15" /></padding>

        <!-- Event Selection -->
        <Label style="-fx-font-weight: bold;-fx-text-fill: #f4f4f4; -fx-font-size: 14px;" text="🎭 Événement:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
        <ComboBox fx:id="eventComboBox" prefWidth="250" GridPane.columnIndex="1" GridPane.rowIndex="0" />

        <!-- Booking Date -->
        <Label style="-fx-font-weight: bold;-fx-text-fill: #f4f4f4; -fx-font-size: 14px;" text="📅 Date de réservation:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
        <DatePicker fx:id="datePicker" GridPane.columnIndex="1" GridPane.rowIndex="1" />

        <!-- Number of Places -->
        <Label style="-fx-font-weight: bold;-fx-text-fill: #f4f4f4; -fx-font-size: 14px;" text="🧮 Nombre de places:" GridPane.columnIndex="0" GridPane.rowIndex="2" />
        <Spinner fx:id="placesSpinner" editable="true"
                 style="-fx-background-color: white; -fx-border-color: #cccccc; -fx-border-radius: 4;"
                 GridPane.columnIndex="1" GridPane.rowIndex="2"/>


        <!-- Payment Method -->
        <Label style="-fx-font-weight: bold;-fx-text-fill: #f4f4f4; -fx-font-size: 14px;" text="💳 Moyen de paiement:" GridPane.columnIndex="0" GridPane.rowIndex="3" />
        <ComboBox fx:id="paymentComboBox" GridPane.columnIndex="1" GridPane.rowIndex="3">
            <items>
                <FXCollections fx:factory="observableArrayList">
                    <String fx:value="Espèces" />
                    <String fx:value="Carte bancaire" />
                    <String fx:value="Virement" />
                    <String fx:value="Chèque" />
                </FXCollections>
            </items>
        </ComboBox>
      <columnConstraints>
         <ColumnConstraints />
         <ColumnConstraints />
      </columnConstraints>
      <rowConstraints>
         <RowConstraints />
         <RowConstraints />
         <RowConstraints />
         <RowConstraints />
      </rowConstraints>
    </GridPane>

    <HBox alignment="CENTER_RIGHT" spacing="15">
        <Button onAction="#handleCancel" style="-fx-background-color: #b94e4e; -fx-text-fill: white; -fx-font-weight: bold; -fx-padding: 8 20; -fx-background-radius: 4;" text="❌ Annuler" />
        <Button onAction="#handleSave" style="-fx-background-color: #b97b4c; -fx-text-fill: white; -fx-font-weight: bold; -fx-padding: 8 20; -fx-background-radius: 4;" text="✅ Enregistrer" />
    </HBox>
</VBox>
