package service;

import com.stripe.Stripe;
import com.stripe.exception.StripeException;
import com.stripe.model.PaymentIntent;
import com.stripe.param.PaymentIntentCreateParams;

import java.util.HashMap;
import java.util.Map;

public class PaymentService {
    private static final String STRIPE_SECRET_KEY = "sk_test_51RJhZfFLKMXwd88yokLGgXxjULL8uWLNPO5PQg7pTmDxtLzOFO7f7lw8rdh0PpmFgFTthN2q4kdgm6rA0R3Zy4CA00EXPKcJxH"; // Remplacez par votre clé secrète
    String publicKey = "pk_test_51RJhZfFLKMXwd88yk4r9kM6096s9uMI3GiJ7pthVglvSOcsGD71XtMhhJxQamIhvBKs0T7OxGGcOqFynlgGWdzCm00yhexWWbP"; // Pour le frontend
    public PaymentService() {
        Stripe.apiKey = STRIPE_SECRET_KEY;
    }

    public String createPaymentIntent(double amount, String currency, String description) throws StripeException {
        PaymentIntentCreateParams params = PaymentIntentCreateParams.builder()
                .setAmount((long) (amount * 100)) // Stripe utilise des centimes
                .setCurrency(currency.toLowerCase())
                .setDescription(description)
                .build();

        PaymentIntent intent = PaymentIntent.create(params);
        return intent.getClientSecret();
    }

}

