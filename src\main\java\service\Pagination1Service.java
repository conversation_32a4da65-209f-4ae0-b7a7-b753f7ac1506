package service;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import entity.Produit;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.util.List;

public class Pagination1Service {
    private static final String API_URL = "http://localhost:8080/api/produits";
    private final HttpClient httpClient = HttpClient.newHttpClient();
    private final Gson gson = new Gson();

    public List<Produit> fetchProduits(int page, int size) throws Exception {
        String url = API_URL + "?page=" + page + "&size=" + size;
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(url))
                .GET()
                .build();

        HttpResponse<String> response = httpClient.send(
                request, HttpResponse.BodyHandlers.ofString());

        if (response.statusCode() != 200) {
            throw new RuntimeException("Erreur API: " + response.body());
        }

        return gson.fromJson(response.body(), new TypeToken<List<Produit>>(){}.getType());
    }
}