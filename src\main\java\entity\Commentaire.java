package entity;

public class Commentaire {
    private int id;
    private int post_id;
    private User user;
    private String contenu;
    private String date_commentaire;
    private Post post;    // Relation Many-to-One avec Post
    private int nbr_like_commentaire;

    // Constructeur par défaut
    public Commentaire() {
    }

    // Constructeur avec post_id
    public Commentaire(int id, int post_id, User user, String contenu, String date_commentaire, int nbr_like_commentaire) {
        this.id = id;
        this.post_id = post_id;
        this.user = user;
        this.contenu = contenu;
        this.date_commentaire = date_commentaire;
        this.nbr_like_commentaire = nbr_like_commentaire;
    }

    // Constructeur avec objet Post
    public Commentaire(int id, String contenu, String date_commentaire, 
                      User user, Post post, int nbr_like_commentaire) {
        this.id = id;
        this.contenu = contenu;
        this.date_commentaire = date_commentaire;
        this.user= user;
        this.post = post;
        this.nbr_like_commentaire = nbr_like_commentaire;
        if (post != null) {
            this.post_id = post.getId();
        }
    }

    // Getters et Setters
    public int getId() { return id; }
    public void setId(int id) { this.id = id; }

    public String getContenu() { return contenu; }
    public void setContenu(String contenu) { this.contenu = contenu; }

    public String getDate_commentaire() { return date_commentaire; }
    public void setDate_commentaire(String date_commentaire) { 
        this.date_commentaire = date_commentaire; 
    }


    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public Post getPost() { return post; }
    public void setPost(Post post) { 
        this.post = post;
        if (post != null) {
            this.post_id = post.getId();
        }
    }

    public int getNbr_like_commentaire() { return nbr_like_commentaire; }
    public void setNbr_like_commentaire(int nbr_like_commentaire) { 
        this.nbr_like_commentaire = nbr_like_commentaire; 
    }

    public int getPost_id() { 
        return post_id; 
    }
    
    public void setPost_id(int post_id) {
        this.post_id = post_id;
    }

    @Override
    public String toString() {
        return "Commentaire{" +
                "id=" + id +
                ", contenu='" + contenu + '\'' +
                ", date_commentaire='" + date_commentaire + '\'' +
                ", post_id=" + post_id +
                ", nbr_like_commentaire=" + nbr_like_commentaire +
                '}';
    }
}



