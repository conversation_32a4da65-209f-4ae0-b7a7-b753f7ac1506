<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.chart.*?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<VBox xmlns="http://javafx.com/javafx/17.0.2"
      xmlns:fx="http://javafx.com/fxml/1"
      fx:controller="Controllers.StatBack"
      spacing="20"
      style="-fx-padding: 20;">

    <Label text="Product Statistics by Type"
           style="-fx-font-size: 20px; -fx-font-weight: bold;"/>

    <TabPane>

        <!-- Count by Type Tab -->
        <Tab text="Count by Type">
            <VBox spacing="15">
                <HBox spacing="20">
                    <VBox spacing="10">
                        <TitledPane text="Pie Chart" expanded="true">
                            <content>
                                <PieChart fx:id="countPieChart" prefHeight="300" prefWidth="500"/>
                            </content>
                        </TitledPane>
                    </VBox>
                    <VBox fx:id="countLegendBox" spacing="10" style="-fx-padding: 10;"/>
                </HBox>

                <TitledPane text="Vertical Bar Chart" expanded="true">
                    <content>
                        <BarChart fx:id="countChartVertical"
                                  title="Number of Products by Type"
                                  prefHeight="300" prefWidth="700">
                            <xAxis>
                                <CategoryAxis label="Product Type"/>
                            </xAxis>
                            <yAxis>
                                <NumberAxis label="Count"/>
                            </yAxis>
                        </BarChart>
                    </content>
                </TitledPane>

                <TitledPane text="Horizontal Bar Chart" expanded="true">
                    <content>
                        <BarChart fx:id="countChartHorizontal"
                                  title="Number of Products by Type"
                                  prefHeight="300" prefWidth="700">
                            <xAxis>
                                <NumberAxis label="Count"/>
                            </xAxis>
                            <yAxis>
                                <CategoryAxis label="Product Type"/>
                            </yAxis>
                        </BarChart>
                    </content>
                </TitledPane>
            </VBox>
        </Tab>

        <!-- Average Price Tab -->
        <Tab text="Average Price">
            <VBox spacing="15">
                <HBox spacing="20">
                    <VBox spacing="10">
                        <TitledPane text="Pie Chart" expanded="true">
                            <content>
                                <PieChart fx:id="pricePieChart" prefHeight="300" prefWidth="500"/>
                            </content>
                        </TitledPane>
                    </VBox>
                    <VBox fx:id="priceLegendBox" spacing="10" style="-fx-padding: 10;"/>
                </HBox>

                <TitledPane text="Vertical Bar Chart" expanded="true">
                    <content>
                        <BarChart fx:id="priceChartVertical"
                                  title="Average Price by Product Type"
                                  prefHeight="300" prefWidth="700">
                            <xAxis>
                                <CategoryAxis label="Product Type"/>
                            </xAxis>
                            <yAxis>
                                <NumberAxis label="Price"/>
                            </yAxis>
                        </BarChart>
                    </content>
                </TitledPane>

                <TitledPane text="Horizontal Bar Chart" expanded="true">
                    <content>
                        <BarChart fx:id="priceChartHorizontal"
                                  title="Average Price by Product Type"
                                  prefHeight="300" prefWidth="700">
                            <xAxis>
                                <NumberAxis label="Price"/>
                            </xAxis>
                            <yAxis>
                                <CategoryAxis label="Product Type"/>
                            </yAxis>
                        </BarChart>
                    </content>
                </TitledPane>
            </VBox>
        </Tab>

        <!-- Total Stock Tab -->
        <Tab text="Total Stock">
            <VBox spacing="15">
                <HBox spacing="20">
                    <VBox spacing="10">
                        <TitledPane text="Pie Chart" expanded="true">
                            <content>
                                <PieChart fx:id="stockPieChart" prefHeight="300" prefWidth="500"/>
                            </content>
                        </TitledPane>
                    </VBox>
                    <VBox fx:id="stockLegendBox" spacing="10" style="-fx-padding: 10;"/>
                </HBox>

                <TitledPane text="Vertical Bar Chart" expanded="true">
                    <content>
                        <BarChart fx:id="stockChartVertical"
                                  title="Total Stock by Product Type"
                                  prefHeight="300" prefWidth="700">
                            <xAxis>
                                <CategoryAxis label="Product Type"/>
                            </xAxis>
                            <yAxis>
                                <NumberAxis label="Stock"/>
                            </yAxis>
                        </BarChart>
                    </content>
                </TitledPane>

                <TitledPane text="Horizontal Bar Chart" expanded="true">
                    <content>
                        <BarChart fx:id="stockChartHorizontal"
                                  title="Total Stock by Product Type"
                                  prefHeight="300" prefWidth="700">
                            <xAxis>
                                <NumberAxis label="Stock"/>
                            </xAxis>
                            <yAxis>
                                <CategoryAxis label="Product Type"/>
                            </yAxis>
                        </BarChart>
                    </content>
                </TitledPane>
            </VBox>
        </Tab>

        <!-- Price Range Tab -->
        <Tab text="Price Range">
            <VBox spacing="15">
                <HBox spacing="20">
                    <VBox spacing="10">
                        <TitledPane text="Pie Chart" expanded="true">
                            <content>
                                <PieChart fx:id="rangePieChart" prefHeight="300" prefWidth="500"/>
                            </content>
                        </TitledPane>
                    </VBox>
                    <VBox fx:id="rangeLegendBox" spacing="10" style="-fx-padding: 10;"/>
                </HBox>

                <TitledPane text="Vertical Bar Chart" expanded="true">
                    <content>
                        <BarChart fx:id="rangeChartVertical"
                                  title="Price Range by Product Type"
                                  prefHeight="300" prefWidth="700">
                            <xAxis>
                                <CategoryAxis label="Product Type"/>
                            </xAxis>
                            <yAxis>
                                <NumberAxis label="Price Range"/>
                            </yAxis>
                        </BarChart>
                    </content>
                </TitledPane>

                <TitledPane text="Horizontal Bar Chart" expanded="true">
                    <content>
                        <BarChart fx:id="rangeChartHorizontal"
                                  title="Price Range by Product Type"
                                  prefHeight="300" prefWidth="700">
                            <xAxis>
                                <NumberAxis label="Price Range"/>
                            </xAxis>
                            <yAxis>
                                <CategoryAxis label="Product Type"/>
                            </yAxis>
                        </BarChart>
                    </content>
                </TitledPane>
            </VBox>
        </Tab>
    </TabPane>
</VBox>