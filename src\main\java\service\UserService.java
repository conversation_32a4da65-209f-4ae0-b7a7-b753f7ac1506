package service;

import entity.User;
import tools.MyDataBase;

import java.sql.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

public class UserService {

    private final Connection cnx;

    public UserService() {
        cnx = MyDataBase.getInstance().getCnx();
    }

    // ADD USER
    public void ajouter(User user) throws SQLException {
        String sql = "INSERT INTO user(role_user, nom_user, prenom_user, email_user, password) " +
                "VALUES (?, ?, ?, ?, ?)";
        try (PreparedStatement ste = cnx.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {
            ste.setString(1, user.getRole_user());
            ste.setString(2, user.getNom_user());
            ste.setString(3, user.getPrenom_user());
            ste.setString(4, user.getEmail_user());
            ste.setString(5, user.getPassword());

            int rowsInserted = ste.executeUpdate();
            if (rowsInserted > 0) {
                try (ResultSet rs = ste.getGeneratedKeys()) {
                    if (rs.next()) {
                        user.setId(rs.getInt(1));
                        System.out.println("✅ User added with ID: " + user.getId());
                    }
                }
            }
        }
    }

    // UPDATE USER
    public void modifier(User user) throws SQLException {
        String sql = "UPDATE user SET telephone_user = ?, role_user = ?, nom_user = ?, prenom_user = ?, " +
                "email_user = ?, adresse = ?, photo_user = ?, date_naissance_user = ?, password = ? WHERE id = ?";
        try (PreparedStatement ste = cnx.prepareStatement(sql)) {
            ste.setInt(1, user.getTelephone_user());
            ste.setString(2, user.getRole_user());
            ste.setString(3, user.getNom_user());
            ste.setString(4, user.getPrenom_user());
            ste.setString(5, user.getEmail_user());
            ste.setString(6, user.getAdresse());
            ste.setString(7, user.getPhoto_user());
            ste.setDate(8, user.getDate_naissance_user());
            ste.setString(9, user.getPassword());
            ste.setInt(10, user.getId());

            int rowsUpdated = ste.executeUpdate();
            System.out.println(rowsUpdated > 0 ? "✅ User updated." : "❌ Update failed.");
        }
    }

    // DELETE USER
    public void supprimer(User user) throws SQLException {
        String sql = "DELETE FROM user WHERE id = ?";
        try (PreparedStatement ste = cnx.prepareStatement(sql)) {
            ste.setInt(1, user.getId());
            int rowsDeleted = ste.executeUpdate();
            System.out.println(rowsDeleted > 0 ? "✅ User deleted." : "❌ No user found.");
        }
    }

    // GET ALL USERS
    public List<User> recuperer() throws SQLException {
        List<User> users = new ArrayList<>();
        String sql = "SELECT * FROM user";
        try (Statement st = cnx.createStatement(); ResultSet rs = st.executeQuery(sql)) {
            while (rs.next()) {
                User user = new User(
                        rs.getInt("id"),
                        rs.getInt("telephone_user"),

                        rs.getString("role_user"),
                        rs.getString("nom_user"),
                        rs.getString("prenom_user"),
                        rs.getString("email_user"),
                        rs.getString("adresse"),
                        rs.getString("photo_user"),
                        rs.getDate("date_naissance_user"),
                        rs.getString("password"));
                users.add(user);
            }
        }
        return users;
    }

    // FIND USER BY ID
    public User getById(int id) throws SQLException {
        String sql = "SELECT * FROM user WHERE id = ?";
        try (PreparedStatement pst = cnx.prepareStatement(sql)) {
            pst.setInt(1, id);
            try (ResultSet rs = pst.executeQuery()) {
                if (rs.next()) {
                    return new User(
                            rs.getInt("id"),
                            rs.getInt("telephone_user"),

                            rs.getString("role_user"),
                            rs.getString("nom_user"),
                            rs.getString("prenom_user"),
                            rs.getString("email_user"),
                            rs.getString("adresse"),
                            rs.getString("photo_user"),
                            rs.getDate("date_naissance_user"),
                            rs.getString("password"));
                }
            }
        }
        return null;
    }

    // LOGIN
    public User login(String email, String password) throws SQLException {
        String sql = "SELECT * FROM user WHERE email_user = ? AND password = ?";
        try (PreparedStatement pst = cnx.prepareStatement(sql)) {
            pst.setString(1, email);
            pst.setString(2, password);
            try (ResultSet rs = pst.executeQuery()) {
                if (rs.next()) {
                    User u = new User();
                    u.setId(rs.getInt("id"));
                    u.setTelephone_user(rs.getInt("telephone_user"));
                    u.setRole_user(rs.getString("role_user"));
                    u.setNom_user(rs.getString("nom_user"));
                    u.setPrenom_user(rs.getString("prenom_user"));
                    u.setEmail_user(rs.getString("email_user"));
                    u.setAdresse(rs.getString("adresse"));
                    u.setPhoto_user(rs.getString("photo_user"));
                    u.setDate_naissance_user(rs.getDate("date_naissance_user"));
                    u.setPassword(rs.getString("password"));
                    u.setIs_verified(rs.getInt("is_verified"));
                    return u;
                }
            }
        }
        return null;
    }

    public void signup(User user) throws SQLException {
        String sql = "INSERT INTO user(telephone_user, role_user, nom_user, prenom_user, email_user, adresse, photo_user, date_naissance_user, password) "
                +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
        try (PreparedStatement ste = cnx.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {
            ste.setInt(1, user.getTelephone_user());
            ste.setString(2, user.getRole_user());
            ste.setString(3, user.getNom_user());
            ste.setString(4, user.getPrenom_user());
            ste.setString(5, user.getEmail_user());
            ste.setString(6, user.getAdresse());
            ste.setString(7, user.getPhoto_user());
            ste.setDate(8, user.getDate_naissance_user());
            ste.setString(9, user.getPassword());

            int rowsInserted = ste.executeUpdate();
            if (rowsInserted > 0) {
                try (ResultSet rs = ste.getGeneratedKeys()) {
                    if (rs.next()) {
                        user.setId(rs.getInt(1));
                        System.out.println("✅ User added with ID: " + user.getId());
                    }
                }
            }
        }
    }

    public User getByUsername(String username) throws SQLException {
        String sql = "SELECT * FROM user WHERE username=?";

        try (PreparedStatement st = cnx.prepareStatement(sql)) {
            st.setString(1, username);

            try (ResultSet rs = st.executeQuery()) {
                if (rs.next()) {
                    User user = new User();
                    user.setId(rs.getInt("id"));
                    // user.getNom_user(rs.getString("username"));
                    // set other fields as needed
                    return user;
                }
            }
        }
        return null;
    }

    public User getUserById(int id) throws SQLException {
        System.out.println("Fetching user with ID: " + id); // Debug

        if (cnx == null || cnx.isClosed()) {
            System.out.println("Database connection is invalid!"); // Debug
            throw new SQLException("No database connection");
        }

        String sql = "SELECT * FROM user WHERE id = ?";
        try (PreparedStatement ps = cnx.prepareStatement(sql)) {
            ps.setInt(1, id);
            ResultSet rs = ps.executeQuery();

            if (!rs.next()) {
                System.out.println("No user found in database for ID: " + id); // Debug
                return null;
            }

            System.out.println("User found in database"); // Debug
            User user = new User();
            user.setId(rs.getInt("id"));
            // Set other fields...
            return user;
        }
    }

    public List<User> getAllUsers() throws SQLException {
        String sql = "SELECT * FROM user";
        try (PreparedStatement ps = cnx.prepareStatement(sql)) {
            ResultSet rs = ps.executeQuery();
            List<User> users = new ArrayList<>();
            while (rs.next()) {
                User user = new User();
                user.setId(rs.getInt("id"));
                user.setNom_user(rs.getString("nom_user"));
                user.setPrenom_user(rs.getString("prenom_user"));
                // Set other fields...
                users.add(user);
            }
            return users;
        }
    }

    public User findByEmail(String email) throws SQLException {
        String query = "SELECT * FROM user WHERE email_user = ?";
        PreparedStatement ps = cnx.prepareStatement(query);
        ps.setString(1, email);
        ResultSet rs = ps.executeQuery();

        if (rs.next()) {
            User user = new User();
            user.setId(rs.getInt("id"));
            user.setEmail_user(rs.getString("email_user"));
            user.setNom_user(rs.getString("nom_user"));
            user.setPrenom_user(rs.getString("prenom_user"));
            user.setPassword(rs.getString("password"));
            user.setRole_user(rs.getString("role_user"));
            user.setIs_verified(rs.getInt("is_verified"));
            return user;
        }
        return null;
    }

    public boolean sendResetCode(String email) throws SQLException {
        User user = findByEmail(email);
        if (user == null)
            return false;

        String token = String.valueOf((int) (Math.random() * 900000) + 100000); // 6 chiffres
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime expiry = now.plusMinutes(15);

        // Supprimer les anciennes demandes
        String delete = "DELETE FROM reset_password_request WHERE user_id = ?";
        try (PreparedStatement psDel = cnx.prepareStatement(delete)) {
            psDel.setInt(1, user.getId());
            psDel.executeUpdate();
        }

        // Insérer une nouvelle demande
        String insert = "INSERT INTO reset_password_request (user_id, hashed_token, requested_at, expires_at) VALUES (?, ?, ?, ?)";
        try (PreparedStatement ps = cnx.prepareStatement(insert)) {
            ps.setInt(1, user.getId());
            ps.setString(2, token); // plus besoin de hash
            ps.setTimestamp(3, Timestamp.valueOf(now));
            ps.setTimestamp(4, Timestamp.valueOf(expiry));
            ps.executeUpdate();
        }

        // Envoyer l'email réel
        try {
            MailService.send(user.getEmail_user(), "Code de réinitialisation", "Votre code est : " + token);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return true;
    }

    public boolean verifyResetCode(String code) throws SQLException {
        String query = "SELECT * FROM reset_password_request WHERE hashed_token = ? AND expires_at > NOW()";
        try (PreparedStatement ps = cnx.prepareStatement(query)) {
            ps.setString(1, code);
            ResultSet rs = ps.executeQuery();
            return rs.next(); // true si code valide
        }
    }

    public boolean resetPasswordWithCode(String code, String newPassword) throws SQLException {
        String query = "SELECT * FROM reset_password_request WHERE expires_at > NOW() AND hashed_token = ?";
        try (PreparedStatement ps = cnx.prepareStatement(query)) {
            ps.setString(1, code);
            ResultSet rs = ps.executeQuery();

            if (rs.next()) {
                int userId = rs.getInt("user_id");

                // Mettre à jour le mot de passe
                String update = "UPDATE user SET password = ? WHERE id= ?";
                try (PreparedStatement psUpdate = cnx.prepareStatement(update)) {
                    psUpdate.setString(1, newPassword); // ou BCrypt si tu veux
                    psUpdate.setInt(2, userId);
                    psUpdate.executeUpdate();
                }

                // Supprimer la demande utilisée
                try (PreparedStatement psDel = cnx
                        .prepareStatement("DELETE FROM reset_password_request WHERE user_id = ?")) {
                    psDel.setInt(1, userId);
                    psDel.executeUpdate();
                }

                return true;
            }
        }
        return false;
    }

    public void sendVerificationLink(User user) {
        String verificationLink = "http://localhost/verify_email.php?id=" + user.getId();

        String message = """
                Bienvenue sur CaféCulture ☕

                Bonjour %s,

                Merci pour votre inscription ! Pour activer votre compte, cliquez sur le lien suivant :

                %s

                Si vous ne parvenez pas à cliquer, copiez ce lien dans votre navigateur.

                L'équipe CaféCulture
                """.formatted(user.getPrenom_user(), verificationLink);

        try {
            MailService.send(
                    user.getEmail_user(),
                    "✨ Activez votre compte CaféCulture",
                    message);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void signupGoogleUser(User user) throws SQLException {
        String sql = "INSERT INTO user(telephone_user, role_user, nom_user, prenom_user, email_user, adresse, photo_user, date_naissance_user, password, is_verified) "
                +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 1)";
        try (PreparedStatement ste = cnx.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {
            ste.setInt(1, user.getTelephone_user());
            ste.setString(2, user.getRole_user());
            ste.setString(3, user.getNom_user());
            ste.setString(4, user.getPrenom_user());
            ste.setString(5, user.getEmail_user());
            ste.setString(6, user.getAdresse());
            ste.setString(7, user.getPhoto_user() != null ? user.getPhoto_user() : "");
            ste.setDate(8, user.getDate_naissance_user());
            ste.setString(9, user.getPassword());

            ste.executeUpdate();
            try (ResultSet rs = ste.getGeneratedKeys()) {
                if (rs.next())
                    user.setId(rs.getInt(1));
            }
        }
    }

    public User getByIdM(int id) throws SQLException {
        String sql = "SELECT * FROM user WHERE id = ?";
        try (PreparedStatement pst = cnx.prepareStatement(sql)) {
            pst.setInt(1, id);
            try (ResultSet rs = pst.executeQuery()) {
                if (rs.next()) {
                    return resultSetToUser(rs);
                }
            }
        }
        return null;
    }

    private User resultSetToUser(ResultSet rs) throws SQLException {
        User user = new User();
        user.setId(rs.getInt("id"));
        user.setPassword(rs.getString("password"));
        user.setRole_user(rs.getString("role_user"));
        user.setNom_user(rs.getString("nom_user"));
        user.setPrenom_user(rs.getString("prenom_user"));
        user.setEmail_user(rs.getString("email_user"));
        user.setAdresse(rs.getString("adresse"));
        user.setTelephone_user(rs.getInt("telephone_user"));
        user.setPhoto_user(rs.getString("photo_user"));

        Date date = rs.getDate("date_naissance_user");
        if (date != null) {
            user.setDate_naissance_user(date);
        }

        return user;
    }

}