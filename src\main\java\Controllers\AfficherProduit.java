package Controllers;

import entity.Commande;
import entity.Produit;
import entity.User;
import entity.UserSession;
import javafx.animation.Animation;
import javafx.animation.KeyFrame;
import javafx.animation.Timeline;
import javafx.event.ActionEvent;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.geometry.Pos;
import javafx.scene.Node;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.scene.layout.HBox;
import javafx.scene.layout.TilePane;
import javafx.scene.layout.VBox;
import javafx.stage.Modality;
import javafx.stage.Stage;
import javafx.util.Duration;
import service.CommandeService;
import service.Pagination1Service; // Importez le nouveau service de pagination
import service.ProduitService;

import java.io.IOException;
import java.sql.SQLException;
import java.util.List;
import java.util.Locale;
import java.util.ResourceBundle;

public class AfficherProduit {

    private final ProduitService produitService = new ProduitService();
    private final CommandeService commandeService = new CommandeService();
    private final Pagination1Service paginationService = new Pagination1Service(); // Instance du service de pagination
    private User currentUser;
    private boolean showingBestSellers = false;
    private int currentPage = 1;
    private int pageSize = 6; // Nombre de produits par page
    private int totalPages;
    private List<Produit> allProduits;
    private List<Produit> bestSellersList;
    private int currentBestSellersPage = 1;
    private int bestSellersPageSize = 4; // Nombre de best sellers par page
    private int totalBestSellersPages;

    // FXML Elements
    @FXML private TilePane productsTilePane;
    @FXML private Button deleteBtn;
    @FXML private ImageView logoImageView;
    @FXML private Button connexionButton;
    @FXML private Button toggleViewBtn;
    @FXML private VBox bestSellersContainer;
    @FXML private Button afficherCommandesBtn;
    @FXML private HBox paginationControls; // Conteneur pour les boutons de pagination des produits
    @FXML private HBox bestSellersPaginationControls; // Conteneur pour la pagination des best sellers

    // Navigation methods
    @FXML
    private void navigateToAccueil(ActionEvent event) {
        loadPage("/AcceuilFront.fxml", event);
    }

    @FXML
    private void navigateToRencontres(ActionEvent event) {
        loadPage("/Rencontres.fxml", event);
    }

    @FXML
    private void navigateToEvenements(ActionEvent event) {
        loadPage("/AfficherEvenementUser.fxml", event);
    }

    @FXML
    private void navigateToForum(ActionEvent event) {
        loadPage("/AfficherPost.fxml", event);
    }

    @FXML
    private void navigateToBoutique(ActionEvent event) {
        loadPage("/AfficherProduit.fxml", event);
    }

    @FXML
    private void navigateToConnexion(ActionEvent event) {
        loadPage("/AjouterUser.fxml", event);
    }

    @FXML
    private void navigateToProfile(ActionEvent event) {
        loadPage("/ProfilUser.fxml", event);
    }
    private Label boutiqueTitleLabel; // Ajoutez ce champ


    private void loadPage(String fxmlPath, ActionEvent event) {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource(fxmlPath));


            Parent root = loader.load();
            Stage stage = (Stage)((Node)event.getSource()).getScene().getWindow();
            stage.setScene(new Scene(root));
            stage.show();
        } catch (IOException e) {
            showAlert("Error", "Failed to load page: " + e.getMessage());
        }
    }




    @FXML
    public void initialize() {
        try {

            // Charger le logo
            Image logo = new Image(getClass().getResource("/logofront.png").toExternalForm());
            logoImageView.setImage(logo);

            // Vérifier la session utilisateur
            checkUserSession();

            // Charger les produits avec pagination
            loadProduitsWithPagination(1);

            // Initialiser les best sellers avec pagination
            loadBestSellersWithPagination(1);
            Timeline timeline = new Timeline(
                    new KeyFrame(Duration.seconds(30),
                            event -> loadBestSellersWithPagination(currentBestSellersPage))
            );
            timeline.setCycleCount(Animation.INDEFINITE);
            timeline.play();

            // Initialiser l'état de la vue des best sellers
            bestSellersContainer.setVisible(showingBestSellers);
            bestSellersContainer.setManaged(showingBestSellers);
            toggleViewBtn.setText(showingBestSellers ? "Voir Tous les Produits" : "Voir Best Sellers");

        } catch (Exception e) {
            System.err.println("Error initializing controller: " + e.getMessage());
            showError("Erreur d'Initialisation", "Une erreur est survenue lors de l'initialisation");
        }
    }


    private void checkUserSession() {
        currentUser = UserSession.getInstance().getUser();
        if (currentUser != null) {
            connexionButton.setText("Profil");
            connexionButton.setOnAction(this::navigateToProfile);
        } else {
            connexionButton.setText("Connexion");
            connexionButton.setOnAction(this::navigateToConnexion);
        }
    }

    @FXML
    private void ouvrirAjoutProduit() {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/AjouterProduit.fxml"));
            Parent root = loader.load();
            Stage stage = new Stage();
            stage.setScene(new Scene(root));
            stage.setTitle("Ajouter Produit");
            stage.initModality(Modality.APPLICATION_MODAL);
            stage.show();
            stage.setOnHidden(e -> loadProduitsWithPagination(currentPage)); // Rafraîchir après ajout
        } catch (IOException e) {
            e.printStackTrace();
            showError("Erreur d'Ouverture", "Impossible d'ouvrir l'interface d'ajout de produit");
        }
    }

    @FXML
    private void ouvrirAfficherCommande() {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/AfficherCommande.fxml"));
            Parent root = loader.load();
            Stage stage = new Stage();
            stage.setScene(new Scene(root));
            stage.setTitle("Mes Commandes");
            stage.initModality(Modality.APPLICATION_MODAL);
            stage.show();
        } catch (IOException e) {
            e.printStackTrace();
            showError("Erreur d'Ouverture", "Impossible d'ouvrir l'interface des commandes");
        }
    }

    @FXML
    private void ouvrirAffichageProduitBack() {
        try {
            Parent root = FXMLLoader.load(getClass().getResource("/BACK.fxml"));
            Stage stage = new Stage();
            stage.setScene(new Scene(root));
            stage.setTitle("Administration");
            stage.show();
        } catch (IOException e) {
            e.printStackTrace();
            showError("Erreur d'Ouverture", "Impossible d'ouvrir l'interface d'administration");
        }
    }

    @FXML
    private void supprimerProduit(ActionEvent event) {
        // Logique pour supprimer un produit (tu peux personnaliser)
        System.out.println("Produit supprimé !");
    }

    private void loadProduits() throws SQLException {
        if (productsTilePane == null) {
            System.err.println("Error: productsTilePane is null. Check your FXML file.");
            return;
        }
        productsTilePane.getChildren().clear();
        List<Produit> produits = produitService.recupererProduits();
        User user = UserSession.getInstance().getUser();

        for (Produit p : produits) {
            if (p.getEtat_produit() == 1) {
                VBox card = createProductCard(p, user);
                productsTilePane.getChildren().add(card);
            }
        }
    }

    private void loadProduitsWithPagination(int page) {
        currentPage = page;
        if (productsTilePane == null) {
            System.err.println("Error: productsTilePane is null. Check your FXML file.");
            return;
        }
        productsTilePane.getChildren().clear();
        if (paginationControls != null) {
            paginationControls.getChildren().clear();
        }

        try {
            allProduits = produitService.recupererProduits();
            List<Produit> produitsToShow = allProduits.stream().filter(p -> p.getEtat_produit() == 1).toList();
            int totalItems = produitsToShow.size();
            totalPages = (int) Math.ceil((double) totalItems / pageSize);

            int startIndex = (page - 1) * pageSize;
            int endIndex = Math.min(startIndex + pageSize, totalItems);

            List<Produit> currentPageProducts = produitsToShow.subList(startIndex, endIndex);

            User user = UserSession.getInstance().getUser();
            for (Produit p : currentPageProducts) {
                VBox card = createProductCard(p, user);
                productsTilePane.getChildren().add(card);
            }

            generatePaginationButtons();

        } catch (SQLException e) {
            e.printStackTrace();
            showError("Erreur de Chargement", "Impossible de charger les produits");
        }
    }

    private void generatePaginationButtons() {
        if (paginationControls == null) {
            return;
        }
        paginationControls.getChildren().clear();
        for (int i = 1; i <= totalPages; i++) {
            Button pageButton = new Button(String.valueOf(i));
            pageButton.setStyle(i == currentPage ?
                    "-fx-background-color: #c49b63; -fx-text-fill: black; -fx-padding: 5 10; -fx-background-radius: 5;" :
                    "-fx-background-color: #5d5d5d; -fx-text-fill: white; -fx-padding: 5 10; -fx-background-radius: 5;");
            int pageNumber = i;
            pageButton.setOnAction(event -> loadProduitsWithPagination(pageNumber));
            paginationControls.getChildren().add(pageButton);
        }
    }

    private VBox createProductCard(Produit p, User user) {
        VBox card = new VBox(10);
        card.setPrefSize(180, 280);
        card.setStyle("""
                -fx-background-color: transparent;
                -fx-padding: 10;
                -fx-border-color: #c49b63;
                -fx-border-width: 1;
                -fx-border-radius: 10;
                -fx-background-radius: 10;
                -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 10, 0.5, 0, 0);
                -fx-alignment: center;
                """);

        // Image du produit
        ImageView imageView = createProductImageView(p);
        card.getChildren().add(imageView);

        // Infos produit
        Label nomLabel = new Label(p.getNom_produit());
        nomLabel.setStyle("-fx-font-weight: bold; -fx-font-size: 14; -fx-text-fill: white;");
        Label prixLabel = new Label(p.getPrix_produit() + " TND");
        prixLabel.setStyle("-fx-text-fill: #ff6600; -fx-font-weight: bold;");
        Label typeLabel = new Label(p.getType_produit());
        typeLabel.setStyle("-fx-text-fill: white;");

        // Boutons principaux
        Button addBtn = new Button("🛒 +");
        addBtn.setStyle("-fx-background-color: #c49b63; -fx-text-fill: black; -fx-padding: 4 8; -fx-background-radius: 8;");
        addBtn.setOnAction(e -> openAddOrderPopup(p));

        Button detailsBtn = new Button("🔍 Détails");
        detailsBtn.setStyle("-fx-background-color: #5d5d5d; -fx-text-fill: white; -fx-padding: 4 8; -fx-background-radius: 8;");
        detailsBtn.setOnAction(e -> openDetailsProduit(p));

        HBox mainButtons = new HBox(10, addBtn, detailsBtn);
        mainButtons.setAlignment(Pos.CENTER);

        card.getChildren().addAll(nomLabel, typeLabel, prixLabel, mainButtons);

        // Boutons admin (si l'utilisateur est le créateur)
        if (user != null && p.getUser() != null && user.getId() == p.getUser().getId()) {
            Button modifierBtn = new Button("✏️");
            modifierBtn.setStyle("-fx-background-color: #8b6914; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 5;");
            modifierBtn.setOnAction(e -> openModifierProduit(p));
            modifierBtn.setTooltip(new Tooltip("Modifier"));

            Button deleteBtn = new Button("🗑️");
            deleteBtn.setStyle("-fx-background-color: #a52a2a; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 5;");
            deleteBtn.setOnAction(e -> openPopupSuppression(p));
            deleteBtn.setTooltip(new Tooltip("Supprimer"));

            HBox adminButtons = new HBox(10, modifierBtn, deleteBtn);
            adminButtons.setAlignment(Pos.CENTER);
            card.getChildren().add(adminButtons);
        }

        return card;
    }



    private void openDetailsProduit(Produit p) {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/DetailsProduit.fxml"));
            Parent root = loader.load();
            DetailsProduit controller = loader.getController();
            controller.setProduit(p);
            Stage stage = new Stage();
            stage.setScene(new Scene(root));
            stage.setTitle("Détails du Produit");
            stage.initModality(Modality.APPLICATION_MODAL);
            stage.show();
        } catch (IOException ex) {
            ex.printStackTrace();
            showError("Erreur d'Ouverture", "Impossible d'ouvrir les détails du produit");
        }
    }

    private void openModifierProduit(Produit p) {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/ModifierProduit.fxml"));
            Parent root = loader.load();
            ModifierProduit controller = loader.getController();
            controller.setProduit(p);
            Stage stage = new Stage();
            stage.setScene(new Scene(root));
            stage.setTitle("Modifier Produit");
            stage.initModality(Modality.APPLICATION_MODAL);
            stage.show();
            stage.setOnHidden(e -> loadProduitsWithPagination(currentPage)); // Rafraîchir après modification
        } catch (IOException ex) {
            ex.printStackTrace();
            showError("Erreur d'Ouverture", "Impossible d'ouvrir la fenêtre de modification du produit");
        }
    }

    private void openAddOrderPopup(Produit produit) {
        try {
            if (produit.getStock_produit() <= 0) {
                showAlert("Indisponible", "Ce produit est épuisé");
                return;
            }

            FXMLLoader loader = new FXMLLoader(getClass().getResource("/AjouterCommande.fxml"));
            Parent root = loader.load();
            AjouterCommande controller = loader.getController();
            controller.setProduit(produit);
            controller.setOnSuccess(() -> {
                try {
                    loadProduitsWithPagination(currentPage);
                } catch (Exception e) {
                    showError("Erreur", "Impossible de rafraîchir la liste des produits");
                }
            });
            Stage stage = new Stage();
            stage.setScene(new Scene(root));
            stage.setTitle("Commander: " + produit.getNom_produit());
            stage.initModality(Modality.APPLICATION_MODAL);
            stage.showAndWait();
        } catch (IOException e) {
        }
    }

    private void openPopupSuppression(Produit produit) {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/SupprimerProduit.fxml"));
            Parent root = loader.load();
            SupprimerProduit controller = loader.getController();
            controller.setMessage("Êtes-vous sûr de vouloir supprimer le produit : " + produit.getNom_produit() + "?");
            controller.setOnConfirmAction(() -> {
                try {
                    produitService.supprimerProduit(produit.getId_produit());
                    loadProduitsWithPagination(currentPage);
                } catch (SQLException ex) {
                    showError("Erreur de Suppression", "Impossible de supprimer le produit");
                }
            });
            Stage popupStage = new Stage();
            popupStage.setScene(new Scene(root));
            popupStage.setTitle("Confirmation de Suppression");
            popupStage.initModality(Modality.APPLICATION_MODAL);
            popupStage.show();
        } catch (IOException e) {
            e.printStackTrace();
            showError("Erreur d'Ouverture", "Impossible d'ouvrir la fenêtre de confirmation de suppression");
        }
    }

    @FXML
    private void toggleView() {
        showingBestSellers = !showingBestSellers;
        bestSellersContainer.setVisible(showingBestSellers);
        bestSellersContainer.setManaged(showingBestSellers);
        toggleViewBtn.setText(showingBestSellers ? "Voir Tous les Produits" : "Voir Best Sellers");
        paginationControls.setVisible(!showingBestSellers);
        paginationControls.setManaged(!showingBestSellers);
        bestSellersPaginationControls.setVisible(showingBestSellers);
        bestSellersPaginationControls.setManaged(showingBestSellers);

        if (showingBestSellers) {
            loadBestSellersWithPagination(1);
        } else {
            loadProduitsWithPagination(1);
        }
    }

    private void loadBestSellersWithPagination(int page) {
        currentBestSellersPage = page;
        if (bestSellersContainer == null) {
            System.err.println("Error: bestSellersContainer is null. Check your FXML file.");
            return;
        }
        bestSellersContainer.getChildren().clear();
        if (bestSellersPaginationControls != null) {
            bestSellersPaginationControls.getChildren().clear();
        }

        try {
            bestSellersList = commandeService.getTop5BestSellingProducts();
            int totalItems = bestSellersList.size();
            totalBestSellersPages = (int) Math.ceil((double) totalItems / bestSellersPageSize);

            // Titre principal
            Label titleLabel = new Label("BEST COFFEE SELLERS");
            titleLabel.setStyle("-fx-font-weight: bold; -fx-font-size: 22; -fx-text-fill: #c49b63;");

            // Description
            Label descriptionLabel = new Label("Découvrez nos cafés les plus appréciés par nos clients, des saveurs d'exception qui font notre réputation.");
            descriptionLabel.setStyle("-fx-text-fill: white; -fx-font-size: 14; -fx-wrap-text: true;");
            descriptionLabel.setMaxWidth(600);

            // Séparateur
            Separator separator = new Separator();
            separator.setStyle("-fx-background-color: #c49b63;");

            VBox headerBox = new VBox(10, titleLabel, descriptionLabel, separator);
            headerBox.setStyle("-fx-padding: 0 0 20 0;");
            bestSellersContainer.getChildren().add(headerBox);

            if (bestSellersList == null || bestSellersList.isEmpty()) {
                Label noDataLabel = new Label("Aucune donnée de vente disponible pour le moment");
                noDataLabel.setStyle("-fx-text-fill: white; -fx-font-size: 16; -fx-padding: 20;");
                bestSellersContainer.getChildren().add(noDataLabel);
                return;
            }

            int startIndex = (page - 1) * bestSellersPageSize;
            int endIndex = Math.min(startIndex + bestSellersPageSize, totalItems);

            List<Produit> currentPageBestSellers = bestSellersList.subList(startIndex, endIndex);

            // Conteneur horizontal pour les produits
            HBox productsRow = new HBox(30); // Augmentez l'espacement
            productsRow.setAlignment(Pos.CENTER);
            productsRow.setStyle("-fx-padding: 20; -fx-background-color: rgba(30,30,30,0.7); -fx-background-radius: 10;");

            for (Produit produit : currentPageBestSellers) {
                VBox productCard = createBestSellerCard(produit);
                productsRow.getChildren().add(productCard);
            }

            // Centrer la ligne de produits
            HBox centeredRow = new HBox(productsRow);
            centeredRow.setAlignment(Pos.CENTER);
            bestSellersContainer.getChildren().add(centeredRow);

            generateBestSellersPaginationButtons();

        } catch (SQLException e) {
            e.printStackTrace();
            showError("Erreur de Chargement", "Impossible de charger les meilleures ventes");

            Label errorLabel = new Label("Erreur lors du chargement des meilleures ventes");
            errorLabel.setStyle("-fx-text-fill: #ff6666; -fx-font-size: 16; -fx-padding: 20;");
            bestSellersContainer.getChildren().add(errorLabel);
        }
    }

    private void generateBestSellersPaginationButtons() {
        if (bestSellersPaginationControls == null) {
            return;
        }
        bestSellersPaginationControls.getChildren().clear();
        for (int i = 1; i <= totalBestSellersPages; i++) {
            Button pageButton = new Button(String.valueOf(i));
            pageButton.setStyle(i == currentBestSellersPage ?
                    "-fx-background-color: #c49b63; -fx-text-fill: black; -fx-padding: 5 10; -fx-background-radius: 5;" :
                    "-fx-background-color: #5d5d5d; -fx-text-fill: white; -fx-padding: 5 10; -fx-background-radius: 5;");
            int pageNumber = i;
            pageButton.setOnAction(event -> loadBestSellersWithPagination(pageNumber));
            bestSellersPaginationControls.getChildren().add(pageButton);
        }
    }

    private VBox createBestSellerCard(Produit produit) {
        VBox card = new VBox(15); // Augmentez l'espacement
        card.setAlignment(Pos.CENTER);
        card.setStyle("""
        -fx-background-color: rgba(40,40,40,0.9);
        -fx-padding: 15;
        -fx-background-radius: 10;
        -fx-border-color: #c49b63;
        -fx-border-width: 1;
        -fx-border-radius: 10;
        -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.5), 10, 0, 0, 0);
        """);

        // Image du produit
        ImageView imageView = createProductImageView(produit);
        imageView.setFitWidth(150); // Taille un peu plus grande
        imageView.setFitHeight(150);
        imageView.setStyle("-fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.8), 10, 0, 0, 0);");

        // Nom du produit
        Label nameLabel = new Label(produit.getNom_produit().toUpperCase());
        nameLabel.setStyle("""
        -fx-font-weight: bold;
        -fx-font-size: 16;
        -fx-text-fill: white;
        -fx-wrap-text: true;
        -fx-alignment: center;
        """);
        nameLabel.setMaxWidth(150);

        // Prix
        Label priceLabel = new Label(String.format("%.2f TND", produit.getPrix_produit()));
        priceLabel.setStyle("-fx-text-fill: #c49b63; -fx-font-weight: bold; -fx-font-size: 14;");

        card.getChildren().addAll(imageView, nameLabel, priceLabel);
        return card;
    }

    private void showAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.WARNING);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    private void showError(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.ERROR);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    private void showSuccess(String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("Succès");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    private ImageView createProductImageView(Produit produit) {
        ImageView imageView = new ImageView();
        try {
            // Essayer de charger depuis l'URL
            if (produit.getImage_produit() != null && !produit.getImage_produit().isEmpty()) {
                try {
                    // Vérifier si c'est une URL valide ou un chemin de fichier
                    if (produit.getImage_produit().startsWith("http") || produit.getImage_produit().startsWith("file:")) {
                        imageView.setImage(new Image(produit.getImage_produit(), 150, 100, true, true));
                    } else {
                        // Essayer de charger depuis les ressources
                        String imagePath = produit.getImage_produit().startsWith("/")
                                ? produit.getImage_produit()
                                : "/" + produit.getImage_produit();
                        imageView.setImage(new Image(getClass().getResourceAsStream(imagePath), 150, 100, true, true));
                    }
                } catch (Exception e) {
                    System.err.println("Erreur chargement image URL: " + e.getMessage());
                    // Charger une image par défaut
                    loadDefaultImage(imageView);
                }
            } else {
                loadDefaultImage(imageView);
            }
        } catch (Exception e) {
            System.err.println("Erreur générale chargement image: " + e.getMessage());
            loadDefaultImage(imageView);
        }
        return imageView;
    }

    private void loadDefaultImage(ImageView imageView) {
        try {
            imageView.setImage(new Image(getClass().getResourceAsStream("/images/default-product.png"), 150, 100, true, true));
        } catch (Exception e) {
            System.err.println("Erreur chargement image par défaut: " + e.getMessage());
            // Créer une image vide si même l'image par défaut ne charge pas
            imageView.setImage(null);
        }
    }
}