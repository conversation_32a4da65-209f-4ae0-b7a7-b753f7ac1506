package Controllers;

import entity.Produit;
import javafx.fxml.FXML;
import javafx.scene.control.ComboBox;
import javafx.stage.Stage;
import service.ProduitService;

public class ModifierProduitBack {

    @FXML
    private ComboBox<String> etatProduitComboBox;

    private Produit produit;
    private ProduitService produitService = new ProduitService();
    private Runnable onConfirmAction;

    public void setProduit(Produit produit) {
        this.produit = produit;
        // Initialiser avec la bonne valeur
        etatProduitComboBox.setValue(produit.getEtat_produit() == 1 ? "Disponible" : "Indisponible");
    }

    public void setOnConfirmAction(Runnable onConfirmAction) {
        this.onConfirmAction = onConfirmAction;
    }

    @FXML
    private void modifierProduit() {
        try {
            int nouvelEtat = etatProduitComboBox.getValue().equals("Disponible") ? 1 : 0;
            produit.setEtat_produit(nouvelEtat);

            produitService.modifierProduit(produit);

            if (onConfirmAction != null) {
                onConfirmAction.run();
            }

            closeWindow();
        } catch (Exception e) {
            e.printStackTrace();
            // Vous pourriez ajouter showAlert() ici si disponible
        }
    }

    private void closeWindow() {
        Stage stage = (Stage) etatProduitComboBox.getScene().getWindow();
        stage.close();
    }
}