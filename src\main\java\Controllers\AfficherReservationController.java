package Controllers;

import entity.Reservation;
import entity.Evenement;
import entity.User;
import entity.UserSession;
import javafx.event.ActionEvent;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.geometry.Insets;
import javafx.scene.Node;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.scene.layout.*;
import javafx.scene.paint.Color;
import javafx.scene.text.Font;
import javafx.scene.text.FontWeight;
import javafx.scene.text.Text;
import javafx.stage.Modality;
import javafx.stage.Stage;
import service.ReservationService;

import java.io.IOException;
import java.net.URL;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Optional;
import java.util.ResourceBundle;

public class AfficherReservationController implements Initializable {
    @FXML private VBox reservationsContainer;
    @FXML private Button addReservationBtn;
    @FXML private Button connexionButton;

    private final ReservationService reservationService = new ReservationService();
    private final SimpleDateFormat dateFormat = new SimpleDateFormat("dd MMMM yyyy");

    // Navigation methods
    @FXML
    private void navigateToAccueil(ActionEvent event) { loadPage("/Accueil.fxml", event); }
    @FXML
    private void navigateToRencontres(ActionEvent event) { loadPage("/Rencontres.fxml", event); }
    @FXML
    private void navigateToEvenements(ActionEvent event) { loadPage("/AfficherEvenementUser.fxml", event); }
    @FXML
    private void navigateToForum(ActionEvent event) { loadPage("/Forum.fxml", event); }
    @FXML
    private void navigateToBoutique(ActionEvent event) { loadPage("/AfficherProduit.fxml", event); }
    @FXML
    private void navigateToConnexion(ActionEvent event) { loadPage("/Connexion.fxml", event); }

    private void loadPage(String fxmlPath, ActionEvent event) {
        try {
            Parent root = FXMLLoader.load(getClass().getResource(fxmlPath));
            Stage stage = (Stage)((Node)event.getSource()).getScene().getWindow();
            stage.setScene(new Scene(root));
            stage.show();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        checkUserSession();
        loadUserReservations();
    }

    private void checkUserSession() {
        if (connexionButton == null) return;

        User currentUser = UserSession.getInstance().getUser();
        if (currentUser != null) {
            connexionButton.setText("Profil");
            connexionButton.setOnAction(this::navigateToProfile);
        } else {
            connexionButton.setText("Connexion");
            connexionButton.setOnAction(this::navigateToConnexion);
        }
    }

    @FXML
    private void navigateToProfile(ActionEvent event) {
        loadPage("/ProfilUser.fxml", event);
    }

    private void loadUserReservations() {
        reservationsContainer.getChildren().clear();

        // Récupérer l'utilisateur connecté
        User currentUser = UserSession.getInstance().getUser();
        if (currentUser == null) {
            showAlert("Connexion requise", "Veuillez vous connecter pour voir vos réservations", Alert.AlertType.WARNING);
            return;
        }

        try {
            // Charger uniquement les réservations de l'utilisateur connecté
            List<Reservation> reservations = reservationService.getReservationsByUser(currentUser.getId());
            displayReservations(reservations);
        } catch (SQLException e) {
            showAlert("Erreur", "Impossible de charger les réservations", Alert.AlertType.ERROR);
            e.printStackTrace();
        }
    }

    private void displayReservations(List<Reservation> reservations) {
        reservationsContainer.getChildren().clear();

        if (reservations.isEmpty()) {
            Label noRes = new Label("Vous n'avez aucune réservation");
            noRes.setStyle("-fx-font-size: 16px; -fx-text-fill: #666;");
            reservationsContainer.getChildren().add(noRes);
            return;
        }

        for (Reservation res : reservations) {
            reservationsContainer.getChildren().add(createReservationCard(res));
        }
    }

    private VBox createReservationCard(Reservation reservation) {
        VBox card = new VBox(15);
        card.setStyle("-fx-background-color: white; -fx-background-radius: 10; -fx-border-radius: 10; "
                + "-fx-border-color: #915f3a; -fx-border-width: 1; "
                + "-fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 10, 0, 0, 0);");
        card.setPadding(new Insets(20));
        card.setMaxWidth(850);

        // Event Image
        ImageView eventImage = new ImageView();
        try {
            String imageUrl = reservation.getEvenement().getImage_event();
            if (imageUrl != null && !imageUrl.isEmpty()) {
                if (imageUrl.startsWith("http") || imageUrl.startsWith("file:")) {
                    eventImage.setImage(new Image(imageUrl));
                } else {
                    eventImage.setImage(new Image("file:" + imageUrl));
                }
                eventImage.setFitWidth(800);
                eventImage.setFitHeight(300);
                eventImage.setPreserveRatio(true);
                eventImage.setStyle("-fx-border-radius: 5; -fx-background-radius: 5;");
            } else {
                eventImage.setImage(new Image(getClass().getResourceAsStream("/images/default-event.png")));
            }
        } catch (Exception e) {
            System.err.println("Error loading image: " + e.getMessage());
            try {
                eventImage.setImage(new Image(getClass().getResourceAsStream("/images/default-event.png")));
            } catch (Exception ex) {
                System.err.println("Couldn't load placeholder image: " + ex.getMessage());
            }
        }

        Label eventTitle = new Label(reservation.getEvenement().getTitre_evenement());
        eventTitle.setFont(Font.font("System", FontWeight.BOLD, 20));
        eventTitle.setStyle("-fx-text-fill: #915f3a;");

        GridPane detailsGrid = new GridPane();
        detailsGrid.setHgap(20);
        detailsGrid.setVgap(10);
        detailsGrid.setPadding(new Insets(10, 0, 0, 0));

        addDetailRow(detailsGrid, 0, "📅 Date de réservation:",
                reservation.getDate_booking() != null ? dateFormat.format(reservation.getDate_booking()) : "N/A");
        addDetailRow(detailsGrid, 1, "🎟️ Nombre de places:", String.valueOf(reservation.getNbr_places()));
        addDetailRow(detailsGrid, 2, "💳 Méthode de paiement:", reservation.getMoyen_payement_booking());
        addDetailRow(detailsGrid, 3, "📅 Date de l'événement:",
                reservation.getEvenement().getDate_event() != null ? dateFormat.format(reservation.getEvenement().getDate_event()) : "N/A");
        addDetailRow(detailsGrid, 4, "💰 Prix total:",
                String.format("%.2f DT", reservation.getNbr_places() * reservation.getEvenement().getPrix_event()));

        HBox buttonBox = createActionButtons(reservation);

        card.getChildren().addAll(eventImage, eventTitle, detailsGrid, buttonBox);
        return card;
    }

    private HBox createActionButtons(Reservation reservation) {
        HBox buttonBox = new HBox(15);
        buttonBox.setPadding(new Insets(15, 0, 0, 0));

        Button editButton = new Button("📝 Modifier");
        editButton.setStyle("-fx-background-color: #c98a56; -fx-text-fill: white; -fx-font-weight: bold;");
        editButton.setOnAction(e -> handleEditReservation(reservation));

        Button cancelButton = new Button("❌ Annuler");
        cancelButton.setStyle("-fx-background-color: #cc5c5c; -fx-text-fill: white; -fx-font-weight: bold;");
        cancelButton.setOnAction(e -> cancelReservation(reservation.getId()));

        if (!"Annulée".equals(reservation.getStatut_booking())) {
            buttonBox.getChildren().addAll(editButton, cancelButton);
        } else {
            Button deleteButton = new Button("Supprimer");
            deleteButton.setStyle("-fx-background-color: #cc5c5c; -fx-text-fill: white; -fx-font-weight: bold;");
            deleteButton.setOnAction(e -> handleDeleteReservation(reservation.getId()));
            buttonBox.getChildren().add(deleteButton);
        }

        return buttonBox;
    }

    private void addDetailRow(GridPane grid, int row, String label, String value) {
        Label detailLabel = new Label(label);
        detailLabel.setStyle("-fx-font-weight: bold; -fx-text-fill: #4a5568;");

        Label detailValue = new Label(value);
        detailValue.setStyle("-fx-text-fill: #2d3748;");

        grid.add(detailLabel, 0, row);
        grid.add(detailValue, 1, row);
    }

    @FXML
    private void handleAddReservation(ActionEvent event) {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/AjouterReservation.fxml"));
            Parent root = loader.load();

            // Passer l'utilisateur connecté
            User currentUser = UserSession.getInstance().getUser();
            if (currentUser != null) {
                AjouterReservationController controller = loader.getController();
                controller.setCurrentUser(currentUser.getId());
            }

            Stage stage = new Stage();
            stage.setScene(new Scene(root));
            stage.setTitle("Nouvelle Réservation");
            stage.initModality(Modality.WINDOW_MODAL);
            stage.initOwner(((Node)event.getSource()).getScene().getWindow());
            stage.showAndWait();

            loadUserReservations();
        } catch (IOException e) {
            showAlert("Erreur", "Impossible d'ouvrir le formulaire", Alert.AlertType.ERROR);
            e.printStackTrace();
        }
    }

    private void handleEditReservation(Reservation reservation) {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/ModifierReservation.fxml"));
            Parent root = loader.load();

            ModifierReservationController controller = loader.getController();
            controller.setReservation(reservation);

            Stage stage = new Stage();
            stage.setScene(new Scene(root));
            stage.setTitle("Modifier Réservation");
            stage.initModality(Modality.WINDOW_MODAL);
            stage.initOwner(reservationsContainer.getScene().getWindow());
            stage.showAndWait();

            loadUserReservations();
        } catch (IOException e) {
            showAlert("Erreur", "Échec de l'ouverture de l'éditeur", Alert.AlertType.ERROR);
            e.printStackTrace();
        }
    }

    private void cancelReservation(int reservationId) {
        Alert alert = new Alert(Alert.AlertType.CONFIRMATION);
        alert.setTitle("Confirmation d'annulation");
        alert.setHeaderText("Annuler la réservation");
        alert.setContentText("Êtes-vous sûr de vouloir annuler cette réservation?");

        alert.showAndWait().ifPresent(response -> {
            if (response == ButtonType.OK) {
                try {
                    reservationService.updateReservationStatus(reservationId, "Annulée");
                    loadUserReservations();
                    showAlert("Succès", "Réservation annulée avec succès", Alert.AlertType.INFORMATION);
                } catch (SQLException e) {
                    showAlert("Erreur", "Échec de l'annulation", Alert.AlertType.ERROR);
                }
            }
        });
    }

    private void handleDeleteReservation(int reservationId) {
        Alert alert = new Alert(Alert.AlertType.CONFIRMATION);
        alert.setTitle("Confirmation de suppression");
        alert.setHeaderText("Supprimer définitivement");
        alert.setContentText("Cette action est irréversible. Continuer?");

        alert.showAndWait().ifPresent(response -> {
            if (response == ButtonType.OK) {
                try {
                    reservationService.deleteReservation(reservationId);
                    loadUserReservations();
                    showAlert("Succès", "Réservation supprimée", Alert.AlertType.INFORMATION);
                } catch (SQLException e) {
                    showAlert("Erreur", "Échec de la suppression", Alert.AlertType.ERROR);
                }
            }
        });
    }

    private void showAlert(String title, String message, Alert.AlertType type) {
        Alert alert = new Alert(type);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}