package Controllers;

import entity.Evenement;
import javafx.event.ActionEvent;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.stage.Stage;
import service.EvenementService;

import javafx.scene.input.MouseEvent;
import java.net.URL;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.ResourceBundle;

public class AjouterEvenementController implements Initializable {

    @FXML private TextArea CapEve;
    @FXML private TextArea DescEve;
    @FXML private TextArea ImageEve;
    @FXML private TextArea PrixEve;
    @FXML private TextArea TitreEve;
    @FXML private ComboBox<String> TypeEve;
    @FXML private DatePicker Dateeve;
    @FXML private Button btnSave;
    @FXML private Button btnclear;
    @FXML private Button btnRetour;

    @FXML private ImageView imagePreview;

    private String[] event = {
            "Ateliers",
            "Conférences",
            "Événements culturels",
            "Événements éducatifs",
            "Événements de réseautage"
    };


    @Override
    public void initialize(URL arg0, ResourceBundle arg1) {
        //TypeEve.getItems().addAll(event);

        ImageEve.textProperty().addListener((observable, oldValue, newValue) -> {
            try {
                if (newValue != null && !newValue.isEmpty()) {
                    Image image = new Image(newValue, true);
                    image.errorProperty().addListener((obs, wasError, isNowError) -> {
                        if (isNowError) {
                            imagePreview.setImage(null);
                        }
                    });
                    imagePreview.setImage(image);
                } else {
                    imagePreview.setImage(null);
                }
            } catch (Exception e) {
                imagePreview.setImage(null);
            }
        });
    }
    @FXML
    void Enregistrer(ActionEvent event) {
        // Validate required fields

        if (TitreEve.getText().isEmpty() ||
                TypeEve.getValue()== null ||
                DescEve.getText().isEmpty() ||
                ImageEve.getText().isEmpty() ||
                CapEve.getText().isEmpty() ||
                PrixEve.getText().isEmpty() ||
                Dateeve.getValue() == null) {

            Alert alert = new Alert(Alert.AlertType.ERROR);
            alert.setTitle("Erreur");
            alert.setHeaderText("Champs manquants");
            alert.setContentText("Veuillez remplir tous les champs.");
            alert.showAndWait();
            return;
        }


        try {
            if (TypeEve.getValue() == null) {
                throw new IllegalArgumentException("Type manquant");
            }
        } catch (IllegalArgumentException e) {
            Alert alert = new Alert(Alert.AlertType.ERROR);
            alert.setTitle("Erreur");
            alert.setHeaderText("Type manquant");
            alert.setContentText("Veuillez sélectionner un type d'événement.");
            alert.showAndWait();
            return;
        }

        // Validate description
        try {
            if (DescEve.getText().isEmpty() || DescEve.getText().length() < 10) {
                throw new IllegalArgumentException("Description invalide");
            }
        } catch (IllegalArgumentException e) {
            Alert alert = new Alert(Alert.AlertType.ERROR);
            alert.setTitle("Erreur");
            alert.setHeaderText("Description invalide");
            alert.setContentText("La description doit contenir au moins 10 caractères.");
            alert.showAndWait();
            return;
        }

        // Validate image URL
        try {
            if (!ImageEve.getText().matches(".*(jpg|png|jpeg|image/jpeg|image/png).*")) {
                throw new IllegalArgumentException("Image invalide");
            }
        } catch (IllegalArgumentException e) {
            Alert alert = new Alert(Alert.AlertType.ERROR);
            alert.setTitle("Erreur");
            alert.setHeaderText("Image invalide");
            alert.setContentText("L'URL de l'image doit contenir une référence à jpg, png ou jpeg.");
            alert.showAndWait();
            return;
        }

        // Validate capacity
        try {
            int capacity = Integer.parseInt(CapEve.getText());
            if (capacity <= 0) {
                throw new IllegalArgumentException("Capacité invalide");
            }
        } catch (NumberFormatException e) {
            Alert alert = new Alert(Alert.AlertType.ERROR);
            alert.setTitle("Erreur");
            alert.setHeaderText("Format invalide");
            alert.setContentText("Le champ 'Capacité' doit être un nombre.");
            alert.showAndWait();
            return;
        } catch (IllegalArgumentException e) {
            Alert alert = new Alert(Alert.AlertType.ERROR);
            alert.setTitle("Erreur");
            alert.setHeaderText("Capacité invalide");
            alert.setContentText("La capacité doit être un nombre positif.");
            alert.showAndWait();
            return;
        }

        // Validate price
        try {
            double price = Double.parseDouble(PrixEve.getText());
            if (price < 0) {
                throw new IllegalArgumentException("Prix invalide");
            }
        } catch (NumberFormatException e) {
            Alert alert = new Alert(Alert.AlertType.ERROR);
            alert.setTitle("Erreur");
            alert.setHeaderText("Format invalide");
            alert.setContentText("Le champ 'Prix' doit être un nombre.");
            alert.showAndWait();
            return;
        } catch (IllegalArgumentException e) {
            Alert alert = new Alert(Alert.AlertType.ERROR);
            alert.setTitle("Erreur");
            alert.setHeaderText("Prix invalide");
            alert.setContentText("Le prix ne peut pas être négatif.");
            alert.showAndWait();
            return;
        }

        // Validate date
        try {
            if (Dateeve.getValue() == null || Dateeve.getValue().isBefore(LocalDate.now())) {
                throw new IllegalArgumentException("Date invalide");
            }
        } catch (IllegalArgumentException e) {
            Alert alert = new Alert(Alert.AlertType.ERROR);
            alert.setTitle("Erreur");
            alert.setHeaderText("Date invalide");
            alert.setContentText("La date ne peut pas être dans le passé.");
            alert.showAndWait();
            return;
        }


        EvenementService service = new EvenementService();

        // Convert LocalDate to java.util.Date
        LocalDate localDate = Dateeve.getValue();
        java.sql.Date sqlDate = java.sql.Date.valueOf(localDate);

        Evenement evenement = new Evenement(
                Integer.parseInt(CapEve.getText()),
                TitreEve.getText(),
                ImageEve.getText(),
                TypeEve.getValue(),
                DescEve.getText(),
                sqlDate,
                Double.parseDouble(PrixEve.getText())
        );


        // Save to database
        try {
            service.ajouterEvenement(evenement);
            Alert alert = new Alert(Alert.AlertType.INFORMATION);
            alert.setTitle("Succès");
            alert.setHeaderText("Événement ajouté");
            alert.setContentText("L'événement a été enregistré avec succès.");
            alert.showAndWait();

        } catch (Exception e) {
            Alert alert = new Alert(Alert.AlertType.INFORMATION);
            alert.setTitle("Erreur");
            alert.setHeaderText("Erreur d'enregistrement");
            alert.setContentText("Une erreur est survenue:"+ e.getMessage());
            alert.showAndWait();
        }
    }

    @FXML
    void Rénitialiser(ActionEvent event) {
        TitreEve.clear();
        TypeEve.getSelectionModel().clearSelection(); // Clear selected value
        TypeEve.setValue(null); // Reset displayed value
        DescEve.clear();
        ImageEve.clear();
        CapEve.clear();
        PrixEve.clear();
        Dateeve.setValue(null);
        imagePreview.setImage(null);

    }

    @FXML
    void Retour(ActionEvent event) {
        try {
            // Close current window
            Stage stage = (Stage) btnRetour.getScene().getWindow();
            stage.close();

            // Load AfficherEvenement window
            Parent root = FXMLLoader.load(getClass().getResource("/AfficherEvenement.fxml"));
            Stage newStage = new Stage();
            newStage.setScene(new Scene(root));
            newStage.setTitle("Liste des Événements");
            newStage.show();
        } catch (Exception e) {
            e.printStackTrace();
            Alert alert = new Alert(Alert.AlertType.ERROR);
            alert.setTitle("Erreur");
            alert.setHeaderText("Impossible d'ouvrir la liste des événements");
            alert.setContentText(e.getMessage());
            alert.showAndWait();
        }
    }
}



