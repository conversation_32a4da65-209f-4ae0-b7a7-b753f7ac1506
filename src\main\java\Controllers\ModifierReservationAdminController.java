package Controllers;

import entity.Reservation;
import entity.Evenement;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.scene.control.*;
import javafx.stage.Stage;
import javafx.util.StringConverter;
import service.ReservationService;
import service.EvenementService;

import java.sql.SQLException;
import java.util.Objects;

public class ModifierReservationAdminController {
    @FXML private Label userLabel;
    @FXML private ComboBox<Evenement> eventComboBox;
    @FXML private DatePicker datePicker;
    @FXML private Spinner<Integer> placesSpinner;
    @FXML private ComboBox<String> paymentComboBox;
    @FXML private ComboBox<String> statusComboBox;

    private Reservation currentReservation;
    private final ReservationService reservationService = new ReservationService();
    private final EvenementService evenementService = new EvenementService();

    public void setReservation(Reservation reservation) {
        this.currentReservation = reservation;
        initializeFields();
        populateFields();
    }

    private void initializeFields() {
        try {
            // Load events for combo box
            ObservableList<Evenement> events = FXCollections.observableArrayList(evenementService.recupererEvenement());
            eventComboBox.setItems(events);

            // Configure event display in combo box
            eventComboBox.setConverter(new StringConverter<Evenement>() {
                @Override
                public String toString(Evenement event) {
                    return event != null ? event.getTitre_evenement() : "";
                }
                @Override
                public Evenement fromString(String string) {
                    return null;
                }
            });

            // Set up payment methods
            paymentComboBox.setItems(FXCollections.observableArrayList(
                    "Espèces", "Carte bancaire", "Virement", "Chèque"
            ));

            // Set up status options
            statusComboBox.setItems(FXCollections.observableArrayList(
                    "Confirmée", "En attente", "Annulée"
            ));

            // Configure spinner
            placesSpinner.setValueFactory(
                    new SpinnerValueFactory.IntegerSpinnerValueFactory(1, 100, 1)
            );

        } catch (SQLException e) {
            showAlert("Erreur", "Impossible de charger les événements: " + e.getMessage(), Alert.AlertType.ERROR);
        }
    }

    private void populateFields() {
        if (currentReservation == null) return;

        // Set user info (non-editable)
        userLabel.setText(currentReservation.getUser().getNom_user() + " " +
                currentReservation.getUser().getPrenom_user());

        // Set other fields
        eventComboBox.getSelectionModel().select(currentReservation.getEvenement());
        datePicker.setValue(currentReservation.getDate_booking().toLocalDate());
        placesSpinner.getValueFactory().setValue(currentReservation.getNbr_places());
        paymentComboBox.getSelectionModel().select(currentReservation.getMoyen_payement_booking());
        statusComboBox.getSelectionModel().select(currentReservation.getStatut_booking());
    }

    @FXML
    private void handleUpdate() {
        try {
            // Validate inputs
            if (eventComboBox.getValue() == null) {
                showAlert("Erreur", "Veuillez sélectionner un événement", Alert.AlertType.ERROR);
                return;
            }

            if (datePicker.getValue() == null) {
                showAlert("Erreur", "Veuillez sélectionner une date valide", Alert.AlertType.ERROR);
                return;
            }

            // Check if any changes were made
            boolean eventChanged = !Objects.equals(eventComboBox.getValue().getId(), currentReservation.getEvenement().getId());
            boolean dateChanged = !datePicker.getValue().equals(currentReservation.getDate_booking().toLocalDate());
            boolean placesChanged = !placesSpinner.getValue().equals(currentReservation.getNbr_places());
            boolean paymentChanged = !paymentComboBox.getValue().equals(currentReservation.getMoyen_payement_booking());
            boolean statusChanged = !statusComboBox.getValue().equals(currentReservation.getStatut_booking());

            if (!eventChanged && !dateChanged && !placesChanged && !paymentChanged && !statusChanged) {
                showAlert("Information", "Aucune modification n'a été apportée", Alert.AlertType.INFORMATION);
                return;
            }

            // Update reservation object with form values
            currentReservation.setEvenement(eventComboBox.getValue());
            currentReservation.setDate_booking(java.sql.Date.valueOf(datePicker.getValue()));
            currentReservation.setNbr_places(placesSpinner.getValue());
            currentReservation.setMoyen_payement_booking(paymentComboBox.getValue());
            currentReservation.setStatut_booking(statusComboBox.getValue());

            // Update in database
            reservationService.modifierReservation(currentReservation);

            showAlert("Succès", "Réservation mise à jour avec succès", Alert.AlertType.INFORMATION);

            // Close window
            ((Stage) userLabel.getScene().getWindow()).close();

        } catch (SQLException e) {
            showAlert("Erreur", "Échec de la mise à jour: " + e.getMessage(), Alert.AlertType.ERROR);
            e.printStackTrace();
        } catch (Exception e) {
            showAlert("Erreur", "Une erreur inattendue est survenue", Alert.AlertType.ERROR);
            e.printStackTrace();
        }
    }

    @FXML
    private void handleCancel() {
        ((Stage) userLabel.getScene().getWindow()).close();
    }

    private void showAlert(String title, String message, Alert.AlertType type) {
        Alert alert = new Alert(type);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}