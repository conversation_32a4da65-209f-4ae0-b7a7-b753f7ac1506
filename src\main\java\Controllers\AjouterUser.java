package Controllers;

import entity.User;
import javafx.event.ActionEvent;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.scene.Node;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.stage.FileChooser;
import javafx.stage.Stage;
import service.UserService;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;
import java.sql.Date;
import java.sql.SQLException;
import java.util.regex.Pattern;

public class AjouterUser {

    @FXML private TextField txtName;
    @FXML private TextField txtLastname;
    @FXML private TextField txtemail;
    @FXML private TextField txtAdresse;
    @FXML private TextField txtTelephone;
    @FXML private TextField txtPhoto;
    @FXML private DatePicker dateNaissance;
    @FXML private PasswordField txtpassword;
    @FXML private PasswordField txtConfirmPassword;
    @FXML private ImageView imagePreview;
    @FXML private Label errorLabel;

    @FXML
    void AjouterUser(ActionEvent event) {
        // Lecture des champs
        String prenom = txtName.getText().trim();
        String nom = txtLastname.getText().trim();
        String email = txtemail.getText().trim();
        String pwd = txtpassword.getText();
        String confirmPwd = txtConfirmPassword.getText();
        String adresse = txtAdresse.getText().trim();
        String photo = txtPhoto.getText().trim();
        String telStr = txtTelephone.getText().trim();

        // Validation
        if (prenom.isEmpty() || nom.isEmpty() || email.isEmpty() || pwd.isEmpty() || confirmPwd.isEmpty()
                || adresse.isEmpty() || telStr.isEmpty() || photo.isEmpty() || dateNaissance.getValue() == null) {
            showAlert("Error", "All fields are required.");
            return;
        }

        if (!pwd.equals(confirmPwd)) {
            showAlert("Error", "Password and Confirm Password do not match.");
            return;
        }

        if (!isValidEmail(email)) {
            showAlert("Error", "Invalid email format.");
            return;
        }

        if (pwd.length() < 6) {
            showAlert("Error", "Password must be at least 6 characters long.");
            return;
        }

        int tel;
        try {
            tel = Integer.parseInt(telStr);
        } catch (NumberFormatException e) {
            showAlert("Error", "Invalid phone number.");
            return;
        }

        // Création de l'objet utilisateur
        User usr = new User();
        usr.setPrenom_user(prenom);
        usr.setNom_user(nom);
        usr.setEmail_user(email);
        usr.setPassword(pwd);
        usr.setRole_user("ROLE_CLIENT");
        usr.setAdresse(adresse);
        usr.setTelephone_user(tel);
        usr.setDate_naissance_user(Date.valueOf(dateNaissance.getValue()));
        usr.setPhoto_user(photo); // chemin image

        UserService service = new UserService();
        try {
            service.signup(usr);
            service.sendVerificationLink(usr); // Envoi email avec lien de vérification

            Alert success = new Alert(Alert.AlertType.INFORMATION);
            success.setTitle("Inscription réussie");
            success.setHeaderText(null);
            success.setContentText("✅ Compte créé. Un email de vérification vous a été envoyé.\nVeuillez vérifier votre adresse pour activer votre compte.");
            success.showAndWait();

            Parent loginView = FXMLLoader.load(getClass().getResource("/Login.fxml"));
            Stage stage = (Stage) ((Node) event.getSource()).getScene().getWindow();
            stage.setScene(new Scene(loginView));
        } catch (SQLException | IOException e) {
            showAlert("Error", "Erreur : " + e.getMessage());
            e.printStackTrace();
        }
    }

    @FXML
    private void handleChoosePhoto(ActionEvent event) {
        FileChooser fileChooser = new FileChooser();
        fileChooser.setTitle("Select Profile Photo");
        fileChooser.getExtensionFilters().addAll(
                new FileChooser.ExtensionFilter("Image Files", "*.png", "*.jpg", "*.jpeg")
        );

        File selectedFile = fileChooser.showOpenDialog(((Node) event.getSource()).getScene().getWindow());
        if (selectedFile != null) {
            try {
                File destDir = new File("uploads");
                if (!destDir.exists()) destDir.mkdirs();

                String filename = System.currentTimeMillis() + "_" + selectedFile.getName();
                File destFile = new File(destDir, filename);
                Files.copy(selectedFile.toPath(), destFile.toPath(), StandardCopyOption.REPLACE_EXISTING);

                txtPhoto.setText("uploads/" + filename);
                imagePreview.setImage(new Image(destFile.toURI().toString()));

            } catch (IOException e) {
                showAlert("Error", "Failed to save image.");
                e.printStackTrace();
            }
        }
    }

    private boolean isValidEmail(String email) {
        String emailRegex = "^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+$";
        Pattern pat = Pattern.compile(emailRegex);
        return pat.matcher(email).matches();
    }

    private void showAlert(String title, String content) {
        errorLabel.setText(content);
        Alert alert = new Alert(Alert.AlertType.ERROR);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(content);
        alert.showAndWait();
    }

    @FXML
    private void handleBackToLogin(ActionEvent event) {
        try {
            Parent loginView = FXMLLoader.load(getClass().getResource("/Login.fxml"));
            Stage stage = (Stage) ((Node) event.getSource()).getScene().getWindow();
            stage.setScene(new Scene(loginView));
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}