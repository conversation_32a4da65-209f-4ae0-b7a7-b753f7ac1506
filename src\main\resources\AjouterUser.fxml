<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.Button?>
<?import javafx.scene.control.DatePicker?>
<?import javafx.scene.control.Hyperlink?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.PasswordField?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.image.Image?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.Region?>
<?import javafx.scene.layout.StackPane?>
<?import javafx.scene.layout.VBox?>

<StackPane prefHeight="600" prefWidth="600" style="-fx-background-color: linear-gradient(to bottom right, #2193b0, #6dd5ed);" xmlns="http://javafx.com/javafx/23.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="Controllers.AjouterUser">

   <VBox alignment="CENTER" maxHeight="570" maxWidth="470" spacing="20" style="-fx-padding: 40;                  -fx-background-color: #ffffff;                  -fx-background-radius: 20;                  -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.15), 20, 0, 0, 10);">

      <!-- Header -->
      <VBox alignment="CENTER" spacing="5">
         <ImageView fitHeight="60" fitWidth="60" preserveRatio="true">
            <Image url="@images/add-user-icon.png" />
         </ImageView>
         <Label style="-fx-font-size: 26px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;" text="Create Account" />
         <Label style="-fx-font-size: 13px; -fx-text-fill: #95a5a6;" text="Please fill in the form below" />
      </VBox>

      <!-- Form Fields -->
      <VBox alignment="CENTER" maxWidth="360" spacing="12">
         <HBox prefHeight="100.0" prefWidth="200.0">
            <children>
               <TextField fx:id="txtName" promptText="Name" style="-fx-background-radius: 8; -fx-padding: 10; -fx-background-color: #f8f9fa; -fx-border-color: #e0e0e0; -fx-border-radius: 8;" />
               <Region prefHeight="39.0" prefWidth="20.0" />
               <TextField fx:id="txtLastname" promptText="Last Name" style="-fx-background-radius: 8; -fx-padding: 10; -fx-background-color: #f8f9fa; -fx-border-color: #e0e0e0; -fx-border-radius: 8;" />
            </children>
         </HBox>
         <HBox prefHeight="100.0" prefWidth="200.0">
            <children>
               <TextField fx:id="txtemail" promptText="Email" style="-fx-background-radius: 8; -fx-padding: 10; -fx-background-color: #f8f9fa; -fx-border-color: #e0e0e0; -fx-border-radius: 8;" />
               <Region prefHeight="39.0" prefWidth="20.0" />
               <TextField fx:id="txtAdresse" promptText="Address" style="-fx-background-radius: 8; -fx-padding: 10; -fx-background-color: #f8f9fa; -fx-border-color: #e0e0e0; -fx-border-radius: 8;" />
            </children>
         </HBox>
         <TextField fx:id="txtTelephone" promptText="Phone Number" style="-fx-background-radius: 8; -fx-padding: 10; -fx-background-color: #f8f9fa; -fx-border-color: #e0e0e0; -fx-border-radius: 8;" />
         <DatePicker fx:id="dateNaissance" prefHeight="43.0" prefWidth="359.0" promptText="Birth Date" style="-fx-background-radius: 8; -fx-padding: 8; -fx-background-color: #f8f9fa; -fx-border-color: #e0e0e0; -fx-border-radius: 8;" />
         <HBox prefHeight="100.0" prefWidth="200.0">
            <children>
               <PasswordField fx:id="txtpassword" promptText="Password" style="-fx-background-radius: 8; -fx-padding: 10; -fx-background-color: #f8f9fa; -fx-border-color: #e0e0e0; -fx-border-radius: 8;" />
               <Region prefHeight="39.0" prefWidth="20.0" />
               <PasswordField fx:id="txtConfirmPassword" promptText="Confirm Password" style="-fx-background-radius: 8; -fx-padding: 10; -fx-background-color: #f8f9fa; -fx-border-color: #e0e0e0; -fx-border-radius: 8;" />
            </children>
         </HBox>

         <Button onAction="#handleChoosePhoto" style="-fx-background-color: #3498db; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 8; -fx-padding: 10;" text="Choose Photo" />

         <ImageView fx:id="imagePreview" fitHeight="118.0" fitWidth="132.0" preserveRatio="true" style="-fx-border-color: #ccc; -fx-border-radius: 8;" />

         <!-- Hidden photo path -->
         <TextField fx:id="txtPhoto" managed="false" promptText="Photo Path" visible="false" />

         <!-- Register Button -->
         <Button onAction="#AjouterUser" style="-fx-background-color: #27ae60; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 8; -fx-padding: 10;" text="Register" />

         <!-- Already have an account -->
         <HBox alignment="CENTER" spacing="5">
            <Label style="-fx-text-fill: #95a5a6; -fx-font-size: 12px;" text="Already have an account?" />
            <Hyperlink onAction="#handleBackToLogin" style="-fx-text-fill: #3498db; -fx-font-weight: bold; -fx-font-size: 12px;" text="Log in" />
         </HBox>

         <!-- Error message -->
         <Label fx:id="errorLabel" style="-fx-font-size: 13px; -fx-padding: 8; -fx-background-radius: 4;" textFill="#e74c3c" />
      </VBox>
   </VBox>
</StackPane>
