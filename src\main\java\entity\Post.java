package entity;

import java.sql.Date;
import java.util.ArrayList;
import java.util.List;

public class Post {
    private int id;
    private User user;
    private String description_post;
    private Date date_post;
    private int nbr_likes;
    private String image;
    private boolean is_validated;
    private List<Commentaire> commentaires;

    public Post() {
        this.commentaires = new ArrayList<>();
    }
    public Post(User user, String description_post, Date date_post, String image, boolean is_validated) {
        this.user = user;
        this.description_post = description_post;
        this.date_post = date_post;
        this.nbr_likes = 0;
        this.image = image;
        this.is_validated = is_validated;
        this.commentaires = new ArrayList<>();
    }

    public Post(int id, User user, String description_post, Date date_post, int nbr_likes, String image, boolean is_validated, List<Commentaire> commentaires) {
        this.id = id;
        this.user = user;
        this.description_post = description_post;
        this.date_post = date_post;
        this.nbr_likes = nbr_likes;
        this.image = image;
        this.is_validated = is_validated;
        this.commentaires = new ArrayList<>();
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public String getDescription_post() {
        return description_post;
    }

    public void setDescription_post(String description_post) {
        this.description_post = description_post;
    }

    public Date getDate_post() {
        return date_post;
    }

    public void setDate_post(Date date_post) {
        this.date_post = date_post;
    }

    public String getImage() {
        return image;
    }

    public void setImage(String image) {
        this.image = image;
    }

    public boolean isIs_validated() {
        return is_validated;
    }

    public void setIs_validated(boolean is_validated) {
        this.is_validated = is_validated;
    }

    public int getNbr_likes() {
        return nbr_likes;
    }

    public void setNbr_likes(int nbr_likes) {
        this.nbr_likes = nbr_likes;
    }

    public List<Commentaire> getCommentaires() {
        return commentaires;
    }

    public void setCommentaires(List<Commentaire> commentaires) {
        this.commentaires = commentaires;
    }

    public void addCommentaire(Commentaire commentaire) {
        if (this.commentaires == null) {
            this.commentaires = new ArrayList<>();
        }
        this.commentaires.add(commentaire);
        commentaire.setPost(this);
    }

    public void removeCommentaire(Commentaire commentaire) {
        this.commentaires.remove(commentaire);
        commentaire.setPost(null);
    }




    @Override
    public String toString() {
        return "Post{" +
                "id=" + id +
                ", description_post='" + description_post + '\'' +
                ", date_post=" + date_post +
                ", image='" + image + '\'' +
                ", nbr_likes=" + nbr_likes +
                ", is_validated=" + is_validated +
                ", nombre_commentaires=" + (commentaires != null ? commentaires.size() : 0) +
                '}';
    }



}
