<?xml version="1.0" encoding="UTF-8"?>

<?import java.lang.String?>
<?import javafx.collections.FXCollections?>
<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.ComboBox?>
<?import javafx.scene.control.DatePicker?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.Spinner?>
<?import javafx.scene.control.SpinnerValueFactory.IntegerSpinnerValueFactory?>
<?import javafx.scene.layout.ColumnConstraints?>
<?import javafx.scene.layout.GridPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.RowConstraints?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.text.Font?>

<VBox prefHeight="423.0" prefWidth="700.0" spacing="15" style="-fx-padding: 20; -fx-background-color: #282828;" xmlns="http://javafx.com/javafx/23.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="Controllers.ModifierReservationController">
    <Label style="-fx-font-size: 20; -fx-font-weight: bold; -fx-text-fill: #c98a56;" text="📝 Modifier Réservation">

        <font>
            <Font name="System Bold" size="20.0" />
        </font>
    </Label>

    <GridPane hgap="15" style="-fx-background-color: #282828; -fx-background-radius: 8; -fx-padding: 20; -fx-border-color: #c98a56; -fx-border-radius: 8; -fx-border-width: 1.5;" vgap="15">
        <padding>
            <Insets bottom="15.0" left="15.0" right="15.0" top="15.0" />
        </padding>

        <!-- Event Selection -->
        <Label style="-fx-font-weight: bold;-fx-text-fill: #f4f4f4; -fx-font-size: 14px;" text="🎭 Événement:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
        <ComboBox fx:id="eventComboBox" prefWidth="250.0" style="-fx-background-radius: 4;" GridPane.columnIndex="1" GridPane.rowIndex="0" />

        <!-- Booking Date -->
        <Label style="-fx-font-weight: bold;-fx-text-fill: #f4f4f4; -fx-font-size: 14px;" text="📅 Date de réservation:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
        <DatePicker fx:id="datePicker" style="-fx-background-radius: 4;" GridPane.columnIndex="1" GridPane.rowIndex="1" />

        <!-- Number of Places -->
        <Label style="-fx-font-weight: bold;-fx-text-fill: #f4f4f4; -fx-font-size: 14px;" text="🎟️ Nombre de places:" GridPane.columnIndex="0" GridPane.rowIndex="2" />
        <HBox alignment="CENTER_LEFT" spacing="5" GridPane.columnIndex="1" GridPane.rowIndex="2">
            <Spinner fx:id="placesSpinner" editable="true" style="-fx-background-color: white; -fx-border-color: #cccccc; -fx-border-radius: 4;">
                <valueFactory>
                    <SpinnerValueFactory.IntegerSpinnerValueFactory initialValue="1" max="20" min="1" />
                </valueFactory>
            </Spinner>
            <Label fx:id="availablePlacesLabel" style="-fx-text-fill: #666; -fx-font-size: 12;" />
        </HBox>

        <!-- Payment Method -->
        <Label style="-fx-font-weight: bold;-fx-text-fill: #f4f4f4; -fx-font-size: 14px;" text="💳 Moyen de paiement:" GridPane.columnIndex="0" GridPane.rowIndex="3" />
        <ComboBox fx:id="paymentComboBox" style="-fx-background-radius: 4;" GridPane.columnIndex="1" GridPane.rowIndex="3">
            <items>
                <FXCollections fx:factory="observableArrayList">
                    <String fx:value="Espèces" />
                    <String fx:value="Carte bancaire" />
                    <String fx:value="Virement" />
                    <String fx:value="Chèque" />
                </FXCollections>
            </items>
        </ComboBox>

        <!-- Status -->
      <!--  <Label style="-fx-font-weight: bold;" text="🔄 Statut:" GridPane.columnIndex="0" GridPane.rowIndex="4" />
        <ComboBox fx:id="statusComboBox" style="-fx-background-radius: 4;" GridPane.columnIndex="1" GridPane.rowIndex="4">
            <items>
                <FXCollections fx:factory="observableArrayList">
                    <String fx:value="En attente" />
                    <String fx:value="Confirmée" />
                    <String fx:value="Annulée" />
                    <String fx:value="Terminée" />
                </FXCollections>
            </items>
        </ComboBox>
-->
        <columnConstraints>
            <ColumnConstraints hgrow="SOMETIMES" minWidth="150.0" prefWidth="150.0" />
            <ColumnConstraints hgrow="SOMETIMES" minWidth="200.0" prefWidth="250.0" />
        </columnConstraints>
        <rowConstraints>
            <RowConstraints minHeight="35.0" prefHeight="35.0" vgrow="SOMETIMES" />
            <RowConstraints minHeight="35.0" prefHeight="35.0" vgrow="SOMETIMES" />
            <RowConstraints minHeight="35.0" prefHeight="35.0" vgrow="SOMETIMES" />
            <RowConstraints minHeight="35.0" prefHeight="35.0" vgrow="SOMETIMES" />
            <RowConstraints minHeight="35.0" prefHeight="35.0" vgrow="SOMETIMES" />
        </rowConstraints>
    </GridPane>

    <HBox alignment="CENTER_RIGHT" spacing="15">
        <Button onAction="#handleCancel" style="-fx-background-color: #cc5c5c; -fx-text-fill: white; -fx-font-weight: bold; -fx-padding: 8 20; -fx-background-radius: 4;" text="❌ Annuler" />
        <Button onAction="#handleUpdate" style="-fx-background-color: #c98a56; -fx-text-fill: white; -fx-font-weight: bold; -fx-padding: 8 20; -fx-background-radius: 4;" text="💾 Enregistrer les modifications" />
    </HBox>
</VBox>
