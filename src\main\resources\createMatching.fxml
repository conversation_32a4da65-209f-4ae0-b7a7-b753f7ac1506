<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.ButtonType?>
<?import javafx.scene.control.DialogPane?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.layout.ColumnConstraints?>
<?import javafx.scene.layout.GridPane?>
<?import javafx.scene.layout.RowConstraints?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.control.ComboBox?>

<?import javafx.scene.layout.HBox?>
<?import javafx.scene.control.Button?>
<DialogPane fx:id="dialogPane" headerText="add Matching" xmlns="http://javafx.com/javafx/21.0.2" xmlns:fx="http://javafx.com/fxml/1"
            fx:controller="Controllers.CreateMatchingController">
    <headerText>
        <Label text="Create New Matching" style="-fx-font-size: 16px; -fx-font-weight: bold;"/>
    </headerText>
    <content>
        <VBox spacing="10">
            <GridPane hgap="10" vgap="10">
                <Label text="Name:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                <TextField fx:id="nameField" GridPane.columnIndex="1" GridPane.rowIndex="0" />

               <Label text="Subject:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                <ComboBox fx:id="subjectComboBox" GridPane.columnIndex="1" GridPane.rowIndex="1" promptText="Select a subject">
   
                </ComboBox>

                <Label text="Table Number:" GridPane.columnIndex="0" GridPane.rowIndex="2" />
                <TextField fx:id="tableNumberField" GridPane.columnIndex="1" GridPane.rowIndex="2" />

                <Label text="Participant Count:" GridPane.columnIndex="0" GridPane.rowIndex="3" />
                <TextField fx:id="participantCountField" GridPane.columnIndex="1" GridPane.rowIndex="3" />

                <Label text="Image:" GridPane.columnIndex="0" GridPane.rowIndex="4"/>
                <HBox spacing="10" GridPane.columnIndex="1" GridPane.rowIndex="4">
                    <TextField fx:id="imagePathField" editable="false" HBox.hgrow="ALWAYS"/>
                    <Button text="Browse" onAction="#browseImage"/>
                </HBox>

                <columnConstraints>
                    <ColumnConstraints />
                    <ColumnConstraints />
                </columnConstraints>
                <rowConstraints>
                    <RowConstraints />
                    <RowConstraints />
                    <RowConstraints />
                    <RowConstraints />
                </rowConstraints>
            </GridPane>

            <!-- Label for displaying validation errors -->
            <Label fx:id="errorLabel" style="-fx-text-fill: red;" wrapText="true" />
        </VBox>
    </content>

    <buttonTypes>
        <ButtonType buttonData="CANCEL_CLOSE" text="Cancel" />
        <ButtonType buttonData="APPLY" text="Save" />
    </buttonTypes>
</DialogPane>