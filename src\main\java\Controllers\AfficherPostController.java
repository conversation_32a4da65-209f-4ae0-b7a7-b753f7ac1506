package Controllers;

import entity.Commentaire;
import entity.Post;
import entity.User;
import entity.UserSession;
import javafx.application.Platform;
import javafx.event.ActionEvent;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.Node;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.scene.layout.HBox;
import javafx.scene.layout.VBox;
import javafx.stage.Stage;
import service.CommentaireService;
import service.PostService;

import java.io.File;
import java.io.IOException;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;
import java.util.Optional;

public class AfficherPostController {

    @FXML
    private VBox postContainer;

    @FXML
    private TextField searchField;

    private List<Post> allPosts;
    private final PostService postService = new PostService();
    private final CommentaireService commentaireService = new CommentaireService();

    public void setPostContainer(String postContainer) {
        this.postContainer.setStyle(postContainer);
    }

    public void setSearchField(String searchField) {
        this.searchField.setText(searchField);
    }

    public void setAllPosts(List<Post> allPosts) {
        this.allPosts = allPosts;
        displayPosts(allPosts);
    }
    @FXML private Button connexionButton;
    @FXML
    public void initialize() {
        System.out.println("==== Initializing AfficherPostController ====");

        // Initialiser la session utilisateur et mettre à jour le bouton de connexion
        checkUserSession();

        // Afficher un message de chargement temporaire
        postContainer.getChildren().clear();
        Label loadingLabel = new Label("Chargement des posts...");
        loadingLabel.setStyle("-fx-font-size: 16px; -fx-text-fill: #888;");
        postContainer.getChildren().add(loadingLabel);

        // Charger les posts après l'initialisation complète de l'UI
        Platform.runLater(() -> {
            try {
                refreshPosts();
            } catch (Exception e) {
                System.out.println("ERROR in initialize: " + e.getMessage());
                e.printStackTrace();

                postContainer.getChildren().clear();
                Label errorLabel = new Label("Erreur de chargement: " + e.getMessage());
                errorLabel.setStyle("-fx-font-size: 16px; -fx-text-fill: red;");
                postContainer.getChildren().add(errorLabel);
            }
        });

        searchField.textProperty().addListener((obs, oldText, newText) -> {
            if (allPosts != null) {
                displayPosts(allPosts.stream()
                        .filter(p -> p.getDescription_post().toLowerCase().contains(newText.toLowerCase()))
                        .toList());
            }
        });
    }

    private void checkUserSession() {
        currentUser = UserSession.getInstance().getUser();
        if (currentUser != null) {
            // Utilisateur connecté
            connexionButton.setText("Profil");
            connexionButton.setOnAction(this::navigateToProfile);
        } else {
            // Utilisateur non connecté
            connexionButton.setText("Connexion");
            connexionButton.setOnAction(this::navigateToConnexion);
        }
    }



    private User currentUser;
    @FXML
    private void navigateToProfile(ActionEvent event) {
        loadPage("/ProfilUser.fxml", event);
    }



    // Public method for refreshing posts (can be called from other controllers)
    public void refreshPosts() {
        System.out.println("==== Refreshing posts list ====");
        try {
            PostService service = new PostService();

            // Check database connection
            if (service.getCnx() == null) {
                System.out.println("ERROR: Database connection is null");
                throw new SQLException("Database connection is null");
            }

            allPosts = service.recuperer();

            if (allPosts == null) {
                System.out.println("WARNING: service.recuperer() returned null");
                postContainer.getChildren().clear();
                Label errorLabel = new Label("Erreur: La liste des posts est null");
                errorLabel.setStyle("-fx-font-size: 16px; -fx-text-fill: red;");
                postContainer.getChildren().add(errorLabel);
                return;
            }

            System.out.println("Retrieved " + allPosts.size() + " posts");

            if (!allPosts.isEmpty()) {
                Post firstPost = allPosts.get(0);
                System.out.println("First post: id=" + firstPost.getId() +
                                  ", user_id=" + firstPost.getUser().getId() +
                                  ", description=" + firstPost.getDescription_post());
            }

            // Display posts on the JavaFX UI thread
            Platform.runLater(() -> displayPosts(allPosts));

        } catch (SQLException e) {
            System.out.println("ERROR in refreshPosts: " + e.getMessage());
            e.printStackTrace();

            Platform.runLater(() -> {
                postContainer.getChildren().clear();
                Label errorLabel = new Label("Erreur SQL: " + e.getMessage());
                errorLabel.setStyle("-fx-font-size: 16px; -fx-text-fill: red;");
                postContainer.getChildren().add(errorLabel);

                showAlert(Alert.AlertType.ERROR, "Erreur",
                         "Impossible de récupérer les posts: " + e.getMessage());
            });
        }
    }

    private void displayPosts(List<Post> posts) {

        postContainer.getChildren().clear();
        for (Post post : posts) {
            // Vérification de l'utilisateur
            String userInfo = "Auteur inconnu";
            if (post.getUser() != null) {
                userInfo = "Posté par " + post.getUser().getNom_user() + " " + post.getUser().getPrenom_user();
            }

            Label detailsLabel = new Label(userInfo);
            Label noPostsLabel = new Label("Aucun post disponible");
            noPostsLabel.setStyle("-fx-text-fill: #666; -fx-font-size: 16px;");
            postContainer.getChildren().add(noPostsLabel);
            return;
        }

        for (Post post : posts) {
            // Main post card
            HBox postCard = new HBox(20);
            postCard.setStyle("-fx-background-color: #282828; -fx-border-color: #c49b63; -fx-border-width: 1; -fx-border-radius: 0; -fx-background-radius: 0;");
            postCard.setPadding(new Insets(20));
            postCard.setAlignment(Pos.TOP_LEFT);

            // Left side - Image
            VBox imageContainer = new VBox();
            imageContainer.setPrefWidth(200);
            if (post.getImage() != null && !post.getImage().isEmpty()) {
                try {
                    ImageView imageView = new ImageView(new Image("file:src/main/resources/images/" + post.getImage()));
                    imageView.setFitWidth(200);
                    imageView.setFitHeight(150);
                    imageView.setPreserveRatio(true);
                    imageContainer.getChildren().add(imageView);
                } catch (Exception e) {
                    Label imageError = new Label("Image non disponible");
                    imageError.setStyle("-fx-text-fill: white;");
                    imageContainer.getChildren().add(imageError);
                }
            }

            // Right side - Content
            VBox contentContainer = new VBox(10);
            contentContainer.setAlignment(Pos.TOP_LEFT);

            // Post title (using description as title)
            Label titleLabel = new Label(post.getDescription_post());
            titleLabel.setStyle("-fx-text-fill: #c49b63; -fx-font-size: 18px; -fx-font-weight: bold;");
            titleLabel.setWrapText(true);

            // Post details (user and date)
            Label detailsLabel = new Label("Posté par User #" + post.getUser().getId());
            detailsLabel.setStyle("-fx-text-fill: #aaaaaa; -fx-font-size: 12px;");

            // Comments section (hidden by default)
            VBox commentsBox = new VBox(5);
            commentsBox.setStyle("-fx-background-color: #333333; -fx-padding: 10;");
            commentsBox.setVisible(false);
            commentsBox.setManaged(false);

            // Action buttons
            HBox buttonContainer = new HBox(10);
            buttonContainer.setAlignment(Pos.CENTER_LEFT);

            // Like button
            Button likeButton = new Button("Like (" + post.getNbr_likes() + ")");
            styleActionButton(likeButton, "#8D6E63");
            likeButton.setOnAction(e -> handleLike(post));

            // Comment button
            Button commentButton = new Button("Commenter");
            styleActionButton(commentButton, "#4CAF50");
            commentButton.setOnAction(e -> handleAddComment(post));

            // Show Comments button (only one declaration)
            Button showCommentsButton = new Button("Afficher commentaires");
            styleActionButton(showCommentsButton, "#2196F3");
            showCommentsButton.setOnAction(e -> {
                boolean shouldShow = !commentsBox.isVisible();
                commentsBox.setVisible(shouldShow);
                commentsBox.setManaged(shouldShow);

                if (shouldShow) {
                    loadCommentsForPost(post, commentsBox);
                }
            });

            // Modify button
            Button modifyButton = new Button("Modifier");
            styleActionButton(modifyButton, "#1976D2");
            modifyButton.setOnAction(e -> handleModify(post));

            // Delete button
            Button deleteButton = new Button("Supprimer");
            styleActionButton(deleteButton, "#D32F2F");
            deleteButton.setOnAction(e -> handleDelete(post));

            buttonContainer.getChildren().addAll(
                    likeButton,
                    commentButton,
                    showCommentsButton,
                    modifyButton,
                    deleteButton
            );

            // Add all content elements
            contentContainer.getChildren().addAll(
                    titleLabel,
                    detailsLabel,
                    buttonContainer
            );

            // Combine image and content
            postCard.getChildren().addAll(
                    imageContainer,
                    contentContainer
            );

            // Store comments box reference in the post card
            postCard.setUserData(commentsBox);

            // Create separator between posts
            Separator separator = new Separator();
            separator.setStyle("-fx-background-color: #333333;");

            // Wrap in VBox with separator
            VBox postWrapper = new VBox(postCard, commentsBox, separator);
            postWrapper.setStyle("-fx-background-color: transparent;");

            postContainer.getChildren().add(postWrapper);
        }
    }

    private void loadCommentsForPost(Post post, VBox commentsBox) {
        try {
            List<Commentaire> comments = commentaireService.recupererCommentaireParPost(post.getId());
            commentsBox.getChildren().clear();

            if (comments.isEmpty()) {
                Label noCommentsLabel = new Label("Aucun commentaire pour le moment");
                noCommentsLabel.setStyle("-fx-text-fill: #aaaaaa; -fx-font-style: italic;");
                commentsBox.getChildren().add(noCommentsLabel);
            } else {
                for (Commentaire comment : comments) {
                    VBox commentBox = createCommentBox(comment);
                    commentsBox.getChildren().add(commentBox);
                }
            }
        } catch (SQLException e) {
            showAlert(Alert.AlertType.ERROR, "Erreur", "Impossible de charger les commentaires: " + e.getMessage());
        }
    }

    private VBox createCommentBox(Commentaire comment) {
        VBox commentBox = new VBox(5);
        commentBox.setStyle("-fx-background-color: #383838; -fx-padding: 10; -fx-background-radius: 5;");

        // Comment content
        Label commentLabel = new Label(comment.getContenu());
        commentLabel.setWrapText(true);
        commentLabel.setStyle("-fx-text-fill: #e0e0e0; -fx-font-size: 14px;");

        // Comment metadata
        Label commentMeta = new Label("Par utilisateur #" + comment.getUser().getId() + " • " + comment.getDate_commentaire());
        commentMeta.setStyle("-fx-text-fill: #c49b63; -fx-font-size: 12px;");

        // Comment actions
        HBox commentActions = new HBox(10);
        commentActions.setAlignment(Pos.CENTER_RIGHT);

        Button deleteCommentBtn = new Button("Supprimer");
        deleteCommentBtn.setStyle("-fx-background-color: #D32F2F; -fx-text-fill: white; -fx-font-size: 12px; -fx-padding: 3 10;");
        deleteCommentBtn.setOnAction(e -> handleDeleteComment(comment));

        Button modifyCommentBtn = new Button("Modifier");
        modifyCommentBtn.setStyle("-fx-background-color: #1976D2; -fx-text-fill: white; -fx-font-size: 12px; -fx-padding: 3 10;");
        modifyCommentBtn.setOnAction(e -> handleModifyComment(comment));

        commentActions.getChildren().addAll(modifyCommentBtn, deleteCommentBtn);
        commentBox.getChildren().addAll(commentLabel, commentMeta, commentActions);

        return commentBox;
    }
    // Helper method to style action buttons consistently
    private void styleActionButton(Button button, String color) {
        button.setStyle(
                "-fx-background-color: " + color + "; " +
                        "-fx-text-fill: white; " +
                        "-fx-font-size: 12px; " +
                        "-fx-padding: 5 10; " +
                        "-fx-background-radius: 15; " +
                        "-fx-cursor: hand;"
        );
    }
    private void handleDelete(Post post) {
        Alert confirmDialog = new Alert(Alert.AlertType.CONFIRMATION);
        confirmDialog.setTitle("Confirmation");
        confirmDialog.setHeaderText("Supprimer le post");
        confirmDialog.setContentText("Êtes-vous sûr de vouloir supprimer ce post ?");

        Optional<ButtonType> result = confirmDialog.showAndWait();
        if (result.isPresent() && result.get() == ButtonType.OK) {
            try {
                postService.supprimer(post);
                refreshPosts();
                showAlert(Alert.AlertType.INFORMATION, "Success", "Post supprimé avec succès");
            } catch (SQLException e) {
                showAlert(Alert.AlertType.ERROR, "Error", "Erreur lors de la suppression du post: " + e.getMessage());
            }
        }
    }

    private void handleModify(Post post) {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/modifierPost.fxml"));
            Parent root = loader.load();

            ModifierPostController controller = loader.getController();
            controller.setPost(post);

            // Get the current stage
            Stage stage = (Stage) postContainer.getScene().getWindow();
            stage.setScene(new Scene(root));
            stage.show();

            // Refresh posts after modification
            stage.setOnHidden(e -> refreshPosts());
        } catch (IOException e) {
            showAlert(Alert.AlertType.ERROR, "Error", "Erreur lors de l'ouverture du formulaire de modification: " + e.getMessage());
        }
    }

    private void handleDeleteComment(Commentaire comment) {
        Alert confirmDialog = new Alert(Alert.AlertType.CONFIRMATION);
        confirmDialog.setTitle("Confirmation");
        confirmDialog.setHeaderText("Supprimer le commentaire");
        confirmDialog.setContentText("Êtes-vous sûr de vouloir supprimer ce commentaire ?");

        Optional<ButtonType> result = confirmDialog.showAndWait();
        if (result.isPresent() && result.get() == ButtonType.OK) {
            try {
                commentaireService.supprimerCommentaire(comment);
                refreshPosts();
                showAlert(Alert.AlertType.INFORMATION, "Success", "Commentaire supprimé avec succès");
            } catch (SQLException e) {
                showAlert(Alert.AlertType.ERROR, "Error", "Erreur lors de la suppression du commentaire: " + e.getMessage());
            }
        }
    }

    private void handleLike(Post post) {
        try {
            post.setNbr_likes(post.getNbr_likes() + 1);
            // Update the post in the database
            String updateLikesSQL = "UPDATE post SET nbr_likes = ? WHERE id = ?";
            PreparedStatement pst = postService.getCnx().prepareStatement(updateLikesSQL);
            pst.setInt(1, post.getNbr_likes());
            pst.setInt(2, post.getId());
            pst.executeUpdate();
            refreshPosts();
        } catch (SQLException e) {
            showAlert(Alert.AlertType.ERROR, "Error", "Erreur lors du like: " + e.getMessage());
        }
    }

    private void handleAddComment(Post post) {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/ajouterCommentaire.fxml"));
            Parent root = loader.load();
            
            AjouterCommentaireController controller = loader.getController();
            controller.setPost(post);
            
            // Get the current stage
            Stage stage = (Stage) postContainer.getScene().getWindow();
            stage.setScene(new Scene(root));
            stage.show();
            
            // Refresh posts after adding comment
            stage.setOnHidden(e -> refreshPosts());
        } catch (IOException e) {
            showAlert(Alert.AlertType.ERROR, "Error", "Erreur lors de l'ouverture du formulaire d'ajout de commentaire: " + e.getMessage());
        }
    }

    private void handleModifyComment(Commentaire comment) {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/modifierCommentaire.fxml"));
            Parent root = loader.load();
            
            ModifierCommentaireController controller = loader.getController();
            controller.setCommentaire(comment);
            
            // Get the current stage
            Stage stage = (Stage) postContainer.getScene().getWindow();
            stage.setScene(new Scene(root));
            stage.show();
            
            // Refresh posts after modification
            stage.setOnHidden(e -> refreshPosts());
        } catch (IOException e) {
            showAlert(Alert.AlertType.ERROR, "Error", "Erreur lors de l'ouverture du formulaire de modification: " + e.getMessage());
        }
    }

    private void showAlert(Alert.AlertType alertType, String title, String content) {
        Alert alert = new Alert(alertType);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(content);
        alert.showAndWait();
    }

    @FXML
    private void onAjouterClick() {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/ajouterPost.fxml"));
            Parent root = loader.load();
            
            // Get the current stage
            Stage stage = (Stage) postContainer.getScene().getWindow();
            stage.setScene(new Scene(root));
            stage.show();
            
            // Refresh posts after adding
            stage.setOnHidden(e -> refreshPosts());
        } catch (IOException e) {
            showAlert(Alert.AlertType.ERROR, "Error", "Erreur lors de l'ouverture du formulaire d'ajout: " + e.getMessage());
        }
    }

    @FXML
    private void onOpenAlbumClick() {
        System.out.println("Album ouvert");
    }

    private void modifierPost(Post post) {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getClassLoader().getResource("modifierPost.fxml"));
            Parent root = loader.load();
            
            ModifierPostController controller = loader.getController();
            controller.setPost(post);
            
            postContainer.getScene().setRoot(root);
        } catch (IOException e) {
            System.out.println("Error navigating to modifierPost: " + e.getMessage());
            showAlert(Alert.AlertType.ERROR, "Error", 
                     "Could not open modification form: " + e.getMessage());
        }
    }

    private void ajouterCommentaire(Post post) {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getClassLoader().getResource("ajouterCommentaire.fxml"));
            Parent root = loader.load();
            
            AjouterCommentaireController controller = loader.getController();
            controller.setPost(post);
            
            postContainer.getScene().setRoot(root);
        } catch (IOException e) {
            System.out.println("Error navigating to ajouterCommentaire: " + e.getMessage());
            showAlert(Alert.AlertType.ERROR, "Error", 
                     "Could not open comment form: " + e.getMessage());
        }
    }

    private void afficherCommentaires(Post post) {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getClassLoader().getResource("afficherCommentaire.fxml"));
            Parent root = loader.load();
            
            AfficherCommentaireController controller = loader.getController();
            controller.setPost(post);
            
            postContainer.getScene().setRoot(root);
        } catch (IOException e) {
            System.out.println("Error navigating to afficherCommentaire: " + e.getMessage());
            showAlert(Alert.AlertType.ERROR, "Error", 
                     "Could not open comment view: " + e.getMessage());
        }
    }

    private void modifierCommentaire(Commentaire commentaire) {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getClassLoader().getResource("modifierCommentaire.fxml"));
            Parent root = loader.load();
            
            ModifierCommentaireController controller = loader.getController();
            controller.setCommentaire(commentaire);
            
            postContainer.getScene().setRoot(root);
        } catch (IOException e) {
            System.out.println("Error navigating to modifierCommentaire: " + e.getMessage());
            showAlert(Alert.AlertType.ERROR, "Erreur", 
                     "Impossible d'ouvrir le formulaire de modification: " + e.getMessage());
        }
    }

    private void supprimerCommentaire(Commentaire commentaire) {
        try {
            CommentaireService service = new CommentaireService();
            service.supprimerCommentaire(commentaire);
            refreshPosts();
            showAlert(Alert.AlertType.INFORMATION, "Succès", "Commentaire supprimé avec succès");
        } catch (SQLException e) {
            System.out.println("Error deleting comment: " + e.getMessage());
            showAlert(Alert.AlertType.ERROR, "Erreur", 
                     "Impossible de supprimer le commentaire: " + e.getMessage());
        }
    }

    private void handleShowComments(Post post) {
        // Find the parent container of the clicked button
        for (Node postNode : postContainer.getChildren()) {
            if (postNode instanceof VBox) {
                VBox postWrapper = (VBox) postNode;
                HBox postCard = (HBox) postWrapper.getChildren().get(0); // The post card is first child

                // Get the comments box we stored in the post card's userData
                VBox commentsBox = (VBox) postCard.getUserData();

                // Toggle visibility
                boolean isVisible = commentsBox.isVisible();
                commentsBox.setVisible(!isVisible);
                commentsBox.setManaged(!isVisible);

                if (!isVisible) {
                    // Load and display comments if showing
                    try {
                        List<Commentaire> comments = commentaireService.recupererCommentaireParPost(post.getId());
                        commentsBox.getChildren().clear();

                        if (comments.isEmpty()) {
                            Label noCommentsLabel = new Label("Aucun commentaire pour le moment");
                            noCommentsLabel.setStyle("-fx-text-fill: #aaaaaa; -fx-font-style: italic;");
                            commentsBox.getChildren().add(noCommentsLabel);
                        } else {
                            for (Commentaire comment : comments) {
                                VBox commentBox = new VBox(5);
                                commentBox.setStyle("-fx-background-color: #383838; -fx-padding: 10; -fx-background-radius: 5;");

                                // Comment content
                                Label commentLabel = new Label(comment.getContenu());
                                commentLabel.setWrapText(true);
                                commentLabel.setStyle("-fx-text-fill: #e0e0e0; -fx-font-size: 14px;");

                                // Comment metadata
                                Label commentMeta = new Label("Par utilisateur #" + comment.getUser().getId() + " • " + comment.getDate_commentaire());
                                commentMeta.setStyle("-fx-text-fill: #c49b63; -fx-font-size: 12px;");

                                // Comment actions
                                HBox commentActions = new HBox(10);
                                commentActions.setAlignment(Pos.CENTER_RIGHT);

                                Button deleteCommentBtn = new Button("Supprimer");
                                deleteCommentBtn.setStyle("-fx-background-color: #D32F2F; -fx-text-fill: white; -fx-font-size: 12px; -fx-padding: 3 10;");
                                deleteCommentBtn.setOnAction(e -> handleDeleteComment(comment));

                                Button modifyCommentBtn = new Button("Modifier");
                                modifyCommentBtn.setStyle("-fx-background-color: #1976D2; -fx-text-fill: white; -fx-font-size: 12px; -fx-padding: 3 10;");
                                modifyCommentBtn.setOnAction(e -> handleModifyComment(comment));

                                commentActions.getChildren().addAll(modifyCommentBtn, deleteCommentBtn);

                                commentBox.getChildren().addAll(commentLabel, commentMeta, commentActions);
                                commentsBox.getChildren().add(commentBox);
                            }
                        }
                    } catch (SQLException e) {
                        showAlert(Alert.AlertType.ERROR, "Erreur", "Impossible de charger les commentaires: " + e.getMessage());
                    }
                }
                break; // Found our post, no need to continue searching
            }
        }
    }

    // Add these navigation methods to your AfficherPostController
    @FXML
    private void navigateToAccueil(ActionEvent event) { loadPage("/AcceuilFront.fxml", event); }
    @FXML
    private void navigateToRencontres(ActionEvent event) { loadPage("/Rencontres.fxml", event); }
    @FXML
    private void navigateToEvenements(ActionEvent event) { loadPage("/AfficherEvenementUser.fxml", event); }
    @FXML
    private void navigateToForum(ActionEvent event) { loadPage("/AfficherPost.fxml", event); }
    @FXML
    private void navigateToBoutique(ActionEvent event) { loadPage("/AfficherProduit.fxml", event); }
    @FXML
    private void navigateToConnexion(ActionEvent event) { loadPage("/AjouterUser.fxml", event); }

    private void loadPage(String fxmlPath, ActionEvent event) {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource(fxmlPath));
            Parent root = loader.load();
            Stage stage = (Stage)((Node)event.getSource()).getScene().getWindow();
            stage.setScene(new Scene(root));
            stage.show();
        } catch (IOException e) {
            showAlert(Alert.AlertType.ERROR, "Navigation Error", "Could not load page: " + e.getMessage());
        }
    }

}
