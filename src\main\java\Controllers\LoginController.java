package Controllers;

import com.google.api.client.googleapis.auth.oauth2.GoogleIdToken;
import entity.User;
import javafx.event.ActionEvent;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.scene.Node;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.control.Alert;
import javafx.scene.control.Label;
import javafx.scene.control.PasswordField;
import javafx.scene.control.TextField;
import javafx.stage.Stage;
import service.UserService;

import java.io.IOException;
import java.sql.Date;
import java.sql.SQLException;

public class LoginController {

    @FXML
    private TextField emailField;

    @FXML
    private PasswordField passwordField;

    @FXML
    private Label errorLabel;

    private final UserService userService = new UserService();

    @FXML
    private void handleLogin(ActionEvent event) {
        String email = emailField.getText().trim();
        String password = passwordField.getText().trim();

        if (email.isEmpty() || password.isEmpty()) {
            errorLabel.setText("Please enter both fields.");
            return;
        }

        try {
            User user = userService.login(email, password);
            if (user != null) {
                if (user.getIs_verified() == 0) {
                    errorLabel.setText("Veuillez vérifier votre adresse email avant de vous connecter.");
                    return;
                }

                entity.UserSession.getInstance(user);
                openDashboard(user);
            } else {
                errorLabel.setText("Invalid credentials.");
            }
        } catch (SQLException e) {
            errorLabel.setText("Database error.");
            e.printStackTrace();
        }
    }

    private void openDashboard(User user) {
        try {
            if ("ADMIN".equalsIgnoreCase(user.getRole_user())) {
                FXMLLoader loader = new FXMLLoader(getClass().getResource("/AfficherUser.fxml"));
                Parent root = loader.load();
                Stage stage = new Stage();
                stage.setScene(new Scene(root));
                stage.show();
            } else {
                FXMLLoader loader = new FXMLLoader(getClass().getResource("/ProfilUser.fxml"));
                Parent root = loader.load();

                ProfilUserController controller = loader.getController();
                controller.setUser(user);

                Stage stage = new Stage();
                stage.setScene(new Scene(root));
                stage.show();
            }

            ((Stage) emailField.getScene().getWindow()).close();
        } catch (IOException e) {
            e.printStackTrace();
            errorLabel.setText("Erreur de chargement de l'interface");
        }
    }

    @FXML
    private void handleForgotPassword(ActionEvent event) {
        try {
            Parent root = FXMLLoader.load(getClass().getResource("/ForgotPassword.fxml"));
            Stage stage = new Stage();
            stage.setTitle("Réinitialisation du mot de passe");
            stage.setScene(new Scene(root));
            stage.show();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @FXML
    private void handleSignUp(ActionEvent event) {
        try {
            Parent signupView = FXMLLoader.load(getClass().getResource("/AjouterUser.fxml"));
            Stage stage = (Stage) ((Node) event.getSource()).getScene().getWindow();
            stage.setScene(new Scene(signupView));
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @FXML
    private void handleGoogleAuth(ActionEvent event) {
        try {
            GoogleIdToken idToken = tools.GoogleAuth.authenticate();

            if (idToken != null) {
                GoogleIdToken.Payload payload = idToken.getPayload();
                String email = payload.getEmail();
                String name = (String) payload.get("given_name");
                String lastname = (String) payload.get("family_name");
                int phone = 0;

                User existingUser = userService.findByEmail(email);
                if (existingUser == null) {
                    User newUser = new User();
                    newUser.setEmail_user(email);
                    newUser.setNom_user(lastname != null ? lastname : "");
                    newUser.setPrenom_user(name != null ? name : "");
                    newUser.setPassword("GOOGLE_AUTH");
                    newUser.setRole_user("USER");
                    newUser.setIs_verified(1); // Par défaut vérifié avec Google
                    newUser.setAdresse("");
                    newUser.setDate_naissance_user(Date.valueOf("2000-01-01"));
                    newUser.setTelephone_user(phone);

                    userService.signupGoogleUser(newUser);
                    existingUser = newUser;
                }

                // NE PAS vérifier is_verified pour Google, il est toujours à 1
                entity.UserSession.getInstance(existingUser);
                openDashboard(existingUser);
            } else {
                errorLabel.setText("Google authentication failed.");
            }
        } catch (Exception e) {
            e.printStackTrace();
            errorLabel.setText("Erreur lors de l'authentification Google.");
        }
    }
}
