package Controllers;

import entity.Produit;
import entity.User;
import javafx.animation.ScaleTransition;
import javafx.application.Platform;
import javafx.concurrent.Task;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.Node;
import javafx.scene.control.*;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.scene.layout.HBox;
import javafx.scene.layout.TilePane;
import javafx.scene.layout.VBox;
import javafx.scene.text.Text;
import javafx.util.Duration;
import service.EmailService1;
import service.ProduitService;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.stage.Modality;
import javafx.stage.Stage;
import javafx.fxml.FXMLLoader;

import java.io.IOException;
import java.net.URL;
import java.sql.SQLException;
import java.util.List;
import java.util.Optional;
import java.util.ResourceBundle;

public class AffichageProduitBack implements Initializable {

    @FXML private ScrollPane scrollPane;
    @FXML private TilePane productsContainer;
    @FXML private ImageView logoImageView;
    @FXML private Button btnAddProduct;

    private Integer selectedProductId; // Pour stocker l'ID du produit à mettre en évidence
    private final ProduitService produitService = new ProduitService();

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        try {
            // Initialisation du logo
            if (logoImageView != null) {
                Image logo = new Image(getClass().getResource("/logofront.png").toExternalForm());
                logoImageView.setImage(logo);
            }

            // Configuration du style du ScrollPane
            scrollPane.setStyle("-fx-background: transparent; -fx-background-color: transparent;");

            // Chargement des produits
            loadProduits();

            // Bouton d'ajout
            btnAddProduct.setOnAction(event -> {
                try {
                    FXMLLoader loader = new FXMLLoader(getClass().getResource("/AjouterProduit.fxml"));
                    Parent root = loader.load();
                    Stage stage = new Stage();
                    stage.setScene(new Scene(root));
                    stage.setTitle("Ajouter un produit");
                    stage.initModality(Modality.APPLICATION_MODAL);
                    stage.showAndWait();
                    loadProduits(); // Rafraîchir après ajout
                } catch (IOException e) {
                    showAlert("Erreur", "Impossible d'ouvrir la fenêtre d'ajout");
                }
            });

        } catch (Exception e) {
            System.err.println("Error initializing controller: " + e.getMessage());
        }
    }

    // Méthode pour définir l'ID du produit à mettre en évidence
    public void setSelectedProductId(Integer productId) {
        this.selectedProductId = productId;
    }

    private void loadProduits() {
        try {
            productsContainer.getChildren().clear();
            List<Produit> produits = produitService.recupererProduits();

            if (produits.isEmpty()) {
                showInfoText("Aucun produit disponible");
            } else {
                for (Produit produit : produits) {
                    HBox productCard = createProductCard(produit);
                    productCard.getProperties().put("productId", produit.getId_produit());
                    productsContainer.getChildren().add(productCard);
                }

                // Si un produit est sélectionné, le mettre en évidence
                if (selectedProductId != null) {
                    Platform.runLater(() -> highlightProduct(selectedProductId));
                }
            }
        } catch (SQLException e) {
            showErrorText("Erreur lors du chargement des produits");
        }
    }

    private void highlightProduct(int productId) {
        for (Node node : productsContainer.getChildren()) {
            if (node instanceof HBox) {
                HBox productCard = (HBox) node;
                Integer currentId = (Integer) productCard.getProperties().get("productId");
                if (currentId != null && currentId == productId) {
                    // Animation de mise en évidence
                    ScaleTransition st = new ScaleTransition(Duration.millis(300), productCard);
                    st.setFromX(1);
                    st.setFromY(1);
                    st.setToX(1.05);
                    st.setToY(1.05);
                    st.setAutoReverse(true);
                    st.setCycleCount(4);
                    st.play();

                    // Faire défiler jusqu'au produit
                    Platform.runLater(() -> {
                        double targetY = productCard.getBoundsInParent().getMinY();
                        double scrollHeight = scrollPane.getContent().getBoundsInLocal().getHeight();
                        double viewportHeight = scrollPane.getViewportBounds().getHeight();
                        double vvalue = targetY / (scrollHeight - viewportHeight);
                        scrollPane.setVvalue(vvalue);
                    });
                    break;
                }
            }
        }
    }

    private HBox createProductCard(Produit produit) {
        HBox card = new HBox(20);
        card.setStyle("-fx-background-color: rgba(40,40,40,0.9); -fx-padding: 15; " +
                "-fx-border-radius: 10; -fx-background-radius: 10; " +
                "-fx-border-color: #c49b63; -fx-border-width: 1;");
        card.setPrefSize(900, 250); // Largeur 550px, Hauteur 250px (ajustable selon ce que tu veux)

        // Image du produit
        ImageView imageView = createProductImageView(produit);
        imageView.setFitWidth(180);
        imageView.setFitHeight(180);

        // VBox pour les détails
        VBox detailsBox = new VBox(10);
        detailsBox.setStyle("-fx-padding: 0 0 0 10;");

        // Nom du produit
        Text nomProduit = new Text(produit.getNom_produit());
        nomProduit.setStyle("-fx-font-weight: bold; -fx-font-size: 18; -fx-fill: #c49b63;");

        // Description
        Text description = new Text(produit.getDescription_produit());
        description.setStyle("-fx-font-size: 14; -fx-fill: white;");
        description.setWrappingWidth(250);

        // Détails (Prix, Stock, État)
        Text prix = new Text("Prix: " + produit.getPrix_produit() + " TND");
        prix.setStyle("-fx-font-size: 14; -fx-fill: white;");

        Text stock = new Text("Stock: " + produit.getStock_produit());
        stock.setStyle("-fx-font-size: 14; -fx-fill: white;");

        Text etat = new Text("État: " + produit.getEtat_produit());
        etat.setStyle("-fx-font-size: 14; -fx-fill: white;");

        // Boutons d'actions
        HBox buttonsContainer = createActionButtons(produit);

        detailsBox.getChildren().addAll(nomProduit, description, prix, stock, etat, buttonsContainer);
        card.getChildren().addAll(imageView, detailsBox);

        return card;
    }

    private ImageView createProductImageView(Produit produit) {
        ImageView imageView = new ImageView();
        try {
            String imageUrl = produit.getImage_produit();
            if (imageUrl != null && !imageUrl.isEmpty()) {
                imageView.setImage(new Image(imageUrl, 180, 180, true, true));
            } else {
                imageView.setImage(new Image(getClass().getResourceAsStream("/images/default-product.png"), 180, 180, true, true));
            }
            imageView.setPreserveRatio(true);
        } catch (Exception e) {
            System.err.println("Erreur chargement image: " + e.getMessage());
        }
        return imageView;
    }

    private HBox createActionButtons(Produit produit) {
        HBox buttonsContainer = new HBox(10);
        buttonsContainer.setStyle("-fx-alignment: center-left; -fx-padding: 10 0 0 0;");

        Button modifyButton = new Button("Modifier");
        modifyButton.setStyle("-fx-background-color: #c49b63; -fx-text-fill: black; " +
                "-fx-font-weight: bold; -fx-padding: 5 15; -fx-background-radius: 20;");
        modifyButton.setOnAction(event -> openModifyPopup(produit));

        Button deleteButton = new Button("Supprimer");
        deleteButton.setStyle("-fx-background-color: #3a1f1f; -fx-text-fill: #c49b63; " +
                "-fx-font-weight: bold; -fx-padding: 5 15; -fx-background-radius: 20;");
        deleteButton.setOnAction(event -> openDeleteConfirmation(produit));

        buttonsContainer.getChildren().addAll(modifyButton, deleteButton);
        return buttonsContainer;
    }

    // Dans AffichageProduitBack.java
    @FXML
    private void openModifyPopup(Produit produit) {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/ModifierProduitBack.fxml"));
            Parent root = loader.load();
            ModifierProduitBack controller = loader.getController();

            // Passer le produit à modifier
            controller.setProduit(produit);

            // Définir l'action après confirmation
            controller.setOnConfirmAction(() -> {
                try {
                    // 1. Rafraîchir la liste des produits
                    loadProduits();

                    // 2. Envoyer l'email de notification
                    Task<Void> emailTask = new Task<Void>() {
                        @Override
                        protected Void call() throws Exception {
                            try {
                                User productOwner = produitService.getProductOwner(produit.getId_produit());
                                if (productOwner != null && productOwner.getEmail_user() != null) {
                                    EmailService1.sendProductUpdateNotification(
                                            productOwner,
                                            produit
                                    );
                                }
                            } catch (Exception e) {
                                Platform.runLater(() ->
                                        showAlert("Avertissement", "Email non envoyé")
                                );
                            }
                            return null;
                        }
                    };

                    new Thread(emailTask).start();

                    showAlert("Succès", "Produit modifié avec succès");
                } catch (Exception e) {
                    showAlert("Erreur", "Erreur lors de la mise à jour");
                }
            });

            // Afficher la fenêtre
            Stage stage = new Stage();
            stage.setScene(new Scene(root));
            stage.initModality(Modality.APPLICATION_MODAL);
            stage.showAndWait();

        } catch (IOException e) {
            showAlert("Erreur", "Impossible d'ouvrir l'éditeur");
        }
    }


    private void openDeleteConfirmation(Produit produit) {
        Alert alert = new Alert(Alert.AlertType.CONFIRMATION);
        alert.setTitle("Confirmation de suppression");
        alert.setHeaderText("Supprimer le produit: " + produit.getNom_produit() + "?");
        alert.setContentText("Cette action est irréversible. Confirmer la suppression?");

        Optional<ButtonType> result = alert.showAndWait();
        if (result.isPresent() && result.get() == ButtonType.OK) {
            try {
                produitService.supprimerProduit(produit.getId_produit());
                loadProduits();
                showAlert("Succès", "Produit supprimé avec succès");
            } catch (SQLException e) {
                showAlert("Erreur", "Échec de la suppression: " + e.getMessage());
            }
        }
    }

    private void showAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    private void showErrorText(String message) {
        Text errorText = new Text(message);
        errorText.setStyle("-fx-font-size: 16; -fx-fill: red;");
        productsContainer.getChildren().add(errorText);
    }

    private void showInfoText(String message) {
        Text infoText = new Text(message);
        infoText.setStyle("-fx-font-size: 16; -fx-fill: white;");
        productsContainer.getChildren().add(infoText);
    }


}