package Controllers;

import entity.Commentaire;
import entity.Post;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import javafx.scene.control.Alert;
import javafx.scene.control.Button;
import javafx.scene.control.ButtonType;
import javafx.scene.control.Label;
import javafx.scene.layout.HBox;
import javafx.scene.layout.VBox;
import service.CommentaireService;

import java.io.IOException;
import java.sql.SQLException;
import java.util.List;
import java.util.Optional;

public class AfficherCommentaireController {
    private Post post;
    private final CommentaireService commentaireService = new CommentaireService();

    @FXML
    private VBox commentContainer;

    @FXML
    private Button btnRetour;

    public void setPost(Post post) {
        this.post = post;
        System.out.println("Post set with ID: " + post.getId());
        displayComments();
    }

    @FXML
    private void initialize() {
        // Initialize any necessary components
    }

    private void displayComments() {
        try {
            // Clear existing comments
            commentContainer.getChildren().clear();

            // Load comments for this specific post
            List<Commentaire> comments = commentaireService.recupererCommentaireParPost(post.getId());
            System.out.println("Found " + comments.size() + " comments for post " + post.getId());
            
            if (comments.isEmpty()) {
                Label noCommentsLabel = new Label("Aucun commentaire pour ce post");
                noCommentsLabel.setStyle("-fx-font-size: 16px; -fx-text-fill: #666;");
                commentContainer.getChildren().add(noCommentsLabel);
                return;
            }

            // Display comments
            for (Commentaire comment : comments) {
                VBox commentBox = new VBox(5);
                commentBox.setStyle("-fx-background-color: #f5f5f5; -fx-padding: 15; -fx-border-radius: 5; -fx-background-radius: 5;");

                // Comment content
                Label contentLabel = new Label(comment.getContenu());
                contentLabel.setStyle("-fx-font-size: 14px;");
                contentLabel.setWrapText(true);

                // Comment metadata
                Label metaLabel = new Label("Par utilisateur " + comment.getUser().getId() +
                                          " le " + comment.getDate_commentaire());
                metaLabel.setStyle("-fx-font-size: 12px; -fx-text-fill: #666;");

                // Action buttons
                HBox actions = new HBox(10);
                Button modifierBtn = new Button("Modifier");
                Button supprimerBtn = new Button("Supprimer");
                
                modifierBtn.setStyle("-fx-background-color: #2196F3; -fx-text-fill: white;");
                supprimerBtn.setStyle("-fx-background-color: #f44336; -fx-text-fill: white;");
                
                modifierBtn.setOnAction(e -> modifierCommentaire(comment));
                supprimerBtn.setOnAction(e -> supprimerCommentaire(comment, commentBox));
                
                actions.getChildren().addAll(modifierBtn, supprimerBtn);

                commentBox.getChildren().addAll(contentLabel, metaLabel, actions);
                commentContainer.getChildren().add(commentBox);
            }
        } catch (SQLException e) {
            System.out.println("Error loading comments: " + e.getMessage());
            e.printStackTrace();
            showAlert(Alert.AlertType.ERROR, "Erreur", 
                     "Impossible de charger les commentaires: " + e.getMessage());
        }
    }

    private void modifierCommentaire(Commentaire commentaire) {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getClassLoader().getResource("modifierCommentaire.fxml"));
            Parent root = loader.load();
            
            ModifierCommentaireController controller = loader.getController();
            controller.setCommentaire(commentaire);
            
            btnRetour.getScene().setRoot(root);
        } catch (IOException e) {
            showAlert(Alert.AlertType.ERROR, "Erreur", 
                     "Impossible d'ouvrir le formulaire de modification: " + e.getMessage());
        }
    }

    private void supprimerCommentaire(Commentaire commentaire, VBox commentBox) {
        Alert confirmDialog = new Alert(Alert.AlertType.CONFIRMATION);
        confirmDialog.setTitle("Confirmer la suppression");
        confirmDialog.setHeaderText("Êtes-vous sûr de vouloir supprimer ce commentaire?");
        confirmDialog.setContentText("Cette action ne peut pas être annulée.");
        
        Optional<ButtonType> result = confirmDialog.showAndWait();
        
        if (result.isPresent() && result.get() == ButtonType.OK) {
            try {
                commentaireService.supprimerCommentaire(commentaire);
                showAlert(Alert.AlertType.INFORMATION, "Succès", 
                         "Commentaire supprimé avec succès");
                displayComments(); // Refresh the comments list
            } catch (SQLException e) {
                showAlert(Alert.AlertType.ERROR, "Erreur", 
                         "Impossible de supprimer le commentaire: " + e.getMessage());
            }
        }
    }

    @FXML
    private void retour() {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getClassLoader().getResource("afficherPost.fxml"));
            Parent root = loader.load();
            
            // Get the controller and refresh the posts list
            AfficherPostController controller = loader.getController();
            controller.refreshPosts();
            
            // Update the scene
            btnRetour.getScene().setRoot(root);
            
        } catch (IOException e) {
            showAlert(Alert.AlertType.ERROR, "Erreur", 
                     "Impossible de revenir à la vue des posts: " + e.getMessage());
        }
    }

    private void showAlert(Alert.AlertType alertType, String title, String content) {
        Alert alert = new Alert(alertType);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(content);
        alert.showAndWait();
    }
} 