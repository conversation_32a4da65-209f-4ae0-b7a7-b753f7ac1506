<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.*?>
<?import javafx.scene.image.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>
<?import javafx.geometry.*?>

<AnchorPane prefHeight="600.0" prefWidth="900.0" style="-fx-background-color: linear-gradient(to bottom, #2a2a2a, #1e1e1e);"
            xmlns="http://javafx.com/javafx/23.0.1"
            xmlns:fx="http://javafx.com/fxml/1"
            fx:controller="Controllers.AfficherProduit">

    <HBox alignment="CENTER_LEFT" prefHeight="70.0" prefWidth="900.0"
          style="-fx-background-color: #000000; -fx-padding: 0 30 0 30;">
        <children>
            <ImageView fx:id="logoImageView" fitHeight="67.0" fitWidth="195.0"
                       pickOnBounds="true" preserveRatio="true"/>
            <Pane HBox.hgrow="ALWAYS" />

            <Button onAction="#navigateToAccueil"
                    style="-fx-background-color: transparent; -fx-text-fill: #ffffff; -fx-font-size: 14px; -fx-cursor: hand;"
                    text="Accueil"/>
            <Button onAction="#navigateToRencontres"
                    style="-fx-background-color: transparent; -fx-text-fill: #ffffff; -fx-font-size: 14px; -fx-cursor: hand;"
                    text="Rencontres"/>
            <Button onAction="#navigateToEvenements"
                    style="-fx-background-color: transparent; -fx-text-fill: #ffffff; -fx-font-size: 14px; -fx-cursor: hand;"
                    text="Événements"/>
            <Button onAction="#navigateToForum"
                    style="-fx-background-color: transparent; -fx-text-fill: #ffffff; -fx-font-size: 14px; -fx-cursor: hand;"
                    text="Forum"/>
            <Button onAction="#navigateToBoutique"
                    style="-fx-background-color: #c49b63; -fx-text-fill: #000000; -fx-font-size: 14px; -fx-cursor: hand; -fx-padding: 5 15; -fx-background-radius: 20;"
                    text="Boutique"/>
            <Button fx:id="connexionButton" onAction="#navigateToConnexion"
                    style="-fx-background-color: #c49b63; -fx-text-fill: #000000; -fx-font-size: 14px; -fx-cursor: hand; -fx-padding: 5 15; -fx-background-radius: 20;"
                    text="Connexion"/>
        </children>
    </HBox>

    <VBox layoutY="70.0" prefHeight="530.0" prefWidth="900.0"
          style="-fx-background-color: transparent;">

        <HBox alignment="CENTER" style="-fx-padding: 8 20 5 20;">
            <VBox alignment="CENTER_LEFT" spacing="2" HBox.hgrow="ALWAYS">
                <Label style="-fx-text-fill: #c49b63; -fx-font-size: 22px; -fx-font-weight: bold;"
                       text="Notre Boutique"/>
            </VBox>

            <Button fx:id="toggleViewBtn" onAction="#toggleView"
                    style="-fx-background-color: #8b6914; -fx-text-fill: white; -fx-font-size: 12px; -fx-cursor: hand; -fx-padding: 3 10; -fx-background-radius: 15;"
                    text=" Best Sellers"/>

            <Button fx:id="afficherCommandesBtn" onAction="#ouvrirAfficherCommande"
                    style="-fx-background-color: #c49b63; -fx-text-fill: #000000; -fx-font-size: 12px; -fx-cursor: hand; -fx-padding: 3 10; -fx-background-radius: 15;"
                    text="Panier"/>
        </HBox>

        <HBox alignment="CENTER" style="-fx-padding: 0 20 8 20; -fx-spacing: 10;">
            <TextField fx:id="searchField" promptText="Rechercher..."
                       style="-fx-background-color: #282828; -fx-text-fill: white; -fx-pref-width: 250; -fx-padding: 3 8; -fx-border-color: #c49b63; -fx-border-width: 0 0 1 0;"/>

            <Button fx:id="ajouterProduitBtn" onAction="#ouvrirAjoutProduit"
                    style="-fx-background-color: #c49b63; -fx-text-fill: #000; -fx-font-weight: bold; -fx-padding: 3 10; -fx-background-radius: 15; -fx-font-size: 12px;"
                    text="+ Ajouter"/>
        </HBox>

        <ScrollPane fitToWidth="true"
                    style="-fx-background: transparent; -fx-background-color: transparent; -fx-padding: 0 20 20 20;">
            <content>
                <VBox fx:id="mainContainer" spacing="20">
                    <TilePane fx:id="productsTilePane" alignment="TOP_CENTER"
                              hgap="20.0" vgap="20.0" prefColumns="3"
                              prefTileWidth="250.0" prefTileHeight="300.0"/>
                    <HBox fx:id="paginationControls" alignment="CENTER" spacing="10"
                          style="-fx-padding: 10 0 0 0;"/>

                    <VBox fx:id="bestSellersContainer"
                          style="-fx-background-color: #1a1a1a; -fx-padding: 20; -fx-spacing: 15;"
                          visible="false" managed="false"
                          prefHeight="450.0" maxHeight="450.0"> <Label text="TOP 4 DES MEILLEURES VENTES"
                                                                       style="-fx-font-weight: bold; -fx-font-size: 22; -fx-text-fill: #c49b63;"/>
                        <Separator style="-fx-background-color: #c49b63;"/>
                        <HBox fx:id="bestSellersPaginationControls" alignment="CENTER" spacing="10"
                              style="-fx-padding: 0 0 10 0;"/>
                        <TilePane fx:id="bestSellersTilePane" alignment="TOP_CENTER"
                                  hgap="20.0" vgap="20.0" prefColumns="3"
                                  prefTileWidth="250.0" prefTileHeight="300.0"/>
                    </VBox>
                </VBox>
            </content>

        </ScrollPane>
    </VBox>
</AnchorPane>