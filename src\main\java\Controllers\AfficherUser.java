package Controllers;

import entity.User;
import javafx.event.ActionEvent;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.geometry.Pos;
import javafx.geometry.Insets;

import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.control.Label;
import javafx.scene.control.Button;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.scene.layout.GridPane;
import javafx.scene.layout.HBox;
import javafx.scene.layout.VBox;
import javafx.stage.Stage;
import service.UserService;

import java.io.File;
import java.io.IOException;
import java.sql.SQLException;
import java.util.List;

public class AfficherUser {

    @FXML
    private GridPane userGrid;

    private final UserService userService = new UserService();
    private User admin;

    public void setLoggedInAdmin(User user) {
        this.admin = user;
    }

    @FXML
    public void initialize() {
        loadUsers();
    }

    private void loadUsers() {
        try {
            userGrid.getChildren().clear();
            userGrid.setHgap(15); // Espacement entre colonnes
            userGrid.setVgap(10); // Espacement entre lignes

            List<User> users = userService.recuperer();
            int row = 0;

            for (User user : users) {
                // ID
                Label idLabel = new Label(String.valueOf(user.getId()));
                idLabel.setStyle("-fx-text-fill: #555; -fx-font-size: 14px;");

                // Nom Complet
                Label nameLabel = new Label(user.getNom_user() + " " + user.getPrenom_user());
                nameLabel.setStyle("-fx-text-fill: black; -fx-font-weight: bold; -fx-font-size: 14px;");

                // Email
                Label emailLabel = new Label(user.getEmail_user());
                emailLabel.setStyle("-fx-text-fill: #444; -fx-font-size: 14px;");

                // Téléphone
                Label phoneLabel = new Label(String.valueOf(user.getTelephone_user()));
                phoneLabel.setStyle("-fx-text-fill: #444; -fx-font-size: 14px;");

                // Rôle
                Label roleLabel = new Label(user.getRole_user());
                roleLabel.setStyle("-fx-text-fill: #777; -fx-font-size: 14px;");

                // Adresse
                Label adresseLabel = new Label(user.getAdresse());
                adresseLabel.setStyle("-fx-text-fill: #444; -fx-font-size: 14px;");

                // Photo de profil
                ImageView profileImageView = new ImageView();
                if (user.getPhoto_user() != null && !user.getPhoto_user().isEmpty()) {
                    try {
                        Image image = new Image(new java.io.File(user.getPhoto_user()).toURI().toString());
                        profileImageView.setImage(image);
                        profileImageView.setFitHeight(200);
                        profileImageView.setFitWidth(200 );
                        profileImageView.setPreserveRatio(true);
                        profileImageView.setStyle("-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 5, 0.2, 0, 2);");
                    } catch (Exception e) {
                        System.out.println("❌ Failed to load image for user ID " + user.getId());
                    }
                }

                // Bouton Modifier
                ImageView editIcon = new ImageView(new Image(getClass().getResourceAsStream("/images/edit.png")));
                editIcon.setFitWidth(30);
                editIcon.setFitHeight(30);
                Button editButton = new Button("", editIcon);
                editButton.setStyle("-fx-background-color: transparent;");
                editButton.setOnAction(e -> openEditUserWindow(user));
                ImageView showIcon = new ImageView(new Image(getClass().getResourceAsStream("/images/regardez.png")));
                showIcon.setFitWidth(30);
                showIcon.setFitHeight(30);
                Button showButton = new Button("", showIcon);
                showButton.setStyle("-fx-background-color: transparent;");
                showButton.setOnAction(e -> showUserDetailsDialog(user));


                // Bouton Supprimer
                ImageView deleteIcon = new ImageView(new Image(getClass().getResourceAsStream("/images/delete.png")));
                deleteIcon.setFitWidth(30);
                deleteIcon.setFitHeight(30);
                Button deleteButton = new Button("", deleteIcon);
                deleteButton.setStyle("-fx-background-color: transparent;");
                deleteButton.setOnAction(e -> {
                    try {
                        userService.supprimer(user);
                        refreshGrid();
                    } catch (SQLException ex) {
                        ex.printStackTrace();
                    }
                });

                // Container pour les boutons
                HBox actionBox = new HBox(10, editButton, deleteButton,showButton);

                // Ajout dans le GridPane
                userGrid.add(profileImageView, 0, row);
                userGrid.add(idLabel, 1, row);
                userGrid.add(nameLabel, 2, row);
                userGrid.add(emailLabel, 3, row);
                userGrid.add(phoneLabel, 4, row);
                userGrid.add(roleLabel, 5, row);
                userGrid.add(adresseLabel, 6, row);
                userGrid.add(actionBox, 7, row);

                // Ajout de marges
                GridPane.setMargin(profileImageView, new Insets(5, 10, 5, 10));
                GridPane.setMargin(idLabel, new Insets(5, 10, 5, 10));
                GridPane.setMargin(nameLabel, new Insets(5, 10, 5, 10));
                GridPane.setMargin(emailLabel, new Insets(5, 10, 5, 10));
                GridPane.setMargin(phoneLabel, new Insets(5, 10, 5, 10));
                GridPane.setMargin(roleLabel, new Insets(5, 10, 5, 10));
                GridPane.setMargin(adresseLabel, new Insets(5, 10, 5, 10));
                GridPane.setMargin(actionBox, new Insets(5, 10, 5, 10));

                row++;
            }

        } catch (SQLException e) {
            e.printStackTrace();
        }
    }


    private void openEditUserWindow(User userToEdit) {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/AddUserDash.fxml"));
            Parent root = loader.load();

            AddUserControllerDash controller = loader.getController();
            controller.prefillForm(userToEdit);

            Stage stage = new Stage();
            stage.setTitle("Update User");
            stage.setScene(new Scene(root));
            stage.show();

        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public void refreshGrid() {
        loadUsers();
    }

    @FXML
    private void openAddUserWindow() {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/AddUserDash.fxml"));
            Parent root = loader.load();
            Stage stage = new Stage();
            stage.setTitle("Add New User");
            stage.setScene(new Scene(root));
            stage.show();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public void refrsh(ActionEvent actionEvent) {
        refreshGrid();
    }


    private void showUserDetailsDialog(User user) {
        Stage dialog = new Stage();
        dialog.setTitle("User Details");

        VBox content = new VBox(10);
        content.setPadding(new Insets(20));
        content.setStyle("-fx-background-color: white; -fx-border-radius: 10; -fx-background-radius: 10; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 10, 0, 0, 4);");

        // Image
        ImageView photo = new ImageView();
        if (user.getPhoto_user() != null && !user.getPhoto_user().isEmpty()) {
            try {
                Image image = new Image(new File(user.getPhoto_user()).toURI().toString());
                photo.setImage(image);
                photo.setFitWidth(120);
                photo.setFitHeight(120);
                photo.setPreserveRatio(true);
                photo.setStyle("-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 5, 0.2, 0, 2);");
            } catch (Exception e) {
                System.out.println("❌ Failed to load image: " + user.getPhoto_user());
            }
        }

        Label name = new Label(user.getNom_user() + " " + user.getPrenom_user());
        name.setStyle("-fx-font-size: 20px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

        Label email = new Label("Email: " + user.getEmail_user());
        Label tel = new Label("Phone: " + user.getTelephone_user());
        Label role = new Label("Role: " + user.getRole_user());
        Label address = new Label("Address: " + user.getAdresse());
        Label birth = new Label("Birth Date: " + (user.getDate_naissance_user() != null ? user.getDate_naissance_user().toString() : "N/A"));

        for (Label lbl : List.of(email, tel, role, address, birth)) {
            lbl.setStyle("-fx-font-size: 14px; -fx-text-fill: #555;");
        }

        Button closeBtn = new Button("Close");
        closeBtn.setStyle("-fx-background-color: #3498db; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 8 20;");
        closeBtn.setOnAction(e -> dialog.close());

        content.getChildren().addAll(photo, name, email, tel, role, address, birth, closeBtn);
        content.setAlignment(Pos.CENTER);

        Scene scene = new Scene(content);
        dialog.setScene(scene);
        dialog.show();
    }

}
