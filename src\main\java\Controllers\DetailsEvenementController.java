package Controllers;

import entity.Evenement;
import javafx.fxml.FXML;
import javafx.scene.control.Label;
import javafx.scene.control.TextArea;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.stage.Stage;
import service.ReservationService;

import java.text.SimpleDateFormat;

public class DetailsEvenementController {
    @FXML private Label titleLabel;
    @FXML private ImageView eventImageView;
    @FXML private Label typeLabel;
    @FXML private Label dateLabel;
    @FXML private Label priceLabel;
    @FXML private Label capacityLabel;
    @FXML private TextArea descriptionArea;
    @FXML private Label reservedPlacesLabel;
    @FXML private Label availablePlacesLabel;

    private final ReservationService reservationService = new ReservationService();
    private final SimpleDateFormat dateFormat = new SimpleDateFormat("dd MMMM yyyy");

    public void setEventData(Evenement event) {
        try {
            titleLabel.setText(event.getTitre_evenement());

            // Load image (handle both URLs and file paths)
            String imageUrl = event.getImage_event();
            if (imageUrl != null && !imageUrl.isEmpty()) {
                if (imageUrl.startsWith("http") || imageUrl.startsWith("file:")) {
                    eventImageView.setImage(new Image(imageUrl));
                } else {
                    eventImageView.setImage(new Image("file:" + imageUrl));
                }
            }

            typeLabel.setText("Type: " + event.getType_event());
            dateLabel.setText("Date: " + dateFormat.format(event.getDate_event()));
            priceLabel.setText("Prix: " + String.format("%.2f DT", event.getPrix_event()));
            capacityLabel.setText("Capacité: " + event.getCapacite_max() + " places");
            descriptionArea.setText(event.getDescription_event());

            // Calculate reserved places
            int reserved = reservationService.getReservedPlacesForEvent(event.getId());
            reservedPlacesLabel.setText(String.valueOf(reserved));
            availablePlacesLabel.setText(String.valueOf(event.getCapacite_max() - reserved));

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @FXML
    private void handleClose() {
        Stage stage = (Stage) titleLabel.getScene().getWindow();
        stage.close();
    }
}