<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>
<?import javafx.geometry.*?>
<?import javafx.scene.paint.*?>
<?import javafx.collections.FXCollections?>

<?import java.lang.String?>
<AnchorPane xmlns="http://javafx.com/javafx/17"
            xmlns:fx="http://javafx.com/fxml/1"
            fx:controller="Controllers.ModifierProduitBack"
            prefHeight="400.0"
            prefWidth="600.0"
            style="-fx-background-color: #f5f5f5; -fx-padding: 20;">

    <!-- Header Section -->
    <VBox alignment="CENTER" spacing="10" AnchorPane.topAnchor="20" AnchorPane.leftAnchor="20" AnchorPane.rightAnchor="20">
        <Label text="Modifier Produit" style="-fx-font-size: 24px; -fx-font-weight: bold; -fx-text-fill: #2a2a2a;"/>
        <Separator prefWidth="400.0" style="-fx-background-color: #c49b63;"/>
    </VBox>

    <!-- Form Section -->
    <GridPane hgap="10" vgap="15"
              AnchorPane.topAnchor="100"
              AnchorPane.leftAnchor="50"
              AnchorPane.rightAnchor="50">
        <columnConstraints>
            <ColumnConstraints prefWidth="100" halignment="RIGHT"/>
            <ColumnConstraints prefWidth="250"/>
        </columnConstraints>

        <!-- État Field -->
        <Label text="État:"
               style="-fx-font-size: 14px; -fx-text-fill: #2a2a2a;"
               GridPane.rowIndex="0" GridPane.columnIndex="0"/>

        <HBox spacing="10" alignment="CENTER_LEFT" GridPane.rowIndex="0" GridPane.columnIndex="1">
            <ComboBox fx:id="etatProduitComboBox" prefWidth="200">
                <items>
                    <FXCollections fx:factory="observableArrayList">
                        <String fx:value="Disponible"/>
                        <String fx:value="Indisponible"/>
                    </FXCollections>
                </items>
                <style>
                    -fx-font-size: 14px;
                    -fx-background-color: white;
                    -fx-border-color: #c49b63;
                    -fx-border-radius: 4px;
                    -fx-background-radius: 4px;
                </style>
            </ComboBox>
        </HBox>
    </GridPane>

    <!-- Action Buttons -->
    <HBox spacing="20" alignment="CENTER"
          AnchorPane.bottomAnchor="30"
          AnchorPane.leftAnchor="0"
          AnchorPane.rightAnchor="0">
        <Button fx:id="modifierButton" text="Enregistrer"
                onAction="#modifierProduit"
                style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: white; -fx-background-color: #c49b63; -fx-background-radius: 4px; -fx-padding: 8 20;"
        />
    </HBox>
</AnchorPane>