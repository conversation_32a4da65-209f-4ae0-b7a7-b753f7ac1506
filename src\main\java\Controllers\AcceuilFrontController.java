package Controllers;



import com.fasterxml.jackson.databind.JsonNode;

import com.fasterxml.jackson.databind.ObjectMapper;

import entity.User;

import entity.UserSession;

import javafx.event.ActionEvent;

import javafx.fxml.FXML;

import javafx.fxml.FXMLLoader;

import javafx.scene.Node;

import javafx.scene.Parent;

import javafx.scene.Scene;

import javafx.scene.control.*;

import javafx.scene.image.Image;

import javafx.scene.image.ImageView;

import javafx.stage.Stage;




import java.io.IOException;

import java.net.HttpURLConnection;

import java.net.URL;

import java.net.URLEncoder;

import java.nio.charset.StandardCharsets;

import java.util.Locale;

import java.util.ResourceBundle;



public class AcceuilFrontController {

    @FXML

    private ImageView logoImageView;

    @FXML

    private Button connexionButton;



    private User currentUser;

    @FXML

    private Label cafeExceptionText;

    @FXML

    private Label espaceCulturelText;

    @FXML

    private Label ambianceUniqueText;

    @FXML

    private TextField textToTranslate;

    @FXML private Label appTitleLabel; // Si vous avez un Label pour le titre de l'application



    @FXML private Button accueilButton;

    @FXML private Button rencontresButton;

    @FXML private Button evenementsButton;

    @FXML private Button forumButton;

    @FXML private Button boutiqueButton;

    @FXML private Label notreConceptTitleLabel;

    @FXML private Label cafeExceptionTitleLabel;

    @FXML private Label cafeExceptionDescriptionLabel;

    @FXML private Label espaceCulturelTitleLabel;

    @FXML private Label espaceCulturelDescriptionLabel;

    @FXML private Label ambianceUniqueTitleLabel;

    @FXML private Label ambianceUniqueDescriptionLabel;

    private String targetLanguage = "fr"; // Default target language

    private ResourceBundle resources;

    private String currentLanguage = "fr"; // Langue par défaut



    public void setLanguage(String languageCode) {

        currentLanguage = languageCode;

        loadResources();

        updateText();

    }



    public void initialize() {

        try {

            loadResources();

            updateText();

// Load logo

            Image logo = new Image(getClass().getResource("/logofront.png").toExternalForm());

            logoImageView.setImage(logo);



// Check user session

            checkUserSession();

        } catch (Exception e) {

            System.err.println("Error loading logo: " + e.getMessage());

        }

    }



    private void checkUserSession() {

        currentUser = UserSession.getInstance().getUser();

        if (currentUser != null) {

// User is logged in

            connexionButton.setText("Profil");

            connexionButton.setOnAction(this::navigateToProfile);



// Show welcome message



        } else {

// User is not logged in

            connexionButton.setText("Connexion");

            connexionButton.setOnAction(this::navigateToConnexion);

        }

    }



    public void setUser(User user) {

        this.currentUser = user;

        if (user != null) {

            connexionButton.setText("Profil");

            connexionButton.setOnAction(this::navigateToProfile);



        }

    }

    @FXML

    private void navigateToProfile(ActionEvent event) {

        loadPage("/ProfilUser.fxml", event);

    }

// Navigation methods

    @FXML

    private void navigateToAccueil(ActionEvent event) {

        loadPage("/AcceuilFront.fxml", event);

    }



    @FXML

    private void navigateToRencontres(ActionEvent event) {

        loadPage("/chat.fxml", event);

    }



    @FXML

    private void navigateToEvenements(ActionEvent event) {

        loadPage("/AfficherEvenementUser.fxml", event);

    }



    @FXML

    private void navigateToForum(ActionEvent event) {

        loadPage("/afficherPost.fxml", event);

    }



    @FXML

    private void navigateToBoutique(ActionEvent event) {

        loadPage("/AfficherProduit.fxml", event);

    }



    @FXML

    private void navigateToConnexion(ActionEvent event) {

        loadPage("/Login.fxml", event);

    }





    private void showAlert(String title, String message, Alert.AlertType type) {

        Alert alert = new Alert(type);

        alert.setTitle(title);

        alert.setHeaderText(null);

        alert.setContentText(message);

        alert.showAndWait();

    }

















    private void reloadCurrentPage(ActionEvent event) {

// Recharge la page actuelle

        if (event.getSource() instanceof Node) {

            Node source = (Node) event.getSource();

            Stage stage = (Stage) source.getScene().getWindow();

            stage.getScene().getRoot().setEffect(null); // Reset effects

            stage.setScene(stage.getScene()); // Refresh scene

        }

    }



// Modifiez votre méthode loadPage

    private void loadPage(String fxmlPath, ActionEvent event) {

        try {

            FXMLLoader loader = new FXMLLoader(getClass().getResource(fxmlPath));





            Parent root = loader.load();

            Stage stage = (Stage)((Node)event.getSource()).getScene().getWindow();

            stage.setScene(new Scene(root));

            stage.show();

        } catch (IOException e) {

            showAlert("Error", "Failed to load page: " + e.getMessage(), Alert.AlertType.ERROR);

        }

    }





    private void loadResources() {

        resources = ResourceBundle.getBundle("messages", new java.util.Locale(currentLanguage));

    }

    private void updateText() {

        if (appTitleLabel != null) appTitleLabel.setText(resources.getString("app.title"));

        if (accueilButton != null) accueilButton.setText(resources.getString("accueil.button"));

        if (rencontresButton != null) rencontresButton.setText(resources.getString("rencontres.button"));

        if (evenementsButton != null) evenementsButton.setText(resources.getString("evenements.button"));

        if (forumButton != null) forumButton.setText(resources.getString("forum.button"));

        if (boutiqueButton != null) boutiqueButton.setText(resources.getString("boutique.button"));

        if (connexionButton != null) connexionButton.setText(resources.getString("connexion.button"));

        if (notreConceptTitleLabel != null) notreConceptTitleLabel.setText(resources.getString("notreConcept.title"));

        if (cafeExceptionTitleLabel != null) cafeExceptionTitleLabel.setText(resources.getString("cafeException.title"));

        if (cafeExceptionDescriptionLabel != null) cafeExceptionDescriptionLabel.setText(resources.getString("cafeException.description"));

        if (espaceCulturelTitleLabel != null) espaceCulturelTitleLabel.setText(resources.getString("espaceCulturel.title"));

        if (espaceCulturelDescriptionLabel != null) espaceCulturelDescriptionLabel.setText(resources.getString("espaceCulturel.description"));

        if (ambianceUniqueTitleLabel != null) ambianceUniqueTitleLabel.setText(resources.getString("ambianceUnique.title"));

        if (ambianceUniqueDescriptionLabel != null) ambianceUniqueDescriptionLabel.setText(resources.getString("ambianceUnique.description"));

    }





}