package Controllers;

import entity.Reservation;
import entity.Evenement;
import javafx.application.Platform;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.scene.control.*;
import javafx.stage.Stage;
import javafx.stage.Window;
import javafx.util.StringConverter;
import service.ReservationService;
import service.EvenementService;

import java.sql.Date;
import java.sql.SQLException;
import java.util.HashSet;
import java.util.Optional;
import java.util.Set;

public class ModifierReservationController {

    @FXML private ComboBox<Evenement> eventComboBox;
    @FXML private DatePicker datePicker;
    @FXML private Spinner<Integer> placesSpinner;
    @FXML private ComboBox<String> paymentComboBox;

    private Reservation currentReservation;
    private Stage currentStage;
    private final ReservationService reservationService = new ReservationService();
    private final EvenementService eventService = new EvenementService();

    public void initialize() {
        setupSpinner();
        setupPaymentComboBox();
    }

    public void setStage(Stage stage) {
        this.currentStage = stage;
    }

    private void setupSpinner() {
        placesSpinner.setValueFactory(new SpinnerValueFactory.IntegerSpinnerValueFactory(1, 100, 1));
    }

    private void setupPaymentComboBox() {
        Set<String> uniquePayments = new HashSet<>();
        uniquePayments.add("Espèces");
        uniquePayments.add("Carte bancaire");
        uniquePayments.add("Virement");
        uniquePayments.add("Chèque");
        paymentComboBox.getItems().setAll(uniquePayments);
    }

    public void setReservation(Reservation reservation) {
        System.out.println("DEBUG - Setting reservation:");
        System.out.println("Reservation ID: " + reservation.getId());
        System.out.println("Event in reservation: " + reservation.getEvenement());
        if (reservation.getEvenement() != null) {
            System.out.println("Event ID: " + reservation.getEvenement().getId());
            System.out.println("Event Title: " + reservation.getEvenement().getTitre_evenement());
        }

        this.currentReservation = reservation;
        setupEventComboBox();
        populateFields();
    }

    private void setupEventComboBox() {
        try {
            ObservableList<Evenement> events = FXCollections.observableArrayList(eventService.recupererEvenement());
            eventComboBox.setItems(events);

            eventComboBox.setConverter(new StringConverter<Evenement>() {
                @Override
                public String toString(Evenement event) {
                    if (event == null) return "Sélectionner un événement";
                    return event.getTitre_evenement() + " (ID: " + event.getId() + ")";
                }
                @Override
                public Evenement fromString(String string) { return null; }
            });

            Platform.runLater(() -> {
                if (currentReservation != null && currentReservation.getEvenement() != null) {
                    Evenement currentEvent = currentReservation.getEvenement();
                    System.out.println("Current event in reservation: " + currentEvent.getId());

                    // Find matching event by ID
                    Optional<Evenement> match = events.stream()
                            .filter(e -> e.getId() == currentEvent.getId())
                            .findFirst();

                    if (match.isPresent()) {
                        eventComboBox.getSelectionModel().select(match.get());
                    } else {
                        System.err.println("Event ID " + currentEvent.getId() + " not found in database!");
                        showAlert("Erreur",
                                "L'événement original (ID: " + currentEvent.getId() +
                                        ") n'existe pas dans la base de données",
                                Alert.AlertType.ERROR);
                    }
                }
            });
        } catch (SQLException e) {
            showAlert("Erreur", "Échec du chargement des événements", Alert.AlertType.ERROR);
        }
    }
    private void populateFields() {
        if (currentReservation != null) {
            try {
                // Handle potentially null date
                if (currentReservation.getDate_booking() != null) {
                    datePicker.setValue(currentReservation.getDate_booking().toLocalDate());
                } else {
                    datePicker.setValue(null);
                    System.out.println("Warning: Reservation date is null");
                }

                // Handle places
                Integer places = currentReservation.getNbr_places();
                placesSpinner.getValueFactory().setValue(places != null ? places : 1);

                // Handle payment method
                String payment = currentReservation.getMoyen_payement_booking();
                if (payment != null && !payment.isEmpty()) {
                    paymentComboBox.getSelectionModel().select(payment);
                } else {
                    paymentComboBox.getSelectionModel().selectFirst();
                }

            } catch (Exception e) {
                System.err.println("Error populating fields: " + e.getMessage());
                e.printStackTrace();
            }
        }
    }
    @FXML
    private void handleUpdate() {
        try {
            if (currentReservation == null) {
                showAlert("Erreur", "Aucune réservation à modifier", Alert.AlertType.ERROR);
                return;
            }

            Evenement selectedEvent = eventComboBox.getSelectionModel().getSelectedItem();
            if (selectedEvent == null) {
                showAlert("Erreur", "Veuillez sélectionner un événement", Alert.AlertType.ERROR);
                return;
            }

            if (datePicker.getValue() == null) {
                showAlert("Erreur", "Veuillez sélectionner une date valide", Alert.AlertType.ERROR);
                return;
            }

            // Get the new values from the form
            Evenement newEvent = selectedEvent;
            Date newDate = Date.valueOf(datePicker.getValue());
            Integer newPlaces = placesSpinner.getValue();
            String newPayment = paymentComboBox.getValue();

            // Compare with original values
            boolean eventChanged = !newEvent.equals(currentReservation.getEvenement());
            boolean dateChanged = !newDate.equals(currentReservation.getDate_booking());
            boolean placesChanged = !newPlaces.equals(currentReservation.getNbr_places());
            boolean paymentChanged = !newPayment.equals(currentReservation.getMoyen_payement_booking());

            // Check if any changes were made
            if (!eventChanged && !dateChanged && !placesChanged && !paymentChanged) {
                showAlert("Information", "Aucune modification détectée", Alert.AlertType.INFORMATION);
                return;
            }

            // Update the reservation only if changes were made
            currentReservation.setEvenement(newEvent);
            currentReservation.setDate_booking(newDate);
            currentReservation.setNbr_places(newPlaces);
            currentReservation.setMoyen_payement_booking(newPayment);

            reservationService.modifierReservation(currentReservation);
            showAlert("Succès", "Réservation mise à jour", Alert.AlertType.INFORMATION);
            safeCloseWindow();

        } catch (SQLException e) {
            showAlert("Erreur DB", e.getMessage(), Alert.AlertType.ERROR);
        } catch (Exception e) {
            showAlert("Erreur", "Erreur inattendue", Alert.AlertType.ERROR);
        }
    }
    @FXML
    private void handleCancel() {
        safeCloseWindow();
    }

    private void safeCloseWindow() {
        if (currentStage != null) {
            currentStage.close();
        } else if (eventComboBox != null && eventComboBox.getScene() != null) {
            Window window = eventComboBox.getScene().getWindow();
            if (window instanceof Stage) {
                ((Stage) window).close();
            }
        }
    }

    private void showAlert(String title, String message, Alert.AlertType type) {
        Alert alert = new Alert(type);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}