<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.*?>
<?import javafx.scene.image.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<?import javafx.scene.shape.Circle?>
<StackPane style="-fx-background-color: linear-gradient(to bottom, #2a2a2a, #1e1e1e);" xmlns="http://javafx.com/javafx/17" xmlns:fx="http://javafx.com/fxml/1" fx:controller="Controllers.ProfilUserController">
   <VBox alignment="CENTER" spacing="20" style="-fx-background-color: #282828; -fx-padding: 30; -fx-background-radius: 15; -fx-border-color: #c49b63; -fx-border-width: 1; -fx-border-radius: 15; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 15, 0, 0, 5);" maxWidth="1000">

      <!-- Header Section -->
      <HBox alignment="CENTER" spacing="20">
         <!-- Profile Picture -->
         <StackPane>
            <ImageView fx:id="imgPhoto" fitHeight="150" fitWidth="150" preserveRatio="true" style="-fx-effect: dropshadow(gaussian, rgba(196,155,99,0.5), 10, 0, 0, 3);">
               <Image url="@/images/users-icon.png" />
            </ImageView>
            <Circle radius="75" fill="transparent" stroke="#c49b63" strokeWidth="2"/>
         </StackPane>

         <!-- User Info -->
         <VBox alignment="CENTER_LEFT" spacing="5">
            <Label fx:id="lblName" style="-fx-font-size: 28px; -fx-font-weight: bold; -fx-text-fill: #c49b63;" text="User Name">
               <font>
                  <Font name="Segoe UI Light" size="28"/>
               </font>
            </Label>
            <Label fx:id="lblLastname" style="-fx-font-size: 18px; -fx-text-fill: #95a5a6;" text="Last Name">
               <font>
                  <Font name="Segoe UI Light" size="18"/>
               </font>
            </Label>
            <Label fx:id="lblEmail" style="-fx-font-size: 14px; -fx-text-fill: #95a5a6;" text="<EMAIL>">
               <font>
                  <Font name="Segoe UI Light" size="14"/>
               </font>
            </Label>
         </VBox>
      </HBox>

      <!-- Action Buttons - Horizontal Layout -->
      <HBox alignment="CENTER" spacing="15" style="-fx-padding: 20 0 0 0;">
         <!-- Update Profile Button -->
         <Button onAction="#goToUpdatePage" style="-fx-background-color: #c49b63; -fx-text-fill: #000; -fx-font-weight: bold; -fx-background-radius: 5; -fx-padding: 10 20; -fx-cursor: hand; -fx-effect: dropshadow(gaussian, rgba(196,155,99,0.3), 5, 0, 0, 2);" text="Modifier Profil">
            <font>
               <Font name="Segoe UI Bold" size="14"/>
            </font>
            <graphic>
               <ImageView fitHeight="20" fitWidth="20" preserveRatio="true">
                  <Image url="@images/edit.png"/>
               </ImageView>
            </graphic>
         </Button>

         <!-- Home Button -->
         <Button onAction="#toHome" style="-fx-background-color: transparent; -fx-text-fill: #c49b63; -fx-font-weight: bold; -fx-background-radius: 5; -fx-padding: 10 20; -fx-cursor: hand; -fx-border-color: #c49b63; -fx-border-width: 1; -fx-border-radius: 5;" text="Accueil">
            <font>
               <Font name="Segoe UI Bold" size="14"/>
            </font>
            <graphic>
               <ImageView fitHeight="20" fitWidth="20" preserveRatio="true">
                  <Image url="@images/maison.png"/>
               </ImageView>
            </graphic>
         </Button>

         <!-- Logout Button -->
         <Button onAction="#handleLogout" style="-fx-background-color: #e74c3c; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 5; -fx-padding: 10 20; -fx-cursor: hand; -fx-effect: dropshadow(gaussian, rgba(231,76,60,0.3), 5, 0, 0, 2);" text="Déconnexion">
            <font>
               <Font name="Segoe UI Bold" size="14"/>
            </font>
            <graphic>
               <ImageView fitHeight="20" fitWidth="20" preserveRatio="true">
                  <Image url="@/images/deconnexion.png"/>
               </ImageView>
            </graphic>
         </Button>
      </HBox>

      <!-- Posts Section -->
      <VBox spacing="15" style="-fx-padding: 20 0 0 0;" alignment="TOP_CENTER">
         <Label style="-fx-font-size: 24px; -fx-font-weight: bold; -fx-text-fill: #c49b63;" text="Vos Publications">
            <font>
               <Font name="Segoe UI Light" size="24"/>
            </font>
         </Label>

         <ScrollPane fitToWidth="true" style="-fx-background: transparent; -fx-background-color: transparent;" hbarPolicy="NEVER">
            <VBox fx:id="postsContainer" spacing="15" style="-fx-padding: 10;" alignment="TOP_CENTER">
               <!-- Sample Post -->
               <VBox style="-fx-background-color: #1e1e1e; -fx-padding: 15; -fx-background-radius: 10; -fx-border-color: #c49b63; -fx-border-width: 1; -fx-border-radius: 10;">
                  <Label style="-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #c49b63;" text="Titre de publication">
                     <font>
                        <Font name="Segoe UI Light" size="18"/>
                     </font>
                  </Label>
                  <Label style="-fx-font-size: 14px; -fx-text-fill: #95a5a6; -fx-wrap-text: true;" text="Contenu de la publication exemple...">
                     <font>
                        <Font name="Segoe UI Light" size="14"/>
                     </font>
                  </Label>
               </VBox>
            </VBox>
         </ScrollPane>
      </VBox>
   </VBox>
</StackPane>