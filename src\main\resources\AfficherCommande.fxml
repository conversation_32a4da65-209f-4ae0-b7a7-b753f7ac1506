<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.*?>
<?import javafx.scene.image.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<AnchorPane prefHeight="600.0" prefWidth="900.0" style="-fx-background-color: linear-gradient(to bottom, #2a2a2a, #1e1e1e);"
            xmlns="http://javafx.com/javafx/17" xmlns:fx="http://javafx.com/fxml/1"
            fx:controller="Controllers.AfficherCommande">

    <children>
        <!-- Top Navigation Bar (identique à l'accueil) -->
        <HBox alignment="CENTER_LEFT" prefHeight="70.0" prefWidth="900.0" style="-fx-background-color: #000000; -fx-padding: 0 30 0 30;">
            <children>
                <ImageView fx:id="logoImageView" fitHeight="67.0" fitWidth="195.0" pickOnBounds="true" preserveRatio="true">
                    <image>
                    </image>
                </ImageView>
                <Pane HBox.hgrow="ALWAYS" />

                <Button onAction="#navigateToAccueil" style="-fx-background-color: transparent; -fx-text-fill: #c49b63; -fx-font-size: 14px; -fx-cursor: hand; -fx-font-weight: bold;" text="Accueil">
                    <font>
                        <Font name="Segoe UI" size="14.0" />
                    </font>
                </Button>
                <Button onAction="#navigateToRencontres" style="-fx-background-color: transparent; -fx-text-fill: #ffffff; -fx-font-size: 14px; -fx-cursor: hand;" text="Rencontres">
                    <font>
                        <Font name="Segoe UI" size="14.0" />
                    </font>
                </Button>
                <Button onAction="#navigateToEvenements" style="-fx-background-color: transparent; -fx-text-fill: #ffffff; -fx-font-size: 14px; -fx-cursor: hand;" text="Événements">
                    <font>
                        <Font name="Segoe UI" size="14.0" />
                    </font>
                </Button>
                <Button onAction="#navigateToForum" style="-fx-background-color: transparent; -fx-text-fill: #ffffff; -fx-font-size: 14px; -fx-cursor: hand;" text="Forum">
                    <font>
                        <Font name="Segoe UI" size="14.0" />
                    </font>
                </Button>
                <Button onAction="#navigateToBoutique" style="-fx-background-color: transparent; -fx-text-fill: #ffffff; -fx-font-size: 14px; -fx-cursor: hand;" text="Boutique">
                    <font>
                        <Font name="Segoe UI" size="14.0" />
                    </font>
                </Button>
                <Button fx:id="connexionButton" onAction="#navigateToConnexion"
                        style="-fx-background-color: #c49b63; -fx-text-fill: #000000; -fx-font-size: 14px; -fx-cursor: hand; -fx-padding: 5 15; -fx-background-radius: 20;"
                        text="Connexion">
                    <font>
                        <Font name="Segoe UI Bold" size="14.0" />
                    </font>
                </Button>
            </children>
        </HBox>

        <!-- Main Content - Adapté pour les commandes -->
        <VBox layoutY="70.0" prefHeight="530.0" prefWidth="900.0" style="-fx-background-color: transparent;">
            <children>
                <!-- En-tête section commandes -->
                <StackPane prefHeight="100.0" prefWidth="900.0">
                    <children>
                        <VBox alignment="CENTER" spacing="10" style="-fx-background-color: rgba(0, 0, 0, 0.5); -fx-padding: 20;">
                            <children>
                                <Label style="-fx-text-fill: #c49b63; -fx-font-size: 28px; -fx-font-weight: bold;" text="Mes Commandes">
                                    <font>
                                        <Font name="Segoe UI Light" size="28.0" />
                                    </font>
                                </Label>
                            </children>
                        </VBox>
                    </children>
                </StackPane>

                <!-- Conteneur principal avec ScrollPane et cadre de paiement -->
                <VBox style="-fx-padding: 0 20 20 20;" VBox.vgrow="ALWAYS">
                    <children>
                        <!-- Conteneur des commandes avec scroll -->
                        <ScrollPane fx:id="scrollPane" fitToWidth="true" hbarPolicy="NEVER"
                                    style="-fx-background: transparent; -fx-background-color: transparent;"
                                    VBox.vgrow="ALWAYS">
                            <content>
                                <VBox fx:id="commandesContainer" spacing="20" alignment="TOP_CENTER"
                                      style="-fx-background-color: transparent; -fx-padding: 10;">
                                </VBox>
                            </content>
                        </ScrollPane>

                        <!-- Cadre de paiement compact en bas -->
                        <HBox alignment="CENTER_RIGHT" style="-fx-padding: 10 0 0 0;">
                            <VBox style="-fx-background-color: rgba(40,40,40,0.9); -fx-padding: 10 15; -fx-border-radius: 8; -fx-background-radius: 8; -fx-border-color: #c49b63; -fx-border-width: 1; -fx-spacing: 8;">
                                <HBox alignment="CENTER_RIGHT" spacing="15">
                                    <Label fx:id="totalLabel" style="-fx-text-fill: #c49b63; -fx-font-size: 18px; -fx-font-weight: bold;" text="Total: 0.00 TND">
                                        <font>
                                            <Font name="Segoe UI" size="18.0" />
                                        </font>
                                    </Label>
                                    <Button fx:id="payerButton" onAction="#handlePayment"
                                            style="-fx-background-color: #c49b63; -fx-text-fill: black; -fx-font-weight: bold; -fx-padding: 8 20; -fx-background-radius: 15;"
                                            text="Payer">
                                        <font>
                                            <Font name="Segoe UI Bold" size="13.0" />
                                        </font>
                                    </Button>
                                </HBox>
                            </VBox>
                        </HBox>
                    </children>
                </VBox>
            </children>
        </VBox>
    </children>
</AnchorPane>