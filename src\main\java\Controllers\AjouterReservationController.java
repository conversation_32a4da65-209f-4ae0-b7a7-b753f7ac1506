package Controllers;

import entity.Reservation;
import entity.Evenement;
import entity.User;
import entity.UserSession;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.event.ActionEvent;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.scene.Node;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.stage.Stage;
import javafx.util.StringConverter;
import service.ReservationService;
import service.EvenementService;

import java.io.IOException;
import java.sql.Date;
import java.sql.SQLException;
import java.time.LocalDate;

public class AjouterReservationController {
    @FXML private ComboBox<Evenement> eventComboBox;
    @FXML private DatePicker datePicker;
    @FXML private Spinner<Integer> placesSpinner;
    @FXML private ComboBox<String> paymentComboBox;

    private int UserId;
    private final ReservationService reservationService = new ReservationService();
    private final EvenementService eventService = new EvenementService();

    @FXML private Button connexionButton; // Ajout du bouton connexion/profil



    public void initialize() {
        // Configuration complète du Spinner
        SpinnerValueFactory.IntegerSpinnerValueFactory valueFactory =
                new SpinnerValueFactory.IntegerSpinnerValueFactory(1, 20, 1);
        placesSpinner.setValueFactory(valueFactory);
        // Configuration du bouton connexion/profil
        checkUserSession();

        loadEvents();
        datePicker.setValue(LocalDate.now());
        paymentComboBox.getSelectionModel().selectFirst();
    }

    private void checkUserSession() {
        if (connexionButton == null) {
            System.err.println("Warning: connexionButton is null");
            return;
        }

        User currentUser = UserSession.getInstance().getUser();
        if (currentUser != null) {
            connexionButton.setText("Profil");
            connexionButton.setOnAction(this::navigateToProfile);
        } else {
            connexionButton.setText("Connexion");
            connexionButton.setOnAction(this::navigateToConnexion);
        }
    }
    private void navigateToProfile(ActionEvent event) {
        try {
            Parent root = FXMLLoader.load(getClass().getResource("/ProfilUser.fxml"));
            Stage stage = (Stage)((Node)event.getSource()).getScene().getWindow();
            stage.setScene(new Scene(root));
            stage.show();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private void navigateToConnexion(ActionEvent event) {
        try {
            Parent root = FXMLLoader.load(getClass().getResource("/Connexion.fxml"));
            Stage stage = (Stage)((Node)event.getSource()).getScene().getWindow();
            stage.setScene(new Scene(root));
            stage.show();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }



    public void setCurrentUser(int userId) {
        System.out.println("Setting UserId to: " + userId); // Debug
        if(userId <= 0) {
            System.out.println("Warning: Invalid UserId provided!"); // Debug
        }
        this.UserId = userId;
    }

    private void loadEvents() {
        try {
            ObservableList<Evenement> events = FXCollections.observableArrayList(eventService.recupererEvenement());
            eventComboBox.setItems(events);
            eventComboBox.setConverter(new StringConverter<Evenement>() {
                @Override
                public String toString(Evenement event) {
                    return event != null ? event.getTitre_evenement() : "";
                }

                @Override
                public Evenement fromString(String string) {
                    return null;
                }
            });
        } catch (SQLException e) {
            showAlert("Database Error", "Failed to load events: " + e.getMessage(), Alert.AlertType.ERROR);
            e.printStackTrace();
        }
    }

    @FXML
    private void handleSave() {
        try {

            // Récupérer l'utilisateur connecté depuis la session
            User currentUser = UserSession.getInstance().getUser();
            if (currentUser == null) {
                showAlert("Connexion requise", "Vous devez être connecté pour effectuer une réservation", Alert.AlertType.ERROR);
                navigateToConnexion(null);
                return;
            }

            // Vérifier que l'utilisateur existe en base
            User user = UserSession.getInstance().getUser();
            if (user == null) {
                showAlert("Erreur", "Utilisateur non trouvé", Alert.AlertType.ERROR);
                return;
            }


            Reservation reservation = new Reservation();

            Evenement selectedEvent = eventComboBox.getSelectionModel().getSelectedItem();
            if(selectedEvent == null) {
                showAlert("Erreur", "Veuillez sélectionner un événement", Alert.AlertType.ERROR);
                return;
            }


            // Set the complete objects
            reservation.setEvenement(selectedEvent);
            reservation.setUser(user);
            reservation.setDate_booking(Date.valueOf(datePicker.getValue()));
            reservation.setNbr_places(placesSpinner.getValue());
            reservation.setMoyen_payement_booking(paymentComboBox.getValue());
            reservation.setStatut_booking("En attente");

            reservationService.ajouterReservation(reservation);

            showAlert("Succès", "Réservation enregistrée avec succès", Alert.AlertType.INFORMATION);
            closeWindow();

        } catch (Exception e) {
            showAlert("Erreur", "Erreur lors de l'enregistrement: " + e.getMessage(), Alert.AlertType.ERROR);
            e.printStackTrace();
        }
    }
    @FXML
    private void handleCancel() {
        closeWindow();
    }

    private void closeWindow() {
        Stage stage = (Stage) eventComboBox.getScene().getWindow();
        stage.close();
    }

    private void showAlert(String title, String message, Alert.AlertType type) {
        Alert alert = new Alert(type);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}