package Controllers;

import javafx.fxml.FXML;
import javafx.scene.text.Text;
import javafx.stage.Stage;

public class PaymentFormController {
    @FXML private Text transactionIdText;
    @FXML private Text amountText;

    private Stage stage;

    public void setPaymentDetails(String transactionId, String amount) {
        transactionIdText.setText("Transaction: " + transactionId);
        amountText.setText("Montant: " + amount);
    }

    public void setStage(Stage stage) {
        this.stage = stage;
    }

    @FXML
    private void handleClose() {
        stage.close();
    }
}