package Controllers;

import entity.User;
import javafx.fxml.FXML;
import javafx.scene.control.*;
import javafx.scene.control.Button;
import javafx.scene.control.TextField;
import javafx.stage.FileChooser;
import javafx.stage.Stage;
import service.UserService;


import java.awt.*;
import java.io.File;
import java.sql.Date;

public class AddUserControllerDash {

    @FXML private TextField nameField;
    @FXML private TextField lastnameField;
    @FXML private TextField emailField;
    @FXML private TextField adresseField;
    @FXML private TextField telephoneField;
    @FXML private DatePicker birthDatePicker;
    @FXML private PasswordField passwordField;
    @FXML private PasswordField confirmPasswordField;
    @FXML private ComboBox<String> roleComboBox;
    @FXML private TextField photoField;
    @FXML private Button submitButton;

    private final UserService userService = new UserService();
    private User editingUser = null;

    @FXML
    public void initialize() {
        roleComboBox.getItems().addAll("CLIENT", "ADMIN");
        submitButton.setText("Add User");
    }

    @FXML
    private void handleBrowse() {
        FileChooser fileChooser = new FileChooser();
        fileChooser.setTitle("Select a Photo");
        File selectedFile = fileChooser.showOpenDialog(null);
        if (selectedFile != null) {
            photoField.setText(selectedFile.getAbsolutePath());
        }
    }

    @FXML
    private void handleAddUser() {
        String nom = nameField.getText().trim();
        String prenom = lastnameField.getText().trim();
        String email = emailField.getText().trim();
        String password = passwordField.getText();
        String confirmPassword = confirmPasswordField.getText();
        String role = roleComboBox.getValue();
        String photo = photoField.getText();
        String adresse = adresseField.getText().trim();
        String telStr = telephoneField.getText().trim();
        Date birthDate = birthDatePicker.getValue() != null ? Date.valueOf(birthDatePicker.getValue()) : null;

        if (nom.isEmpty() || prenom.isEmpty() || email.isEmpty() ||
                password.isEmpty() || confirmPassword.isEmpty() || role == null ||
                photo.isEmpty() || adresse.isEmpty() || telStr.isEmpty() || birthDate == null) {
            showAlert("Validation Error", "All fields are required.");
            return;
        }

        if (!password.equals(confirmPassword)) {
            showAlert("Validation Error", "Passwords do not match.");
            return;
        }

        int tel;
        try {
            tel = Integer.parseInt(telStr);
        } catch (NumberFormatException e) {
            showAlert("Validation Error", "Invalid phone number.");
            return;
        }

        try {
            if (editingUser != null) {
                // MODIFICATION
                editingUser.setNom_user(nom);
                editingUser.setPrenom_user(prenom);
                editingUser.setEmail_user(email);
                editingUser.setPassword(password);
                editingUser.setRole_user(role);
                editingUser.setPhoto_user(photo);
                editingUser.setAdresse(adresse);
                editingUser.setTelephone_user(tel);
                editingUser.setDate_naissance_user(birthDate);

                userService.modifier(editingUser);
                showAlert("Success", "User updated successfully.");
            } else {
                // AJOUT
                User user = new User();
                user.setNom_user(nom);
                user.setPrenom_user(prenom);
                user.setEmail_user(email);
                user.setPassword(password);
                user.setRole_user(role);
                user.setPhoto_user(photo);
                user.setAdresse(adresse);
                user.setTelephone_user(tel);
                user.setDate_naissance_user(birthDate);

                userService.signup(user);
                showAlert("Success", "User added successfully.");
            }

            // Fermer la fenêtre
            Stage stage = (Stage) nameField.getScene().getWindow();
            stage.close();

        } catch (Exception e) {
            showAlert("Error", "Failed to save user: " + e.getMessage());
            e.printStackTrace();
        }
    }

    public void prefillForm(User user) {
        this.editingUser = user;
        submitButton.setText("Update User");

        nameField.setText(user.getNom_user());
        lastnameField.setText(user.getPrenom_user());
        emailField.setText(user.getEmail_user());
        adresseField.setText(user.getAdresse());
        telephoneField.setText(String.valueOf(user.getTelephone_user()));
        birthDatePicker.setValue(user.getDate_naissance_user().toLocalDate());
        passwordField.setText(user.getPassword());
        confirmPasswordField.setText(user.getPassword());
        roleComboBox.setValue(user.getRole_user());
        photoField.setText(user.getPhoto_user());
    }

    private void showAlert(String title, String content) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(content);
        alert.showAndWait();
    }
}
