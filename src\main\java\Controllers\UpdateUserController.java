package Controllers;

import entity.User;
import javafx.event.ActionEvent;
import javafx.fxml.FXML;
import javafx.scene.control.*;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.stage.FileChooser;
import javafx.stage.Stage;
import service.UserService;


import java.io.File;
import java.sql.Date;
import java.sql.SQLException;

public class UpdateUserController {

    @FXML private TextField txtName;
    @FXML private TextField txtLastname;
    @FXML private TextField txtEmail;
    @FXML private TextField txtPhone;
    @FXML private TextField txtAddress;
    @FXML private TextField txtPhoto;
    @FXML private PasswordField txtPassword;
    @FXML private PasswordField txtConfirmPassword;
    @FXML private ComboBox<String> roleComboBox;
    @FXML private DatePicker birthDatePicker;
    @FXML private ImageView imgProfile;

    private User user;
    private Controllers.AfficherUser afficherUserController;

    public void setAfficherUserController(Controllers.AfficherUser controller) {
        this.afficherUserController = controller;
    }

    public void setUser(User user) {
        if (user != null) {
            this.user = user;
            txtName.setText(user.getNom_user());
            txtLastname.setText(user.getPrenom_user());
            txtEmail.setText(user.getEmail_user());
            txtPhone.setText(String.valueOf(user.getTelephone_user()));
            txtAddress.setText(user.getAdresse());
            txtPhoto.setText(user.getPhoto_user());
            if (user.getPhoto_user() != null && !user.getPhoto_user().isEmpty()) {
                imgProfile.setImage(new Image("file:" + user.getPhoto_user()));
            }

            if (user.getDate_naissance_user() != null)
                birthDatePicker.setValue(user.getDate_naissance_user().toLocalDate());

            roleComboBox.setValue(user.getRole_user());
            txtPassword.setText(user.getPassword());
            txtConfirmPassword.setText(user.getPassword());
        } else {
            System.out.println("❌ Received null user in UpdateUserController");
        }
    }

    @FXML
    private void updateUserInfo() throws SQLException {
        if (user != null) {
            user.setNom_user(txtName.getText());
            user.setPrenom_user(txtLastname.getText());
            user.setEmail_user(txtEmail.getText());
            user.setTelephone_user(Integer.parseInt(txtPhone.getText()));
            user.setAdresse(txtAddress.getText());
            user.setPhoto_user(txtPhoto.getText());

            if (birthDatePicker.getValue() != null)
                user.setDate_naissance_user(Date.valueOf(birthDatePicker.getValue()));

            user.setPassword(txtPassword.getText());
            user.setRole_user(roleComboBox.getValue());

            UserService userService = new UserService();
            userService.modifier(user);

            System.out.println("✅ User info updated successfully!");

            if (afficherUserController != null) {
                afficherUserController.refreshGrid();
            }


        } else {
            System.out.println("⚠️ Cannot update: user is null.");
        }
    }

    @FXML
    private void browse(ActionEvent event) {
        FileChooser fileChooser = new FileChooser();
        fileChooser.setTitle("Choose Profile Picture");
        fileChooser.getExtensionFilters().addAll(
                new FileChooser.ExtensionFilter("Image Files", "*.png", "*.jpg", "*.jpeg")
        );
        File selectedFile = fileChooser.showOpenDialog(null);
        if (selectedFile != null) {
            txtPhoto.setText(selectedFile.getAbsolutePath());
            imgProfile.setImage(new Image("file:" + selectedFile.getAbsolutePath()));
        }
    }
}
