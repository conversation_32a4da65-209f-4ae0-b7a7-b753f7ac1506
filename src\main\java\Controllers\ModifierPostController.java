package Controllers;

import entity.Post;
import javafx.event.ActionEvent;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import javafx.scene.control.Alert;
import javafx.scene.control.Button;
import javafx.scene.control.Label;
import javafx.scene.control.TextField;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.stage.FileChooser;
import service.PostService;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.sql.SQLException;
import java.util.UUID;

public class ModifierPostController {

    private final PostService ps = new PostService();
    private File selectedImageFile;
    private Post currentPost;
    private String newImageName;

    @FXML
    private Button btnModifier;

    @FXML
    private Button btnAnnuler;

    @FXML
    private Button btnChoisirImage;

    @FXML
    private TextField descriptionTxt;

    @FXML
    private ImageView imageView;
    
    @FXML
    private Label currentImageLabel;

    public void setPost(Post post) {
        this.currentPost = post;
        
        // Initialize fields with current post data
        descriptionTxt.setText(post.getDescription_post());
        currentImageLabel.setText(post.getImage());
        
        // Load current image if it exists
        try {
            String imagePath = "src/main/resources/images/" + post.getImage();
            File imageFile = new File(imagePath);
            if (imageFile.exists()) {
                Image image = new Image(imageFile.toURI().toString());
                imageView.setImage(image);
            }
        } catch (Exception e) {
            System.out.println("Error loading current image: " + e.getMessage());
        }
    }

    @FXML
    void choisirImage(ActionEvent event) {
        FileChooser fileChooser = new FileChooser();
        fileChooser.setTitle("Choisir une image");
        fileChooser.getExtensionFilters().addAll(
                new FileChooser.ExtensionFilter("Images", "*.png", "*.jpg", "*.jpeg", "*.gif")
        );
        selectedImageFile = fileChooser.showOpenDialog(btnChoisirImage.getScene().getWindow());

        if (selectedImageFile != null) {
            try {
                Image image = new Image(selectedImageFile.toURI().toString());
                imageView.setImage(image);
                
                // Generate new unique filename
                String originalFilename = selectedImageFile.getName();
                String fileExtension = originalFilename.substring(originalFilename.lastIndexOf("."));
                newImageName = UUID.randomUUID().toString() + fileExtension;
                currentImageLabel.setText("Nouvelle image sélectionnée");
            } catch (Exception e) {
                showAlert(Alert.AlertType.ERROR, "Error loading image", e.getMessage());
            }
        }
    }

    @FXML
    void modifierPost(ActionEvent event) {
        if (currentPost == null) {
            showAlert(Alert.AlertType.ERROR, "Error", "No post to modify");
            return;
        }

        if (descriptionTxt.getText().isEmpty()) {
            showAlert(Alert.AlertType.WARNING, "Validation Error", "Description cannot be empty");
            return;
        }

        try {
            // Handle image if a new one was selected
            if (selectedImageFile != null) {
                try {
                    // Copy the new image to resources directory
                    String destDir = "src/main/resources/images/";
                    Path destPath = Paths.get(destDir);
                    
                    if (!Files.exists(destPath)) {
                        Files.createDirectories(destPath);
                    }
                    
                    Files.copy(
                        selectedImageFile.toPath(),
                        Paths.get(destDir + newImageName),
                        StandardCopyOption.REPLACE_EXISTING
                    );
                    
                    // Update the post's image name
                    currentPost.setImage(newImageName);
                    
                } catch (IOException e) {
                    System.out.println("Error copying new image: " + e.getMessage());
                    e.printStackTrace();
                }
            }

            // Update the post's description
            currentPost.setDescription_post(descriptionTxt.getText());
            
            // Save changes to database
            ps.modifier(currentPost.getId(), currentPost.getDescription_post());
            
            showAlert(Alert.AlertType.INFORMATION, "Success", "Post modified successfully");
            
            // Navigate back to afficherPost view
            navigateToAfficherPost();
            
        } catch (SQLException e) {
            System.out.println("SQL Exception: " + e.getMessage());
            e.printStackTrace();
            showAlert(Alert.AlertType.ERROR, "Database Error", 
                     "Error modifying post: " + e.getMessage());
        }
    }

    @FXML
    void annuler(ActionEvent event) {
        navigateToAfficherPost();
    }
    
    private void navigateToAfficherPost() {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getClassLoader().getResource("afficherPost.fxml"));
            Parent root = loader.load();
            
            // Get controller and refresh posts list
            AfficherPostController controller = loader.getController();
            if (controller != null) {
                controller.refreshPosts();
            }
            
            // Update the scene
            btnModifier.getScene().setRoot(root);
            
        } catch (IOException e) {
            System.out.println("Navigation error: " + e.getMessage());
            e.printStackTrace();
            showAlert(Alert.AlertType.ERROR, "Navigation Error", 
                    "Could not navigate to posts view: " + e.getMessage());
        }
    }
    
    private void showAlert(Alert.AlertType alertType, String title, String content) {
        Alert alert = new Alert(alertType);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(content);
        alert.showAndWait();
    }
} 