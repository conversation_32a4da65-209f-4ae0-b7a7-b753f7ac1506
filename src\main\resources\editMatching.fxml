<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.ButtonType?>
<?import javafx.scene.control.DialogPane?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.layout.ColumnConstraints?>
<?import javafx.scene.layout.GridPane?>
<?import javafx.scene.layout.RowConstraints?>
<?import javafx.scene.layout.VBox?>

<DialogPane headerText="Edit Matching" xmlns="http://javafx.com/javafx/21.0.2" xmlns:fx="http://javafx.com/fxml/1" fx:controller="controllers.EditMatchingController">

    <content>
        <VBox spacing="10">
            <GridPane hgap="10" vgap="10">
                <Label text="Name:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                <TextField fx:id="nameField" GridPane.columnIndex="1" GridPane.rowIndex="0" />

                <Label text="Subject:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                <TextField fx:id="subjectField" GridPane.columnIndex="1" GridPane.rowIndex="1" />

                <Label text="Table Number:" GridPane.columnIndex="0" GridPane.rowIndex="2" />
                <TextField fx:id="tableNumberField" GridPane.columnIndex="1" GridPane.rowIndex="2" />

                <Label text="Participant Count:" GridPane.columnIndex="0" GridPane.rowIndex="3" />
                <TextField fx:id="participantCountField" GridPane.columnIndex="1" GridPane.rowIndex="3" />

                <columnConstraints>
                    <ColumnConstraints />
                    <ColumnConstraints />
                </columnConstraints>
                <rowConstraints>
                    <RowConstraints />
                    <RowConstraints />
                    <RowConstraints />
                    <RowConstraints />
                </rowConstraints>
            </GridPane>

            <!-- Label for displaying validation errors -->
            <Label fx:id="errorLabel" style="-fx-text-fill: red;" wrapText="true" />
        </VBox>
    </content>

    <buttonTypes>
        <ButtonType buttonData="CANCEL_CLOSE" text="Cancel" />
        <ButtonType buttonData="APPLY" text="Save" />
    </buttonTypes>
</DialogPane>