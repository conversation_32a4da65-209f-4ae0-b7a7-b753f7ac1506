<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<AnchorPane prefHeight="600.0" prefWidth="900.0" xmlns="http://javafx.com/javafx/23.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="Controllers.AfficherEvenementController">
    <children>
        <Label alignment="CENTER" layoutX="250.0" layoutY="20.0" prefWidth="400.0" text="Liste des Événements" style="
           -fx-font-size: 32px;
           -fx-font-family: 'Georgia';
           -fx-font-weight: 800;
           -fx-text-fill: linear-gradient(from 0% 0% to 100% 100%, #b07a55, #915f3a);
           -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.25), 4, 0.3, 0, 2);
           -fx-padding: 10 0 10 0;
           -fx-border-width: 0 0 2 0;
       " />

        <VBox fx:id="vboxeve" layoutX="50.0" layoutY="80.0" prefWidth="800.0" spacing="20">
            <children>
                <!-- Top bar - search on left, add button on right -->
                <HBox alignment="CENTER_LEFT" spacing="15" style="-fx-padding: 10;" HBox.hgrow="ALWAYS">
                    <TextField fx:id="searchField" prefHeight="32" prefWidth="400" promptText="Rechercher..." style="-fx-border-color: #915f3a; -fx-border-radius: 4; -fx-border-width: 1.5; -fx-background-radius: 4; -fx-padding: 5 10;" />

                    <Button fx:id="btnSearch" mnemonicParsing="false" onAction="#Rechercher" prefHeight="32" prefWidth="120" style="-fx-background-color: #8c4a3f; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 4; -fx-padding: 5 15; -fx-cursor: hand;" text="Rechercher">
                        <graphic>
                            <Text fill="white" style="-fx-font-weight: bold;">🔍</Text>
                        </graphic>
                    </Button>

                    <!-- Push Ajouter button to the far right -->
                    <Pane HBox.hgrow="ALWAYS"/>
                    <Button fx:id="btnAjout" mnemonicParsing="false" onAction="#Ajouter" prefHeight="32" prefWidth="100" style="-fx-background-color: #a67c52; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 4; -fx-padding: 5 15; -fx-cursor: hand;" text="Ajouter">
                        <graphic>
                            <Text fill="white" style="-fx-font-weight: bold;">+</Text>
                        </graphic>
                    </Button>
                </HBox>

                <ScrollPane fitToWidth="true" prefHeight="450.0">
                    <content>
                        <VBox fx:id="eventsContainer" spacing="15">
                            <!-- Event cards will be added dynamically here -->
                        </VBox>
                    </content>
                </ScrollPane>
            </children>
        </VBox>
    </children>
</AnchorPane>