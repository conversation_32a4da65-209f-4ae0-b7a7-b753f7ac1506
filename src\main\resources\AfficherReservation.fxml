<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>
<?import javafx.scene.image.*?>
<?import javafx.geometry.*?>

<AnchorPane prefHeight="700.0" prefWidth="1000.0" style="-fx-background-color: #282828;"
            xmlns="http://javafx.com/javafx/17"
            xmlns:fx="http://javafx.com/fxml/1"
            fx:controller="Controllers.AfficherReservationController">

    <!-- Barre de navigation principale -->
    <HBox alignment="CENTER_LEFT" prefHeight="70.0" prefWidth="900.0"
          style="-fx-background-color: #000000; -fx-padding: 0 30 0 30;">
        <children>
            <!-- Logo -->
            <ImageView fx:id="logoImageView" fitHeight="67.0" fitWidth="195.0"
                       pickOnBounds="true" preserveRatio="true">
                <image>
                    <!-- Image sera chargée depuis le contrôleur -->
                </image>
            </ImageView>

            <Pane HBox.hgrow="ALWAYS" /> <!-- Espace flexible -->

            <!-- Boutons de navigation -->
            <Button onAction="#navigateToAccueil"
                    style="-fx-background-color: transparent; -fx-text-fill: #ffffff; -fx-font-size: 14px; -fx-cursor: hand;"
                    text="Accueil" />

            <Button onAction="#navigateToRencontres"
                    style="-fx-background-color: transparent; -fx-text-fill: #ffffff; -fx-font-size: 14px; -fx-cursor: hand;"
                    text="Rencontres" />

            <Button onAction="#navigateToEvenements"
                    style="-fx-background-color: transparent; -fx-text-fill: #ffffff; -fx-font-size: 14px; -fx-cursor: hand;"
                    text="Événements" />

            <Button onAction="#navigateToForum"
                    style="-fx-background-color: transparent; -fx-text-fill: #ffffff; -fx-font-size: 14px; -fx-cursor: hand;"
                    text="Forum" />

            <Button onAction="#navigateToBoutique"
                    style="-fx-background-color: #c49b63; -fx-text-fill: #000000; -fx-font-size: 14px; -fx-cursor: hand; -fx-padding: 5 15; -fx-background-radius: 20;"
                    text="Boutique" />

            <!-- Bouton Connexion/Profil -->
            <Button fx:id="connexionButton" onAction="#navigateToConnexion"
                    style="-fx-background-color: #c49b63; -fx-text-fill: #000000; -fx-font-size: 14px; -fx-cursor: hand; -fx-padding: 5 15; -fx-background-radius: 20;"
                    text="Connexion">
                <font>
                    <Font name="Segoe UI Bold" size="14.0" />
                </font>
            </Button>
        </children>
    </HBox>

    <!-- En-tête de page -->
    <HBox layoutX="50.0" layoutY="20.0" prefWidth="900.0" alignment="CENTER" spacing="0">
        <children>
            <StackPane prefWidth="900.0" alignment="CENTER_LEFT">
                <children>
                    <!-- Titre de la page -->
                    <Label text="Mes Réservations" StackPane.alignment="CENTER"
                           style="
                               -fx-font-size: 32px;
                               -fx-font-family: 'Georgia';
                               -fx-font-weight: 800;
                               -fx-text-fill: #c98a56;
                               -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.25), 4, 0.3, 0, 2);
                               -fx-padding: 10 0 10 0;
                               -fx-border-width: 0 0 2 0;
                           " />

                    <!-- Bouton Ajouter Réservation -->
                    <Button fx:id="addReservationBtn" mnemonicParsing="false"
                            onAction="#handleAddReservation"
                            style="-fx-background-color: #c98a56; -fx-text-fill: white; -fx-font-weight: bold; -fx-padding: 8 15;"
                            text="+ Ajouter Réservation"
                            StackPane.alignment="CENTER_RIGHT" />
                </children>
            </StackPane>
        </children>
    </HBox>

    <!-- Zone de contenu principal -->
    <ScrollPane layoutX="50.0" layoutY="100.0" prefWidth="900.0" prefHeight="600.0"
                style="-fx-background: #f4f4f4; -fx-border-color: #c98a56;">
        <content>
            <VBox fx:id="reservationsContainer" spacing="20" style="-fx-padding: 20;" />
        </content>
    </ScrollPane>
</AnchorPane>