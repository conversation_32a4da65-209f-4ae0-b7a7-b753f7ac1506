package service;

import entity.Post;
import entity.Commentaire;
import entity.User;
import tools.MyDataBase;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;


public class PostService implements IServicePost<Post> {
    Connection cnx;
    String sql;
    private Statement st;
    private PreparedStatement ste;

    public PostService() {
        cnx = MyDataBase.getInstance().getCnx();
    }

    public Connection getCnx() {
        return cnx;
    }

    @Override
    public void ajouter(Post post) throws SQLException {
        sql = "INSERT INTO post (user_id, description_post, date_post, nbr_likes, image, is_validated) VALUES (?, ?, ?, ?, ?, ?)";

        ste = cnx.prepareStatement(sql);
        ste.setInt(1, post.getUser().getId());
        ste.setString(2, post.getDescription_post());
        ste.setDate(3, post.getDate_post());
        ste.setInt(4, post.getNbr_likes());
        ste.setString(5, post.getImage());
        ste.setBoolean(6, post.isIs_validated());

        int rowsAffected = ste.executeUpdate();
        System.out.println("Post ajouté avec succès ! Rows affected: " + rowsAffected);
    }

    @Override
    public void modifier(int id, String description_post) throws SQLException {
        sql = "UPDATE post SET description_post=?, date_post = CURRENT_DATE WHERE id=?";
        ste = cnx.prepareStatement(sql);
        ste.setString(1, description_post);
        ste.setInt(2, id);

        ste.executeUpdate();
        System.out.println("Post modifié avec succès !");
    }

    @Override
    public void supprimer(Post post) throws SQLException {
        boolean autoCommitInitialValue = true;
        PreparedStatement commentStmt = null;
        PreparedStatement postStmt = null;

        try {
            // Save initial autocommit value
            autoCommitInitialValue = cnx.getAutoCommit();

            // Disable auto-commit to manage transaction manually
            cnx.setAutoCommit(false);

            // Méthode 1: Suppression directe des commentaires
            try {
                // 1. Supprimer les commentaires associés
                String deleteCommentsSQL = "DELETE FROM commentaire WHERE post_id = ?";
                System.out.println("Tentative 1: Suppression des commentaires pour le post ID: " + post.getId());

                commentStmt = cnx.prepareStatement(deleteCommentsSQL);
                commentStmt.setInt(1, post.getId());
                int commentsDeleted = commentStmt.executeUpdate();
                System.out.println("Supprimé " + commentsDeleted + " commentaires");

                // 2. Supprimer le post
                String deletePostSQL = "DELETE FROM post WHERE id = ?";
                postStmt = cnx.prepareStatement(deletePostSQL);
                postStmt.setInt(1, post.getId());
                int postsDeleted = postStmt.executeUpdate();
                System.out.println("Post supprimé avec succès! Lignes affectées: " + postsDeleted);

                // Commit la transaction
                cnx.commit();
                return; // La suppression a réussi, on peut sortir

            } catch (SQLException e) {
                // Si la première méthode échoue, rollback et essayer la méthode 2
                if (cnx != null) {
                    try {
                        cnx.rollback();
                    } catch (SQLException ignored) {}
                }
                System.out.println("Erreur avec la méthode 1: " + e.getMessage());
                // Continuer avec méthode 2
            }

            // Méthode 2: Utilisation de requêtes directes avec une nouvelle connexion
            try {
                // Obtenir une nouvelle connexion
                cnx = MyDataBase.getInstance().getCnx();
                cnx.setAutoCommit(false);

                // Exécuter les requêtes avec Statement au lieu de PreparedStatement
                Statement stmt = cnx.createStatement();

                // Supprimer d'abord les commentaires
                String directDeleteComments = "DELETE FROM commentaire WHERE post_id = " + post.getId();
                int comments = stmt.executeUpdate(directDeleteComments);
                System.out.println("Tentative 2: Supprimé " + comments + " commentaires");

                // Puis supprimer le post
                String directDeletePost = "DELETE FROM post WHERE id = " + post.getId();
                int posts = stmt.executeUpdate(directDeletePost);
                System.out.println("Post supprimé avec succès (méthode 2)! Lignes affectées: " + posts);

                // Commit la transaction
                cnx.commit();
                return; // La suppression a réussi, on peut sortir

            } catch (SQLException e) {
                // Si la deuxième méthode échoue aussi, rollback et essayer la méthode 3
                if (cnx != null) {
                    try {
                        cnx.rollback();
                    } catch (SQLException ignored) {}
                }
                System.out.println("Erreur avec la méthode 2: " + e.getMessage());
                // Continuer avec méthode 3
            }

            // Méthode 3: Désactiver temporairement les contraintes de clé étrangère
            try {
                // Obtenir une nouvelle connexion
                cnx = MyDataBase.getInstance().getCnx();
                cnx.setAutoCommit(false);

                Statement stmt = cnx.createStatement();

                // Désactiver temporairement les contraintes de clé étrangère
                stmt.execute("SET FOREIGN_KEY_CHECKS = 0");

                // Supprimer le post (maintenant possible sans contraintes)
                String forceDeletePost = "DELETE FROM post WHERE id = " + post.getId();
                int result = stmt.executeUpdate(forceDeletePost);
                System.out.println("Tentative 3: Post supprimé (avec contraintes désactivées)! Lignes affectées: " + result);

                // Supprimer les commentaires orphelins
                String cleanupComments = "DELETE FROM commentaire WHERE post_id = " + post.getId();
                int orphans = stmt.executeUpdate(cleanupComments);
                System.out.println("Nettoyage des commentaires orphelins: " + orphans);

                // Réactiver les contraintes
                stmt.execute("SET FOREIGN_KEY_CHECKS = 1");

                // Commit la transaction
                cnx.commit();

            } catch (SQLException e) {
                // Si la troisième méthode échoue aussi, rollback et lever l'exception
                if (cnx != null) {
                    try {
                        cnx.rollback();
                        // Assurons-nous que les contraintes sont réactivées en cas d'erreur
                        Statement stmt = cnx.createStatement();
                        stmt.execute("SET FOREIGN_KEY_CHECKS = 1");
                    } catch (SQLException ignored) {}
                }
                throw new SQLException("Impossible de supprimer le post après plusieurs tentatives: " + e.getMessage(), e);
            }

        } finally {
            // S'assurer que les contraintes sont réactivées, au cas où
            try {
                Statement stmt = cnx.createStatement();
                stmt.execute("SET FOREIGN_KEY_CHECKS = 1");
            } catch (SQLException ignored) {}

            // Rétablir auto-commit
            if (cnx != null) {
                try {
                    cnx.setAutoCommit(autoCommitInitialValue);
                } catch (SQLException ignored) {}
            }

            // Fermer les statements
            if (commentStmt != null) {
                try {
                    commentStmt.close();
                } catch (SQLException ignored) {}
            }

            if (postStmt != null) {
                try {
                    postStmt.close();
                } catch (SQLException ignored) {}
            }
        }
    }


  /*  @Override
    public List<Post> recuperer() throws SQLException {
        List<Post> posts = new ArrayList<>();
        String sql = "SELECT p.*, u.id as user_id, u.nom as user_nom FROM post p " +
                "LEFT JOIN user u ON p.user_id = u.id " +
                "ORDER BY p.date_post DESC";
        List<Commentaire> commentaires = new ArrayList<>();

        // 1. Récupérer tous les commentaires
        String sqlComments = "SELECT * FROM commentaire ORDER BY date_commentaire DESC";
        Statement stComments = null;
        ResultSet rsComments = null;

        try {
            stComments = cnx.createStatement();
            rsComments = stComments.executeQuery(sqlComments);

            while (rsComments.next()) {
                Commentaire commentaire = new Commentaire();
                commentaire.setId(rsComments.getInt("id"));
                commentaire.setPost_id(rsComments.getInt("post_id"));
                commentaire.setContenu(rsComments.getString("contenu"));
                commentaire.setDate_commentaire(rsComments.getString("date_commentaire"));
                commentaire.setNbr_like_commentaire(rsComments.getInt("nbr_like_commentaire"));

                commentaires.add(commentaire);
            }
        } finally {
            if (rsComments != null) rsComments.close();
            if (stComments != null) stComments.close();
        }

        // 2. Récupérer tous les posts
        String sqlPosts = "SELECT * FROM post ORDER BY date_post DESC";
        Statement stPosts = null;
        ResultSet rsPosts = null;

        try {
            stPosts = cnx.createStatement();
            rsPosts = stPosts.executeQuery(sqlPosts);

            while (rsPosts.next()) {
                Post post = new Post();
                post.setId(rsPosts.getInt("id"));
                post.setDescription_post(rsPosts.getString("description_post"));
                post.setDate_post(rsPosts.getDate("date_post"));
                post.setNbr_likes(rsPosts.getInt("nbr_likes"));
                post.setImage(rsPosts.getString("image"));
                post.setIs_validated(rsPosts.getBoolean("is_validated"));

                // Créer une nouvelle liste pour les commentaires de ce post
                List<Commentaire> commentairesPost = new ArrayList<>();

                // Parcourir la liste des commentaires pour trouver ceux du post
                for (Commentaire c : commentaires) {
                    if (c.getPost_id() == post.getId()) {
                        commentairesPost.add(c);
                    }
                }

                post.setCommentaires(commentairesPost);
                posts.add(post);
            }
        } finally {
            if (rsPosts != null) rsPosts.close();
            if (stPosts != null) stPosts.close();
        }

        System.out.println("Récupéré " + posts.size() + " posts et " + commentaires.size() + " commentaires");
        return posts;
    }

            String query = "SELECT p.*, u.id as user_id, u.nom as user_nom FROM produit p LEFT JOIN user u ON p.user_id = u.id";
*/
@Override
    public List<Post> recuperer() throws SQLException {
        List<Post> posts = new ArrayList<>();

    String sql = "SELECT p.*, u.id as user_id, u.nom_user as nom, u.prenom_user as prenom FROM post p " +
            "LEFT JOIN user u ON p.user_id = u.id " +
            "ORDER BY p.date_post DESC";

        try (Statement st = cnx.createStatement();
             ResultSet rs = st.executeQuery(sql)) {

            while (rs.next()) {
                Post post = new Post();
                post.setId(rs.getInt("id"));
                post.setDescription_post(rs.getString("description_post"));
                post.setDate_post(rs.getDate("date_post"));
                post.setNbr_likes(rs.getInt("nbr_likes"));
                post.setImage(rs.getString("image"));
                post.setIs_validated(rs.getBoolean("is_validated"));

                // Créer et assigner l'utilisateur
                User user = new User();
                user.setId(rs.getInt("user_id"));
                user.setNom_user(rs.getString("nom"));
                user.setPrenom_user(rs.getString("prenom"));
                post.setUser(user);

                posts.add(post);
            }
        }

        return posts;
    }

}
