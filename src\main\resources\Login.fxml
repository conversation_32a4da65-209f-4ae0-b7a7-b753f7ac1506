<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Hyperlink?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.PasswordField?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.image.Image?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.StackPane?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.text.Font?>

<StackPane prefHeight="600.0" prefWidth="900.0" style="-fx-background-color: linear-gradient(to bottom, #2a2a2a, #1e1e1e);" xmlns="http://javafx.com/javafx/23.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="Controllers.LoginController">
    <VBox alignment="CENTER" maxHeight="500" maxWidth="400" spacing="20" style="-fx-padding: 40; -fx-background-color: #282828; -fx-background-radius: 10; -fx-border-color: #c49b63; -fx-border-width: 1; -fx-border-radius: 10; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 15, 0, 0, 5);">

        <!-- Logo Placeholder -->
        <ImageView fitHeight="80" fitWidth="80" preserveRatio="true">
            <Image url="@images/logo.png" />
        </ImageView>

        <!-- Title -->
        <VBox alignment="CENTER" spacing="5">
            <Label style="-fx-font-size: 28px; -fx-font-weight: bold; -fx-text-fill: #c49b63;" text="Welcome Back">
                <font>
                    <Font name="Segoe UI Light" size="28.0" />
                </font>
            </Label>
            <Label style="-fx-font-size: 14px; -fx-text-fill: #95a5a6;" text="Sign in to continue">
                <font>
                    <Font name="Segoe UI Light" size="14.0" />
                </font>
            </Label>
        </VBox>

        <!-- Email -->
        <VBox maxWidth="350" spacing="5">
            <Label style="-fx-text-fill: #c49b63; -fx-font-size: 12px;" text="Email">
                <font>
                    <Font name="Segoe UI" size="12.0" />
                </font>
            </Label>
            <TextField fx:id="emailField" promptText="Enter your email" style="-fx-background-radius: 5; -fx-padding: 12; -fx-background-color: #1e1e1e; -fx-border-color: #c49b63; -fx-border-radius: 5; -fx-text-fill: white;" />
        </VBox>

        <!-- Password -->
        <VBox maxWidth="350" spacing="5">
            <Label style="-fx-text-fill: #c49b63; -fx-font-size: 12px;" text="Password">
                <font>
                    <Font name="Segoe UI" size="12.0" />
                </font>
            </Label>
            <PasswordField fx:id="passwordField" promptText="Enter your password" style="-fx-background-radius: 5; -fx-padding: 12; -fx-background-color: #1e1e1e; -fx-border-color: #c49b63; -fx-border-radius: 5; -fx-text-fill: white;" />

            <HBox alignment="CENTER_RIGHT">
                <Hyperlink onAction="#handleForgotPassword" style="-fx-text-fill: #c49b63; -fx-font-size: 12px; -fx-border-width: 0;" text="Forgot Password?">
                    <font>
                        <Font name="Segoe UI" size="12.0" />
                    </font>
                </Hyperlink>
            </HBox>
        </VBox>

        <!-- Login Button -->
        <Button maxWidth="350" onAction="#handleLogin" style="-fx-background-color: #c49b63; -fx-text-fill: #000000; -fx-font-weight: bold; -fx-background-radius: 5; -fx-padding: 12; -fx-cursor: hand;" text="Sign In">
            <font>
                <Font name="Segoe UI Bold" size="14.0" />
            </font>
        </Button>

        <HBox alignment="CENTER" spacing="5">
            <Label style="-fx-text-fill: #95a5a6; -fx-font-size: 13px;" text="Don't have an account?">
                <font>
                    <Font name="Segoe UI Light" size="13.0" />
                </font>
            </Label>
            <Hyperlink onAction="#handleSignUp" style="-fx-text-fill: #c49b63; -fx-font-weight: bold; -fx-font-size: 13px; -fx-border-width: 0;" text="Sign Up">
                <font>
                    <Font name="Segoe UI Bold" size="13.0" />
                </font>
            </Hyperlink>
        </HBox>
      <Button fx:id="google" onAction="#handleGoogleAuth" mnemonicParsing="false" prefHeight="65.0" prefWidth="318.0" style="-fx-background-color: white;" text="Authentification Par Google">
         <graphic>
            <ImageView fitHeight="25.0" fitWidth="28.0" pickOnBounds="true" preserveRatio="true">
               <image>
                  <Image url="@images/gmail.png" />
               </image>
            </ImageView>
         </graphic>
         <font>
            <Font name="System Bold" size="13.0" />
         </font>
      </Button>

        <!-- Error Label -->
        <Label fx:id="errorLabel" style="-fx-font-size: 13px; -fx-text-fill: #e74c3c;" />
    </VBox>
</StackPane>
