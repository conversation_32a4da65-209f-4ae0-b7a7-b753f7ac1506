package Controllers;

import entity.Reservation;
import entity.User;
import entity.Evenement;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.scene.control.*;
import javafx.stage.Stage;
import javafx.util.StringConverter;
import service.ReservationService;
import service.EvenementService;
import service.UserService;

import java.sql.Date;
import java.sql.SQLException;
import java.time.LocalDate;

public class AjouterReservationAdminController {
    @FXML private ComboBox<User> userComboBox;
    @FXML private ComboBox<Evenement> eventComboBox;
    @FXML private DatePicker datePicker;
    @FXML private Spinner<Integer> placesSpinner;
    @FXML private ComboBox<String> paymentComboBox;
    @FXML private ComboBox<String> statusComboBox;

    private final ReservationService reservationService = new ReservationService();
    private final UserService userService = new UserService();
    private final EvenementService eventService = new EvenementService();

    public void initialize() {
        loadUsers();
        loadEvents();
        datePicker.setValue(LocalDate.now());
        paymentComboBox.getSelectionModel().selectFirst();
        statusComboBox.getSelectionModel().selectFirst();
    }

    private void loadUsers() {
        try {
            ObservableList<User> users = FXCollections.observableArrayList(userService.getAllUsers());
            userComboBox.setItems(users);
            userComboBox.setConverter(new StringConverter<User>() {
                @Override
                public String toString(User user) {
                    return user != null ? user.getNom_user() + " " + user.getPrenom_user() : "";
                }
                @Override
                public User fromString(String string) {
                    return null;
                }
            });
        } catch (SQLException e) {
            showAlert("Database Error", "Failed to load users: " + e.getMessage(), Alert.AlertType.ERROR);
            e.printStackTrace();
        }
    }

    private void loadEvents() {
        try {
            ObservableList<Evenement> events = FXCollections.observableArrayList(eventService.recupererEvenement());
            eventComboBox.setItems(events);
            eventComboBox.setConverter(new StringConverter<Evenement>() {
                @Override
                public String toString(Evenement event) {
                    return event != null ? event.getTitre_evenement() : "";
                }
                @Override
                public Evenement fromString(String string) {
                    return null;
                }
            });
        } catch (SQLException e) {
            showAlert("Database Error", "Failed to load events: " + e.getMessage(), Alert.AlertType.ERROR);
            e.printStackTrace();
        }
    }

    @FXML
    private void handleSave() {
        try {
            User selectedUser = userComboBox.getSelectionModel().getSelectedItem();
            Evenement selectedEvent = eventComboBox.getSelectionModel().getSelectedItem();

            if(selectedUser == null || selectedEvent == null) {
                showAlert("Erreur", "Veuillez sélectionner un utilisateur et un événement", Alert.AlertType.ERROR);
                return;
            }

            Reservation reservation = new Reservation();
            reservation.setUser(selectedUser);
            reservation.setEvenement(selectedEvent);
            reservation.setDate_booking(Date.valueOf(datePicker.getValue()));
            reservation.setNbr_places(placesSpinner.getValue());
            reservation.setMoyen_payement_booking(paymentComboBox.getValue());
            reservation.setStatut_booking(statusComboBox.getValue());

            reservationService.ajouterReservation(reservation);

            showAlert("Succès", "Réservation enregistrée", Alert.AlertType.INFORMATION);
            closeWindow();
        } catch (Exception e) {
            handleError(e);
        }
    }

    private void handleError(Exception e) {
        System.err.println("Error: " + e.getMessage());
        e.printStackTrace();
        showAlert("Erreur", "Erreur technique: " + e.getMessage(), Alert.AlertType.ERROR);
    }
    @FXML
    private void handleCancel() {
        closeWindow();
    }

    private void closeWindow() {
        Stage stage = (Stage) userComboBox.getScene().getWindow();
        stage.close();
    }

    private void showAlert(String title, String message, Alert.AlertType type) {
        Alert alert = new Alert(type);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}