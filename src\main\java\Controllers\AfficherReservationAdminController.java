package Controllers;

import entity.Reservation;
import entity.Evenement;
import javafx.event.ActionEvent;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.geometry.Insets;
import javafx.scene.Node;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.scene.layout.AnchorPane;
import javafx.scene.layout.HBox;
import javafx.scene.layout.VBox;
import javafx.scene.paint.Color;
import javafx.scene.text.Font;
import javafx.scene.text.Text;
import javafx.stage.Modality;
import javafx.stage.Stage;
import service.ReservationService;

import java.io.IOException;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Optional;

public class AfficherReservationAdminController {
    @FXML private AnchorPane scrollPane;
    @FXML private Button btnAjout;
    @FXML private Button btnSearch;
    @FXML private VBox reservationsContainer;
    @FXML private TextField searchField;

    private final ReservationService reservationService = new ReservationService();
    private final SimpleDateFormat dateFormat = new SimpleDateFormat("dd MMMM yyyy");

    @FXML
    public void initialize() {
        loadReservations();
    }

    private void loadReservations() {
        try {
            List<Reservation> reservations = reservationService.recupererReservation();
            displayReservations(reservations);
        } catch (SQLException e) {
            showAlert("Database Error", "Failed to load reservations: " + e.getMessage(), Alert.AlertType.ERROR);
            e.printStackTrace();
        }
    }

    private void displayReservations(List<Reservation> reservations) {
        reservationsContainer.getChildren().clear();

        if (reservations.isEmpty()) {
            Label noReservationsLabel = new Label("Aucune réservation disponible");
            noReservationsLabel.setStyle("-fx-font-size: 16px; -fx-text-fill: #666;");
            reservationsContainer.getChildren().add(noReservationsLabel);
            return;
        }

        for (Reservation reservation : reservations) {
            VBox reservationCard = createReservationCard(reservation);
            reservationsContainer.getChildren().add(reservationCard);
        }
    }

    private VBox createReservationCard(Reservation reservation) {
        VBox card = new VBox(10);
        card.setStyle("-fx-background-color: #f8f9fa; -fx-border-color: #915f3a; -fx-border-radius: 5; -fx-padding: 15;");
        card.setPadding(new Insets(15));

        // User information
        Label userLabel = new Label();
        styleLabel(userLabel, "👤 Utilisateur: ",
                reservation.getUser().getNom_user() + " " + reservation.getUser().getPrenom_user());

        // Event information
        Label eventLabel = new Label();
        styleLabel(eventLabel, "🎪 Événement: ", reservation.getEvenement().getTitre_evenement());

        // Reservation details
        Label dateLabel = new Label();
        styleLabel(dateLabel, "📅 Date: ", dateFormat.format(reservation.getDate_booking()));

        Label placesLabel = new Label();
        styleLabel(placesLabel, "🎟️ Places: ", String.valueOf(reservation.getNbr_places()));

        Label paymentLabel = new Label();
        styleLabel(paymentLabel, "💳 Paiement: ", reservation.getMoyen_payement_booking());

        // Status badge with the requested styling
        HBox statusBadge = createStatusBadge(reservation.getStatut_booking());

        // Action buttons
        HBox buttonBox = new HBox(10);
        buttonBox.setPadding(new Insets(10, 0, 0, 0));

        Button detailsButton = createButton("👁️ Voir Détails", "#915f3a", e -> showEventDetails(reservation.getEvenement()));
        Button editButton = createButton("📝 Modifier", "#b97b4c", e -> openEditReservationForm(reservation));
        Button deleteButton = createButton("❌ Supprimer", "#b94e4e", e -> confirmAndDeleteReservation(reservation.getId()));

        buttonBox.getChildren().addAll(detailsButton, editButton, deleteButton);

        // Add all components to the card
        card.getChildren().addAll(
                userLabel,
                eventLabel,
                dateLabel,
                placesLabel,
                paymentLabel,
                statusBadge,  // Replaced the old statusLabel with the new badge
                buttonBox
        );

        return card;
    }

    private HBox createStatusBadge(String status) {
        HBox statusBox = new HBox(10);
        Label statusLabel = new Label("Statut:");
        statusLabel.setStyle("-fx-font-weight: bold; -fx-text-fill: #4a5568;");

        Label statusValue = new Label(status);
        statusValue.setStyle("-fx-font-weight: bold; -fx-padding: 3 10; -fx-background-radius: 10;");

        switch(status) {
            case "Confirmée":
                statusValue.setStyle(statusValue.getStyle() + "-fx-background-color: #48bb78; -fx-text-fill: white;");
                break;
            case "Annulée":
                statusValue.setStyle(statusValue.getStyle() + "-fx-background-color: #f56565; -fx-text-fill: white;");
                break;
            default: // "En attente" or any other status
                statusValue.setStyle(statusValue.getStyle() + "-fx-background-color: #ed8936; -fx-text-fill: white;");
        }

        statusBox.getChildren().addAll(statusLabel, statusValue);
        return statusBox;
    }
    private void showEventDetails(Evenement event) {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/DetailsEvenement.fxml"));
            Parent root = loader.load();

            // Get controller and set event data
            DetailsEvenementController controller = loader.getController();
            controller.setEventData(event);

            Stage stage = new Stage();
            stage.setTitle("Détails de l'Événement");
            stage.setScene(new Scene(root));
            stage.show();
        } catch (IOException e) {
            showAlert("Erreur", "Impossible d'ouvrir les détails: " + e.getMessage(), Alert.AlertType.ERROR);
        }
    }

    private void openEditReservationForm(Reservation reservation) {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/ModifierReservationAdmin.fxml"));
            Parent root = loader.load();

            ModifierReservationAdminController controller = loader.getController();
            controller.setReservation(reservation);

            Stage stage = new Stage();
            stage.setTitle("Modifier Réservation");
            stage.setScene(new Scene(root));
            stage.initModality(Modality.WINDOW_MODAL);

            stage.setOnHidden(e -> loadReservations());
            stage.show();
        } catch (IOException e) {
            showAlert("Erreur", "Échec du chargement du formulaire: " + e.getMessage(), Alert.AlertType.ERROR);
        }
    }

    @FXML
    private void handleAjouter(ActionEvent event) {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/AjouterReservationAdmin.fxml"));
            Parent root = loader.load();

            Stage stage = new Stage();
            stage.setTitle("Ajouter une Réservation");
            stage.setScene(new Scene(root));

            Stage currentStage = (Stage)((Node)event.getSource()).getScene().getWindow();
            stage.initOwner(currentStage);
            stage.initModality(Modality.WINDOW_MODAL);

            stage.setOnHidden(e -> loadReservations());
            stage.show();
        } catch (IOException e) {
            showAlert("Erreur", "Impossible d'ouvrir le formulaire: " + e.getMessage(), Alert.AlertType.ERROR);
        }
    }

    @FXML
    private void handleRechercher(ActionEvent event) {
        String searchTerm = searchField.getText().trim();
        if (!searchTerm.isEmpty()) {
            try {
                List<Reservation> results = reservationService.searchReservations(searchTerm);
                if (results.isEmpty()) {
                    showAlert("Information", "Aucun résultat trouvé", Alert.AlertType.INFORMATION);
                }
                displayReservations(results);
            } catch (SQLException e) {
                showAlert("Erreur", "Échec de la recherche: " + e.getMessage(), Alert.AlertType.ERROR);
            }
        } else {
            loadReservations(); // Show all if search is empty
        }
    }
    @FXML
    private void handleRefresh(ActionEvent event) {
        loadReservations();
    }

    // Helper methods remain the same
    private void styleLabel(Label label, String prefix, String value) {
        Text prefixText = new Text(prefix);
        prefixText.setStyle("-fx-fill: #915f3a; -fx-font-weight: bold;");
        Text valueText = new Text(value);
        valueText.setStyle("-fx-fill: black;");
        HBox container = new HBox(prefixText, valueText);
        container.setSpacing(0);
        label.setGraphic(container);
        label.setContentDisplay(ContentDisplay.GRAPHIC_ONLY);
    }

    private Button createButton(String text, String color, javafx.event.EventHandler<ActionEvent> handler) {
        Button button = new Button(text);
        button.setStyle(String.format(
                "-fx-background-color: %s; -fx-text-fill: white; -fx-font-weight: bold; -fx-padding: 5 10;",
                color
        ));
        button.setOnAction(handler);
        return button;
    }

    private void confirmAndDeleteReservation(int reservationId) {
        Alert alert = new Alert(Alert.AlertType.CONFIRMATION);
        alert.setTitle("Confirmer la suppression");
        alert.setHeaderText("Supprimer la réservation");
        alert.setContentText("Êtes-vous sûr de vouloir supprimer cette réservation?");

        Optional<ButtonType> result = alert.showAndWait();
        if (result.isPresent() && result.get() == ButtonType.OK) {
            try {
                reservationService.supprimerReservation(reservationId);
                loadReservations();
                showAlert("Succès", "Réservation supprimée avec succès", Alert.AlertType.INFORMATION);
            } catch (SQLException e) {
                showAlert("Erreur", "Échec de la suppression: " + e.getMessage(), Alert.AlertType.ERROR);
            }
        }
    }

    private void showAlert(String title, String message, Alert.AlertType type) {
        Alert alert = new Alert(type);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}