package Controllers;

import entity.Evenement;
import javafx.event.ActionEvent;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.scene.Node;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.stage.Stage;
import service.EvenementService;

import java.io.IOException;
import java.net.URL;
import java.sql.Date;
import java.sql.SQLException;
import java.time.LocalDate;
import java.util.ResourceBundle;

public class ModifierEvenementController implements Initializable {

    @FXML
    private DatePicker DateField;
    @FXML
    private TextArea ImageField;
    @FXML
    private TextArea PrixField;
    @FXML
    private TextArea TitreField;
    @FXML
    private Button btnReturn;
    @FXML
    private Button btnclear;
    @FXML
    private Button btnsave;
    @FXML
    private TextArea capFrield;
    @FXML
    private TextArea descField;
    @FXML
    private ChoiceBox<String> TypeField;

    private EvenementService evenementService;
    private Evenement event;

    // Event types (same as AjouterEvenementController)
    private final String[] eventTypes = {
            "Ateliers",
            "Conférences et tables rondes",
            "Événements artistiques et culturels",
            "Événements littéraires et éducatifs",
            "Événements de divertissement et de réseautage"
    };

    public void setEvent(Evenement event) {
        this.event = event;
        initializeFormData();
    }

    private void initializeFormData() {
        if (event != null) {
            TitreField.setText(event.getTitre_evenement());
            TypeField.setValue(event.getType_event());
            descField.setText(event.getDescription_event());
            ImageField.setText(event.getImage_event());
            capFrield.setText(String.valueOf(event.getCapacite_max()));
            DateField.setValue(event.getDate_event().toLocalDate());
            PrixField.setText(String.valueOf(event.getPrix_event()));
        }
    }

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        evenementService = new EvenementService();
        TypeField.getItems().addAll(eventTypes);
    }

    @FXML
    void Enregistrer(ActionEvent event) {
        if (this.event == null) {
            showAlert("Erreur", "Aucun événement à modifier", Alert.AlertType.ERROR);
            return;
        }

        // Validate required fields
        if (DateField.getValue() == null || TitreField.getText().isEmpty() || TypeField.getValue() == null ||
                descField.getText().isEmpty() || ImageField.getText().isEmpty() || capFrield.getText().isEmpty() ||
                PrixField.getText().isEmpty()) {

            showAlert("Erreur", "Veuillez remplir tous les champs", Alert.AlertType.ERROR);
            return;
        }

        try {
            // Validate numeric fields
            int capaciteMax = Integer.parseInt(capFrield.getText());
            double prix = Double.parseDouble(PrixField.getText());

            if (capaciteMax <= 0 || prix < 0) {
                showAlert("Erreur", "La capacité et le prix doivent être des valeurs valides", Alert.AlertType.ERROR);
                return;
            }

            // Validate date
            LocalDate localDate = DateField.getValue();
            if (localDate.isBefore(LocalDate.now())) {
                showAlert("Erreur", "La date ne peut pas être dans le passé", Alert.AlertType.ERROR);
                return;
            }

            // Convert LocalDate to SQL Date
            Date sqlDate = Date.valueOf(localDate);

            // Get values from form fields
            String titre = TitreField.getText();
            String image = ImageField.getText();
            String type = TypeField.getValue();
            String description = descField.getText();

            // Call service to update event
            evenementService.modifierEvenement(
                    this.event.getId(),
                    capaciteMax,
                    titre,
                    image,
                    type,
                    description,
                    sqlDate,
                    prix
            );

            showAlert("Succès", "Événement modifié avec succès", Alert.AlertType.INFORMATION);
            RetourListe(event);

        } catch (NumberFormatException e) {
            showAlert("Erreur", "Veuillez entrer des valeurs numériques valides pour la capacité et le prix", Alert.AlertType.ERROR);
        } catch (SQLException e) {
            showAlert("Erreur", "Erreur lors de la modification de l'événement: " + e.getMessage(), Alert.AlertType.ERROR);
        }
    }

    @FXML
    void RetourListe(ActionEvent event) {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/AfficherEvenement.fxml"));
            Parent root = loader.load();

            Stage stage = (Stage) ((Node) event.getSource()).getScene().getWindow();
            stage.setScene(new Scene(root));
            stage.setTitle("Liste des Événements");
            stage.show();
        } catch (IOException e) {
            showAlert("Erreur", "Impossible d'ouvrir la liste des événements", Alert.AlertType.ERROR);
        }
    }

    @FXML
    void clear(ActionEvent event) {
        TitreField.clear();
        TypeField.getSelectionModel().clearSelection();
        descField.clear();
        ImageField.clear();
        capFrield.clear();
        DateField.setValue(null);
        PrixField.clear();
    }

    private void showAlert(String title, String message, Alert.AlertType alertType) {
        Alert alert = new Alert(alertType);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}
