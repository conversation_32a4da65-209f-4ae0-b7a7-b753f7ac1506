//package test;
//import entity.Commande;
//import service.CommandeService;

//import entity.Produit;
//import service.ProduitService;
//import tools.MyDataBase;

/*public class Main2 {
    public static void main(String[] args) {

        ProduitService ps = new ProduitService();
        Produit p = new Produit(11,11, " AA", 11, " xxxx"," AA"," AA"," AA", " AA"," AA",10);

        System.out.println("****************************************AJOUTER*********************************");
        try {
            ps.ajouterProduit(p);
            System.out.println("produit ajouté!!!!!! ");
        }
        catch (Exception e) {
            System.out.println("Erreur ajouterProduit");

        }

        System.out.println("****************************************MODIFIER*********************************");
        try {
            ps.modifierProduit(168, "sqsqsqs", 12, 11, "yassmouna ",  12, " bobo", " AA", " AA", " AA", "JJ",10);
            System.out.println("produit modifié!!!!!! ");
        }
        catch (Exception e) {
            System.out.println("Erreur modifierProduit");
            e.printStackTrace(); // Affiche l'erreur complète

        }
        System.out.println("****************************************RECUPERER*********************************");

        try {
            System.out.println( ps.recupererProduits());
            System.out.println("produits recuperé!!!!!! ");
        }
        catch (Exception e) {
            System.out.println("Erreur recupererProduit");
            e.printStackTrace(); // Affiche l'erreur complète

        }
        System.out.println("****************************************SUPP*********************************");

        try {
            ps.supprimerProduit(172);
            System.out.println("produits supp!!!!!! ");
        }
        catch (Exception e) {
            System.out.println("Erreur suppProduit");
            e.printStackTrace(); // Affiche l'erreur complète

        }
/*
        System.out.println("/////////////////////////////////////////////////////////////////////////////" );

        CommandeService cs = new CommandeService();

        Commande c = new Commande(100, 2, 170, 10); // prix_total, quantité, produit_id, user_id

        System.out.println("****************************************AJOUTER COMMANDE*********************************");
        try {
            cs.ajouterCommande(c);
            System.out.println("Commande ajoutée avec succès !");
        } catch (Exception e) {
            System.out.println("Erreur ajouterCommande");
            e.printStackTrace();
        }

        System.out.println("****************************************MODIFIER COMMANDE*********************************");
        try {
            cs.modifierCommande(40, 5, 250, 170, 10); // id_commande, nouvelle quantite, prix total, produit_id, user_id
            System.out.println("Commande modifiée avec succès !");
        } catch (Exception e) {
            System.out.println("Erreur modifierCommande");
            e.printStackTrace();
        }

        System.out.println("****************************************RECUPERER COMMANDES*********************************");
        try {
            System.out.println(cs.recupererCommandes());
            System.out.println("Commandes récupérées avec succès !");
        } catch (Exception e) {
            System.out.println("Erreur recupererCommandes");
            e.printStackTrace();
        }

        System.out.println("****************************************SUPPRIMER COMMANDE*********************************");
        try {
            cs.supprimerCommande(41); // ID de la commande à supprimer
            System.out.println("Commande supprimée avec succès !");
        } catch (Exception e) {
            System.out.println("Erreur supprimerCommande");
            e.printStackTrace();
        }*/

