<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.*?>
<?import javafx.scene.image.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>
<?import javafx.geometry.*?>
<?import javafx.scene.shape.*?>

<AnchorPane prefHeight="600.0" prefWidth="900.0" style="-fx-background-color: linear-gradient(to bottom, #2a2a2a, #1e1e1e);" xmlns="http://javafx.com/javafx/21" xmlns:fx="http://javafx.com/fxml/1" fx:controller="Controllers.DetailsProduit">

    <!-- Barre de navigation -->
    <HBox alignment="CENTER_LEFT" prefHeight="70.0" prefWidth="900.0" style="-fx-background-color: #000000; -fx-padding: 0 30;">
        <ImageView fx:id="logoImageView" fitHeight="67.0" fitWidth="195.0" pickOnBounds="true" preserveRatio="true"/>
        <Pane HBox.hgrow="ALWAYS"/>

        <Button onAction="#navigateToAccueil" style="-fx-background-color: transparent; -fx-text-fill: white; -fx-font-size: 14px;" text="Accueil"/>
        <Button onAction="#navigateToRencontres" style="-fx-background-color: transparent; -fx-text-fill: white; -fx-font-size: 14px;" text="Rencontres"/>
        <Button onAction="#navigateToEvenements" style="-fx-background-color: transparent; -fx-text-fill: white; -fx-font-size: 14px;" text="Événements"/>
        <Button onAction="#navigateToForum" style="-fx-background-color: transparent; -fx-text-fill: white; -fx-font-size: 14px;" text="Forum"/>
        <Button onAction="#navigateToBoutique" style="-fx-background-color: #c49b63; -fx-text-fill: black; -fx-font-weight: bold; -fx-padding: 5 15; -fx-background-radius: 20; -fx-font-size: 14px;" text="Boutique"/>
        <Button fx:id="connexionButton" onAction="#navigateToConnexion" style="-fx-background-color: #c49b63; -fx-text-fill: black; -fx-font-weight: bold; -fx-padding: 5 15; -fx-background-radius: 20; -fx-font-size: 14px;" text="Connexion"/>
    </HBox>

    <!-- Contenu principal -->
    <VBox layoutY="70.0" prefHeight="530.0" prefWidth="900.0" style="-fx-background-color: transparent; -fx-padding: 20;" spacing="15">

        <!-- Titre et bouton retour -->
        <HBox alignment="CENTER_LEFT" spacing="20">
            <Button onAction="#navigateToBoutique" style="-fx-background-color: transparent; -fx-text-fill: #c49b63; -fx-font-size: 16px;" text="← Retour à la boutique"/>
            <Pane HBox.hgrow="ALWAYS"/>
            <Label style="-fx-text-fill: #c49b63; -fx-font-size: 24px; -fx-font-weight: bold;" text="Détails du produit"/>
        </HBox>

        <!-- Section principale -->
        <HBox spacing="30" style="-fx-padding: 10 20;">
            <!-- Galerie d'images -->
            <VBox spacing="15" prefWidth="350" alignment="TOP_CENTER">
                <ImageView fx:id="productImageView" fitHeight="250" fitWidth="250" style="-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.5), 10, 0.5, 0, 2); -fx-border-color: #c49b63; -fx-border-width: 2; -fx-border-radius: 5;"/>

                <HBox spacing="15" alignment="CENTER">
                    <ImageView fx:id="productImage2View" fitHeight="60" fitWidth="60" style="-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 5, 0.3, 0, 1); -fx-cursor: hand;"/>
                    <ImageView fx:id="productImage3View" fitHeight="60" fitWidth="60" style="-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 5, 0.3, 0, 1); -fx-cursor: hand;"/>
                    <ImageView fx:id="productImage4View" fitHeight="60" fitWidth="60" style="-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 5, 0.3, 0, 1); -fx-cursor: hand;"/>
                </HBox>
            </VBox>

            <!-- Détails du produit -->
            <VBox spacing="20" style="-fx-padding: 10 0;" prefWidth="500">
                <!-- Informations principales -->
                <GridPane hgap="15" vgap="15">
                    <columnConstraints>
                        <ColumnConstraints prefWidth="150"/>
                        <ColumnConstraints prefWidth="350"/>
                    </columnConstraints>

                    <Label text="Nom:" style="-fx-text-fill: #c49b63; -fx-font-weight: bold;" GridPane.rowIndex="0"/>
                    <TextField fx:id="productNameField" style="-fx-background-color: #282828; -fx-text-fill: white; -fx-border-color: #c49b63; -fx-border-width: 0 0 1 0;" GridPane.rowIndex="0" GridPane.columnIndex="1" editable="false"/>

                    <Label text="Prix (TND):" style="-fx-text-fill: #c49b63; -fx-font-weight: bold;" GridPane.rowIndex="1"/>
                    <TextField fx:id="productPriceField" style="-fx-background-color: #282828; -fx-text-fill: white; -fx-border-color: #c49b63; -fx-border-width: 0 0 1 0;" GridPane.rowIndex="1" GridPane.columnIndex="1" editable="false"/>
                </GridPane>

                <!-- Description -->
                <VBox spacing="5">
                    <Label style="-fx-text-fill: #c49b63; -fx-font-size: 18px; -fx-font-weight: bold;" text="Description"/>
                    <Separator style="-fx-background-color: #c49b63;"/>
                    <TextArea fx:id="productDescriptionField" style="-fx-control-inner-background: #282828; -fx-text-fill: white; -fx-border-color: #c49b63; -fx-border-width: 1; -fx-border-radius: 5;" wrapText="true" editable="false" prefHeight="150"/>
                </VBox>

                <!-- Boutons d'action -->
                <HBox spacing="15" alignment="CENTER_RIGHT" style="-fx-padding: 20 0 0 0;">
                    <Button text="Commander"
                            onAction="#navigateToAjouterCommande"
                            style="-fx-background-color: #c49b63; -fx-text-fill: black; -fx-font-weight: bold; -fx-padding: 8 20; -fx-background-radius: 20;"/>
                </HBox>
            </VBox>
        </HBox>
    </VBox>
</AnchorPane>