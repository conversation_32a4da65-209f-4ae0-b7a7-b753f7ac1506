package Controllers;

import entity.Produit;
import javafx.animation.KeyFrame;
import javafx.animation.ScaleTransition;
import javafx.animation.Timeline;
import javafx.application.Platform;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import javafx.scene.control.Alert;
import javafx.scene.control.Button;
import javafx.scene.control.Label;
import javafx.scene.control.ListView;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.scene.layout.StackPane;
import javafx.scene.layout.VBox;
import javafx.util.Duration;
import service.ProduitService;

import java.io.IOException;
import java.net.URL;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class AcceuilBackController {


    @FXML
    private StackPane contentPane;

    @FXML
    private void showCommantaire() {
        loadContent("/Admin/Dashboard.fxml");
    }

    @FXML
    private void showEvents() {
        loadContent("/AfficherEvenement.fxml");
    }

    @FXML
    private void showCommandes() {
        loadContent("/AffichageCommandeBack.fxml");
    }

    @FXML
    private void showProduits() {
        loadContent("/AffichageProduitBack.fxml");
    }

    @FXML
    private void showReservations() {
        loadContent("/AfficherReservationAdmin.fxml");
    }

    @FXML
    private void showPosts() {
        loadContent("/orumBackend.fxml");
    }

    @FXML
    private void logout() {
        try {
            Parent root = FXMLLoader.load(getClass().getResource("/Login.fxml"));
            contentPane.getScene().setRoot(root);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private void loadContent(String fxmlPath) {
        try {
            // Make sure the path starts with "/" if it's in the resources folder
            if (!fxmlPath.startsWith("/")) {
                fxmlPath = "/" + fxmlPath;
            }

            System.out.println("Loading FXML from: " + fxmlPath); // Debug log

            // Check if the resource exists
            URL resourceUrl = getClass().getResource(fxmlPath);
            if (resourceUrl == null) {
                throw new RuntimeException("FXML file not found: " + fxmlPath);
            }

            Parent content = FXMLLoader.load(resourceUrl);
            contentPane.getChildren().setAll(content);
        } catch (IOException e) {
            System.err.println("Error loading content: " + fxmlPath);
            e.printStackTrace();
            // Show an error message to the user
            Alert alert = new Alert(Alert.AlertType.ERROR);
            alert.setTitle("Loading Error");
            alert.setHeaderText("Could not load the requested view");
            alert.setContentText("Error loading: " + fxmlPath + "\n" + e.getMessage());
            alert.showAndWait();
        }
    }
    @FXML
    private void showProductStats() {
        loadContent("/StatBack.fxml"); // Corrected path
    }




    @FXML
    private Button notificationButton;
    @FXML private VBox notificationPanel;
    @FXML private ListView<String> notificationList;
    @FXML private Label notificationBadge;

    private ProduitService produitService = new ProduitService();
    private Timeline notificationUpdater;

    @FXML private Label nomProduitLabel;
    @FXML private Label typeProduitLabel;
    @FXML private Label stockLabel;
    @FXML private Label prixLabel;
    @FXML private Label descriptionLabel;
    @FXML private ImageView imageProduitView;

    // Modifiez votre méthode initialize()
    @FXML
    public void initialize() {
        setupNotifications();

        // Ajoutez cet écouteur de clic
        notificationList.setOnMouseClicked(event -> {
            if (event.getClickCount() == 1) { // Simple clic

            }
        });
    }

    private void setupNotifications() {
        // Configuration initiale
        notificationPanel.setVisible(false);

        // Actualisation périodique (toutes les 30 secondes)
        notificationUpdater = new Timeline(
                new KeyFrame(Duration.seconds(2),
                        e -> updateNotificationCount())
        );
        notificationUpdater.setCycleCount(Timeline.INDEFINITE);
        notificationUpdater.play();

        // Premier chargement
        updateNotificationCount();
    }

    @FXML
    private void toggleNotifications() {
        boolean visible = !notificationPanel.isVisible();
        notificationPanel.setVisible(visible);

        if (visible) {
            loadPendingProducts();
            notificationButton.setText("🔕"); // Icône "silencieux" quand ouvert
        } else {
            notificationButton.setText("🔔"); // Icône normale quand fermé
        }
    }

    private void updateNotificationCount() {
        Platform.runLater(() -> {
            try {
                int count = produitService.getPendingProductsCount();
                notificationBadge.setText(String.valueOf(count));
                notificationBadge.setVisible(count > 0);

                // Animation du badge
                if (count > 0) {
                    animateBellIcon();
                }
            } catch (SQLException e) {
                e.printStackTrace();
            }
        });
    }

    private void animateBellIcon() {
        if (notificationButton != null) {  // Ajout d'une vérification de nullité
            ScaleTransition st = new ScaleTransition(Duration.millis(200), notificationButton);
            st.setFromX(1); st.setFromY(1);
            st.setToX(1.2); st.setToY(1.2);
            st.setAutoReverse(true);
            st.setCycleCount(2);
            st.play();
        }
    }

    private void loadPendingProducts() {
        try {
            List<Produit> produits = produitService.getPendingProducts();
            ObservableList<String> items = FXCollections.observableArrayList();

            // Style inchangé
            notificationList.setStyle("-fx-control-inner-background: #c49b63; " +
                    "-fx-text-fill: #1a1a1a; " +
                    "-fx-font-size: 13px; " +
                    "-fx-padding: 5;");

            // Stocker les produits pour référence ultérieure
            Map<Integer, Produit> produitsMap = new HashMap<>();
            int index = 0;

            for (Produit p : produits) {
                String entry = String.format("🛒 %s (%s)\n   👤 %s %s\n   📦 Stock: %d",
                        p.getNom_produit(),
                        p.getType_produit(),
                        p.getUser().getNom_user(),
                        p.getUser().getPrenom_user(),
                        p.getStock_produit());
                items.add(entry);
                produitsMap.put(index, p);
                index++;

                // Ajout d'un séparateur entre les notifications
                if (produits.indexOf(p) < produits.size() - 1) {
                    items.add("------------------------------");
                    index++;
                }
            }

            notificationList.setItems(items);

            // Gestion du clic sur une notification
            notificationList.getSelectionModel().selectedItemProperty().addListener((obs, oldVal, newVal) -> {
                if (newVal != null && !newVal.equals("------------------------------")) {
                    int selectedIndex = notificationList.getSelectionModel().getSelectedIndex();
                    Produit selectedProduit = produitsMap.get(selectedIndex);
                    if (selectedProduit != null) {
                        afficherProduit(selectedProduit);
                    }
                }
            });

        } catch (SQLException e) {
            notificationList.setStyle("-fx-control-inner-background: #c49b63; " +
                    "-fx-text-fill: #1a1a1a;");
            notificationList.setItems(FXCollections.observableArrayList("❌ Erreur de chargement"));
            e.printStackTrace();
        }
    }

    private void afficherProduit(Produit produit) {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/AffichageProduitBack.fxml"));
            Parent root = loader.load();

            // Passer l'ID du produit au contrôleur de détail
            AffichageProduitBack controller = loader.getController();
            controller.setSelectedProductId(produit.getId_produit());
            controller.initialize(null, null); // Force l'initialisation

            // Remplacer le contenu actuel
            contentPane.getChildren().setAll(root);

        } catch (IOException e) {
            e.printStackTrace();
            showAlert("Erreur", "Impossible d'afficher le produit");
        }
    }
    public void initData(Produit produit) {
        try {
            // Texte de base
            nomProduitLabel.setText(produit.getNom_produit());
            typeProduitLabel.setText("Type: " + produit.getType_produit());
            stockLabel.setText("Stock: " + produit.getStock_produit());
            prixLabel.setText("Prix: " + produit.getPrix_produit() + " €");
            descriptionLabel.setText("Description: " + produit.getDescription_produit());

            // Gestion des images (exemple pour la première image)
            if (produit.getImage_produit() != null && !produit.getImage_produit().isEmpty()) {
                Image image = new Image("file:" + produit.getImage_produit());
                imageProduitView.setImage(image);
            }

            // Appliquez le style café culturel
            nomProduitLabel.setStyle("-fx-text-fill: #c49b63; -fx-font-size: 18px; -fx-font-weight: bold;");
            typeProduitLabel.setStyle("-fx-text-fill: #1a1a1a;");
            stockLabel.setStyle("-fx-text-fill: #1a1a1a;");

            // Supprimer cette ligne qui cause l'erreur
            // parentContainer.setStyle("-fx-background-color: #2a2a2a; -fx-padding: 15;");

        } catch (Exception e) {
            System.err.println("Erreur lors de l'initialisation des données du produit");
            e.printStackTrace();
        }
    }
    // Ajoutez cette méthode pour gérer le clic

    @FXML
    private void showAllPending() {
        // Votre logique existante pour afficher tous les produits
        loadContent("/AffichageProduitBack.fxml");
        notificationPanel.setVisible(false);
        notificationButton.setText("🔔");
    }

    private void showAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.ERROR);
        alert.setTitle(title);
        alert.setContentText(message);
        alert.showAndWait();
    }

}