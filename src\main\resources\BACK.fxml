<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>

<TabPane xmlns="http://javafx.com/javafx/21.0.2"
         xmlns:fx="http://javafx.com/fxml/1"
         fx:controller="Controllers.BACK"
         fx:id="tabPane">  <!-- Ajout du fx:id ici -->

    <tabs>
        <Tab text="Produits">
            <fx:include fx:id="produitsTab" source="AffichageProduitBack.fxml" />
        </Tab>

        <Tab text="Commandes">
            <fx:include fx:id="commandesTab" source="AffichageCommandeBack.fxml" />
        </Tab>
    </tabs>
</TabPane>