<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.*?>
<?import javafx.scene.control.*?>
<?import javafx.scene.image.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<VBox xmlns="http://javafx.com/javafx/17" xmlns:fx="http://javafx.com/fxml/1"
      fx:controller="Controllers.DetailsEvenementController"
      spacing="15" style="-fx-padding: 20; -fx-background-color: #f5f5f5;">

    <Label fx:id="titleLabel" text="Détails de l'Événement"
           style="-fx-font-size: 20; -fx-font-weight: bold; -fx-text-fill: #915f3a;"/>

    <GridPane hgap="10" vgap="15"
              style="-fx-background-color: white; -fx-background-radius: 8; -fx-padding: 20; -fx-border-color: #915f3a; -fx-border-radius: 8; -fx-border-width: 1.5;">
        <padding><Insets top="15" right="15" bottom="15" left="15"/></padding>

        <!-- Image and Basic Info -->
        <HBox spacing="30" GridPane.columnSpan="2">
            <children>
                <ImageView fx:id="eventImageView" fitHeight="250" fitWidth="350"
                           preserveRatio="true"
                           style="-fx-border-radius: 10; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 2, 2);"/>

                <VBox spacing="10">
                    <children>
                        <HBox spacing="5" alignment="CENTER_LEFT">
                            <Label text="Type:" style="-fx-font-weight: bold; -fx-text-fill: #5d4037;"/>
                            <Label fx:id="typeLabel" style="-fx-text-fill: #5d4037;"/>
                        </HBox>
                        <HBox spacing="5" alignment="CENTER_LEFT">
                            <Label text="Date:" style="-fx-font-weight: bold; -fx-text-fill: #5d4037;"/>
                            <Label fx:id="dateLabel" style="-fx-text-fill: #5d4037;"/>
                        </HBox>
                        <HBox spacing="5" alignment="CENTER_LEFT">
                            <Label text="Prix:" style="-fx-font-weight: bold; -fx-text-fill: #5d4037;"/>
                            <Label fx:id="priceLabel" style="-fx-text-fill: #5d4037;"/>
                        </HBox>
                        <HBox spacing="5" alignment="CENTER_LEFT">
                            <Label text="Capacité:" style="-fx-font-weight: bold; -fx-text-fill: #5d4037;"/>
                            <Label fx:id="capacityLabel" style="-fx-text-fill: #5d4037;"/>
                        </HBox>
                    </children>
                </VBox>
            </children>
        </HBox>

        <!-- Description -->
        <Label text="Description:" GridPane.rowIndex="1" GridPane.columnIndex="0"
               style="-fx-font-weight: bold; -fx-text-fill: #5d4037;"/>
        <ScrollPane GridPane.rowIndex="1" GridPane.columnIndex="1"
                    fitToWidth="true"
                    prefHeight="100"
                    style="-fx-background-color: transparent;">
            <TextArea fx:id="descriptionArea"
                      editable="false"
                      wrapText="true"
                      style="-fx-background-color: white; -fx-border-color: #cccccc; -fx-border-radius: 4; -fx-text-fill: #5d4037; -fx-font-size: 14px;"/>
        </ScrollPane>

        <!-- Reservation Info in a small framed box -->
        <VBox GridPane.rowIndex="2" GridPane.columnSpan="2"
              spacing="10"
              style="-fx-background-color: #f9f9f9; -fx-border-color: #d7ccc8; -fx-border-radius: 5; -fx-border-width: 1; -fx-padding: 10;">
            <Label text="Informations de Réservation" style="-fx-font-weight: bold; -fx-text-fill: #5d4037;"/>

            <GridPane hgap="10" vgap="5">
                <columnConstraints>
                    <ColumnConstraints hgrow="NEVER" minWidth="150"/>
                    <ColumnConstraints hgrow="ALWAYS"/>
                </columnConstraints>
                <children>
                    <Label text="Places Réservées:" style="-fx-font-weight: bold; -fx-text-fill: #5d4037;"/>
                    <Label fx:id="reservedPlacesLabel" GridPane.columnIndex="1" style="-fx-text-fill: #5d4037;"/>

                    <Label text="Places Disponibles:" GridPane.rowIndex="1" style="-fx-font-weight: bold; -fx-text-fill: #5d4037;"/>
                    <Label fx:id="availablePlacesLabel" GridPane.rowIndex="1" GridPane.columnIndex="1" style="-fx-text-fill: #5d4037;"/>
                </children>
            </GridPane>
        </VBox>
    </GridPane>

    <HBox spacing="15" alignment="CENTER_RIGHT">
        <Button text="Fermer" onAction="#handleClose"
                style="-fx-background-color: #c98a56; -fx-text-fill: white; -fx-font-weight: bold; -fx-padding: 8 20; -fx-background-radius: 4;"/>
    </HBox>
</VBox>