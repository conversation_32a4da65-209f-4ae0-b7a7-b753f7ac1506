package Controllers;

import entity.User;
import entity.UserSession;
import javafx.event.ActionEvent;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.scene.Node;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.control.Label;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.scene.layout.VBox;
import javafx.stage.Stage;

import java.io.File;
import java.io.IOException;

public class ProfilUserController {
    @FXML
    private VBox postsContainer;


    @FXML
    private Label lblName;
    @FXML
    private Label lblLastname;
    @FXML
    private Label lblEmail;
    @FXML
    private ImageView imgPhoto;

    private User user;

    // This method sets the user object
    public void setUser(User user) {
        this.user = user;
        if (this.user != null) {
            // Set user information in the profile view
            lblName.setText(user.getNom_user());  // Assuming 'getNom_user()' gives first name
            lblLastname.setText(user.getPrenom_user());  // Assuming 'getPrenom_user()' gives last name
            lblEmail.setText(user.getEmail_user());  // Assuming 'getEmail_user()' gives email

            if (user.getPhoto_user() != null && !user.getPhoto_user().isEmpty()) {
                File imgFile = new File(user.getPhoto_user());
                if (imgFile.exists()) {
                    Image image = new Image(imgFile.toURI().toString());
                    imgPhoto.setImage(image);
                } else {
                    System.out.println("⚠️ Image file not found: " + imgFile.getAbsolutePath());
                }
            }

        }
    }

    @FXML
    private void handleLogout(ActionEvent event) {
        // Clear user session
        UserSession.getInstance().cleanUserSession();

        // Redirect to login page
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/AcceuilFront.fxml"));
            Parent root = loader.load();
            Stage stage = (Stage) ((Node) event.getSource()).getScene().getWindow();
            stage.setScene(new Scene(root));
            stage.setTitle("Login");
            stage.show();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    @FXML
    private void goToUpdatePage(ActionEvent event) {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/UpdateUser.fxml"));
            VBox updateForm = loader.load(); // IMPORTANT: match the root type in FXML

            UpdateUserController updateController = loader.getController();
            if (updateController != null) {
                updateController.setUser(user);
            } else {
                System.out.println("⚠️ Controller is NULL! Check fx:controller in UpdateUser.fxml.");
            }

            postsContainer.getChildren().clear();
            postsContainer.getChildren().add(updateForm);

        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @FXML
    private void toHome(ActionEvent event) {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/AcceuilFront.fxml"));
            Parent root = loader.load();

            // Récupérer l'utilisateur depuis la session
            User currentUser = UserSession.getInstance().getUser();

            // Passer l'utilisateur au contrôleur d'accueil
            AcceuilFrontController controller = loader.getController();
            if (controller != null && currentUser != null) {
                controller.setUser(currentUser);
            }

            Stage stage = (Stage) ((Node) event.getSource()).getScene().getWindow();
            stage.setScene(new Scene(root));
            stage.setTitle("Accueil");
            stage.show();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


}
