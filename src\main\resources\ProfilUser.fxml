<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.ScrollPane?>
<?import javafx.scene.image.Image?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.Region?>
<?import javafx.scene.layout.StackPane?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.text.Font?>

<StackPane style="-fx-background-color: linear-gradient(to bottom, #2a2a2a, #1e1e1e);" xmlns="http://javafx.com/javafx/23.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="Controllers.ProfilUserController">
    <VBox alignment="CENTER" maxHeight="700" maxWidth="1200" spacing="20" style="-fx-background-color: #282828; -fx-padding: 40; -fx-background-radius: 10; -fx-border-color: #c49b63; -fx-border-width: 1; -fx-border-radius: 10; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 15, 0, 0, 5);">

        <!-- Main Content Area -->
        <HBox alignment="TOP_CENTER" spacing="30">
            <!-- Left Side: Profile Info -->
            <VBox alignment="CENTER" maxWidth="350" minWidth="300" spacing="20" style="-fx-background-color: #1e1e1e; -fx-padding: 30; -fx-background-radius: 10; -fx-border-color: #c49b63; -fx-border-width: 1;">
                <ImageView fx:id="imgPhoto" fitHeight="150" fitWidth="150" preserveRatio="true" style="-fx-effect: dropshadow(gaussian, rgba(196,155,99,0.3), 10, 0, 0, 4);">
                    <Image url="@/images/users-icon.png" />
                </ImageView>

                <VBox alignment="CENTER" spacing="5">
                    <Label fx:id="lblName" style="-fx-font-size: 28px; -fx-font-weight: bold; -fx-text-fill: #c49b63;" text="User Name">
                        <font>
                            <Font name="Segoe UI Light" size="28.0" />
                        </font>
                    </Label>
                    <Label fx:id="lblLastname" style="-fx-font-size: 18px; -fx-text-fill: #95a5a6;" text="Last Name">
                        <font>
                            <Font name="Segoe UI Light" size="18.0" />
                        </font>
                    </Label>
                    <Label fx:id="lblEmail" style="-fx-font-size: 14px; -fx-text-fill: #95a5a6;" text="<EMAIL>">
                        <font>
                            <Font name="Segoe UI Light" size="14.0" />
                        </font>
                    </Label>
                </VBox>

                <Region prefHeight="20" />

                <!-- Action Buttons -->
                <VBox alignment="CENTER" spacing="10">
                    <Button maxWidth="200" onAction="#goToUpdatePage" style="-fx-background-color: #c49b63; -fx-text-fill: #000000; -fx-font-weight: bold; -fx-background-radius: 5; -fx-padding: 12 20; -fx-cursor: hand;" text="Update Profile">
                        <font>
                            <Font name="Segoe UI Bold" size="14.0" />
                        </font>
                        <graphic>
                            <ImageView fitHeight="25.0" fitWidth="24.0" pickOnBounds="true" preserveRatio="true">
                                <image>
                                    <Image url="@images/edit.png" />
                                </image>
                            </ImageView>
                        </graphic>
                    </Button>

                    <Button maxWidth="200" onAction="#toHome" style="-fx-background-color: #282828; -fx-text-fill: #c49b63; -fx-font-weight: bold; -fx-background-radius: 5; -fx-padding: 12 20; -fx-cursor: hand; -fx-border-color: #c49b63; -fx-border-width: 1;" text="Home">
                        <font>
                            <Font name="Segoe UI Bold" size="14.0" />
                        </font>
                        <graphic>
                            <ImageView fx:id="toHome" fitHeight="25.0" fitWidth="24.0" pickOnBounds="true" preserveRatio="true">
                                <image>
                                    <Image url="@images/maison.png" />
                                </image>
                            </ImageView>
                        </graphic>
                    </Button>

                    <Button maxWidth="200" onAction="#handleLogout" style="-fx-background-color: #e74c3c; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 5; -fx-padding: 12 20; -fx-cursor: hand;" text="Logout">
                        <font>
                            <Font name="Segoe UI Bold" size="14.0" />
                        </font>
                        <graphic>
                            <ImageView fitHeight="27.0" fitWidth="22.0" pickOnBounds="true" preserveRatio="true">
                                <image>
                                    <Image url="@/images/deconnexion.png" />
                                </image>
                            </ImageView>
                        </graphic>
                    </Button>
                </VBox>
            </VBox>

            <!-- Right Side: Posts Section -->
            <VBox alignment="TOP_CENTER" maxWidth="800" minWidth="500" spacing="15" HBox.hgrow="ALWAYS">
                <Label style="-fx-font-size: 24px; -fx-font-weight: bold; -fx-text-fill: #c49b63;" text="Your Posts">
                    <font>
                        <Font name="Segoe UI Light" size="24.0" />
                    </font>
                </Label>

                <ScrollPane fitToWidth="true" style="-fx-background: transparent; -fx-background-color: transparent;" VBox.vgrow="ALWAYS">
                    <VBox fx:id="postsContainer" alignment="CENTER" spacing="15" style="-fx-padding: 20;">
                        <!-- Example Post (you can remove this and populate dynamically) -->
                        <VBox style="-fx-background-color: #1e1e1e; -fx-padding: 20; -fx-background-radius: 10; -fx-border-color: #c49b63; -fx-border-width: 1; -fx-effect: dropshadow(gaussian, rgba(196,155,99,0.1), 10, 0, 0, 4);">
                            <Label style="-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #c49b63;" text="Sample Post Title">
                                <font>
                                    <Font name="Segoe UI Light" size="18.0" />
                                </font>
                            </Label>
                            <Label style="-fx-font-size: 14px; -fx-text-fill: #95a5a6; -fx-wrap-text: true;" text="This is a sample post content. Your actual posts will appear here.">
                                <font>
                                    <Font name="Segoe UI Light" size="14.0" />
                                </font>
                            </Label>
                        </VBox>
                    </VBox>
                </ScrollPane>
            </VBox>
        </HBox>
    </VBox>
</StackPane>