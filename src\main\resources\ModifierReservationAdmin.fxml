<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>
<?import javafx.geometry.*?>
<?import javafx.collections.FXCollections?>

<?import java.lang.String?>
<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1"
      fx:controller="Controllers.ModifierReservationAdminController"
      spacing="15" style="-fx-padding: 20; -fx-background-color: #f5f5f5;">

    <Label text="📝 Modifier Réservation" style="-fx-font-size: 20; -fx-font-weight: bold; -fx-text-fill: #915f3a;"/>

    <GridPane hgap="10" vgap="15" style="-fx-background-color: white; -fx-background-radius: 8; -fx-padding: 20; -fx-border-color: #915f3a; -fx-border-radius: 8; -fx-border-width: 1.5;">
        <padding><Insets top="15" right="15" bottom="15" left="15"/></padding>

        <!-- User Display (non-editable) -->
        <Label text="👤 Utilisateur:" GridPane.rowIndex="0" GridPane.columnIndex="0" style="-fx-font-weight: bold;"/>
        <Label fx:id="userLabel" GridPane.rowIndex="0" GridPane.columnIndex="1" style="-fx-text-fill: #5d4037;"/>

        <!-- Event Selection -->
        <Label text="🎭 Événement:" GridPane.rowIndex="1" GridPane.columnIndex="0" style="-fx-font-weight: bold;"/>
        <ComboBox fx:id="eventComboBox" GridPane.rowIndex="1" GridPane.columnIndex="1" prefWidth="250"/>

        <!-- Booking Date -->
        <Label text="📅 Date de réservation:" GridPane.rowIndex="2" GridPane.columnIndex="0" style="-fx-font-weight: bold;"/>
        <DatePicker fx:id="datePicker" GridPane.rowIndex="2" GridPane.columnIndex="1"/>

        <!-- Number of Places -->
        <Label text="🧮 Nombre de places:" GridPane.rowIndex="3" GridPane.columnIndex="0" style="-fx-font-weight: bold;"/>
        <Spinner fx:id="placesSpinner" GridPane.rowIndex="3" GridPane.columnIndex="1"
                 style="-fx-background-color: white; -fx-border-color: #cccccc; -fx-border-radius: 4;"
                 editable="true">
            <valueFactory>
                <SpinnerValueFactory.IntegerSpinnerValueFactory min="1" max="20" initialValue="1"/>
            </valueFactory>
        </Spinner>

        <!-- Payment Method -->
        <Label text="💳 Moyen de paiement:" GridPane.rowIndex="4" GridPane.columnIndex="0" style="-fx-font-weight: bold;"/>
        <ComboBox fx:id="paymentComboBox" GridPane.rowIndex="4" GridPane.columnIndex="1">
            <items>
                <FXCollections fx:factory="observableArrayList">
                    <String fx:value="Espèces"/>
                    <String fx:value="Carte bancaire"/>
                    <String fx:value="Virement"/>
                    <String fx:value="Chèque"/>
                </FXCollections>
            </items>
        </ComboBox>

        <!-- Status -->
        <Label text="🔄 Statut:" GridPane.rowIndex="5" GridPane.columnIndex="0" style="-fx-font-weight: bold;"/>
        <ComboBox fx:id="statusComboBox" GridPane.rowIndex="5" GridPane.columnIndex="1">
            <items>
                <FXCollections fx:factory="observableArrayList">
                    <String fx:value="Confirmée"/>
                    <String fx:value="En attente"/>
                    <String fx:value="Annulée"/>
                </FXCollections>
            </items>
        </ComboBox>
    </GridPane>

    <HBox spacing="15" alignment="CENTER_RIGHT">
        <Button text="❌ Annuler" style="-fx-background-color: #c98a56; -fx-text-fill: white; -fx-font-weight: bold; -fx-padding: 8 20; -fx-background-radius: 4;" onAction="#handleCancel"/>
        <Button text="💾 Enregistrer" style="-fx-background-color: #cc5c5c; -fx-text-fill: white; -fx-font-weight: bold; -fx-padding: 8 20; -fx-background-radius: 4;" onAction="#handleUpdate"/>
    </HBox>
</VBox>