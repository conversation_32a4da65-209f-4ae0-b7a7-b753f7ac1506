<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>

<DialogPane xmlns="http://javafx.com/javafx/21.0.2" xmlns:fx="http://javafx.com/fxml/1"
            fx:controller="controllers.EditMessageController" stylesheets="@styles.css">
    <headerText>
        <Label text="Edit Message" style="-fx-font-size: 18; -fx-font-weight: bold;"/>
    </headerText>

    <content>
        <TextArea fx:id="messageContentArea" wrapText="true" style="-fx-pref-height: 100;"/>
    </content>

    <buttonTypes>
        <ButtonType text="Cancel" buttonData="CANCEL_CLOSE"/>
        <ButtonType text="Save" buttonData="OK_DONE"/>
    </buttonTypes>
</DialogPane>