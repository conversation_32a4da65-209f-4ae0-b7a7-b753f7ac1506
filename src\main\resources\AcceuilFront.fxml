<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.image.Image?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.Pane?>
<?import javafx.scene.layout.StackPane?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.text.Font?>

<AnchorPane prefHeight="600.0" prefWidth="900.0" style="-fx-background-color: linear-gradient(to bottom, #2a2a2a, #1e1e1e);" xmlns="http://javafx.com/javafx/23.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="Controllers.AcceuilFrontController">
    <children>
        <!-- Top Navigation Bar -->
        <HBox alignment="CENTER_LEFT" prefHeight="70.0" prefWidth="900.0" style="-fx-background-color: #000000; -fx-padding: 0 30 0 30;">
            <children>
                <ImageView fx:id="logoImageView" fitHeight="67.0" fitWidth="195.0" pickOnBounds="true" preserveRatio="true">
                    <image>
                        <Image url="@logofront.png" />
                    </image>
                </ImageView>
                <Pane HBox.hgrow="ALWAYS" />

                <Button onAction="#navigateToAccueil" style="-fx-background-color: transparent; -fx-text-fill: #c49b63; -fx-font-size: 14px; -fx-cursor: hand; -fx-font-weight: bold;" text="Accueil">
                    <font>
                        <Font name="Segoe UI" size="14.0" />
                    </font>
                </Button>
                <Button onAction="#navigateToRencontres" style="-fx-background-color: transparent; -fx-text-fill: #ffffff; -fx-font-size: 14px; -fx-cursor: hand;" text="Rencontres">
                    <font>
                        <Font name="Segoe UI" size="14.0" />
                    </font>
                </Button>
                <Button onAction="#navigateToEvenements" style="-fx-background-color: transparent; -fx-text-fill: #ffffff; -fx-font-size: 14px; -fx-cursor: hand;" text="Événements">
                    <font>
                        <Font name="Segoe UI" size="14.0" />
                    </font>
                </Button>
                <Button onAction="#navigateToForum" style="-fx-background-color: transparent; -fx-text-fill: #ffffff; -fx-font-size: 14px; -fx-cursor: hand;" text="Forum">
                    <font>
                        <Font name="Segoe UI" size="14.0" />
                    </font>
                </Button>
                <Button onAction="#navigateToBoutique" style="-fx-background-color: transparent; -fx-text-fill: #ffffff; -fx-font-size: 14px; -fx-cursor: hand;" text="Boutique">
                    <font>
                        <Font name="Segoe UI" size="14.0" />
                    </font>
                </Button>
                <Button fx:id="connexionButton" onAction="#navigateToConnexion"
                        style="-fx-background-color: #c49b63; -fx-text-fill: #000000; -fx-font-size: 14px; -fx-cursor: hand; -fx-padding: 5 15; -fx-background-radius: 20;"
                        text="Connexion">
                    <font>
                        <Font name="Segoe UI Bold" size="14.0" />
                    </font>
                </Button>

            </children>
        </HBox>

        <!-- Main Content -->
        <VBox layoutY="70.0" prefHeight="530.0" prefWidth="900.0" style="-fx-background-color: transparent;">
            <children>
                <!-- Compact Hero Section -->
                <StackPane prefHeight="200.0" prefWidth="900.0">
                    <children>
                        <ImageView fitHeight="200.0" fitWidth="900.0" preserveRatio="false">
                            <image>
                                <!-- <Image url="@../images/logo.png" />-->
                            </image>
                        </ImageView>
                        <VBox alignment="CENTER" spacing="10" style="-fx-background-color: rgba(0, 0, 0, 0.5);">
                            <children>
                                <Label style="-fx-text-fill: #c49b63; -fx-font-size: 32px; -fx-font-weight: bold;" text="Rendez-Vous Culture">
                                    <font>
                                        <Font name="Segoe UI Light" size="32.0" />
                                    </font>
                                </Label>
                                <Label style="-fx-text-fill: white; -fx-font-size: 16px;" text="Savourez l’instant, Partagez la culture">
                                    <font>
                                        <Font name="Segoe UI Light" size="16.0" />
                                    </font>
                                </Label>
                            </children>
                        </VBox>
                    </children>
                </StackPane>

                <!-- Features Section - Now Horizontal -->
                <VBox alignment="TOP_CENTER" spacing="20" style="-fx-padding: 20;">
                    <Label style="-fx-text-fill: #c49b63; -fx-font-size: 24px; -fx-font-weight: bold;" text="Notre Concept">
                        <font>
                            <Font name="Segoe UI Light" size="24.0" />
                        </font>
                    </Label>

                    <HBox alignment="TOP_CENTER" spacing="20" style="-fx-padding: 10;">
                        <!-- Feature 1 -->
                        <VBox alignment="TOP_LEFT" prefHeight="180.0" prefWidth="266.0" spacing="5" style="-fx-background-color: #282828; -fx-border-color: #c49b63; -fx-border-width: 1; -fx-padding: 15; -fx-pref-width: 280;">
                            <Label alignment="CENTER" prefHeight="30.0" prefWidth="229.0" style="-fx-text-fill: #c49b63; -fx-font-size: 18px; -fx-font-weight: bold;" text="Café d'Exception" />
                            <Label prefHeight="137.0" prefWidth="234.0" style="-fx-text-fill: #e0e0e0; -fx-font-size: 14px; -fx-wrap-text: true;" text="Nous sélectionnons les meilleurs grains de café pour une expérience gustative unique." />
                        </VBox>

                        <!-- Feature 2 -->
                        <VBox alignment="TOP_LEFT" prefHeight="192.0" prefWidth="266.0" spacing="5" style="-fx-background-color: #282828; -fx-border-color: #c49b63; -fx-border-width: 1; -fx-padding: 15; -fx-pref-width: 280;">
                            <Label alignment="CENTER" prefHeight="27.0" prefWidth="226.0" style="-fx-text-fill: #c49b63; -fx-font-size: 18px; -fx-font-weight: bold;" text="Espace Culturel" />
                            <Label prefHeight="123.0" prefWidth="234.0" style="-fx-text-fill: #e0e0e0; -fx-font-size: 14px; -fx-wrap-text: true;" text="Un lieu de rencontre pour les amateurs d'art, de littérature et d'échanges intellectuels." />
                        </VBox>

                        <!-- Feature 3 -->
                        <VBox alignment="TOP_LEFT" spacing="5" style="-fx-background-color: #282828; -fx-border-color: #c49b63; -fx-border-width: 1; -fx-padding: 15; -fx-pref-width: 280;">
                            <Label alignment="CENTER" prefHeight="27.0" prefWidth="239.0" style="-fx-text-fill: #c49b63; -fx-font-size: 18px; -fx-font-weight: bold;" text="Ambiance Unique" />
                            <Label prefHeight="126.0" prefWidth="235.0" style="-fx-text-fill: #e0e0e0; -fx-font-size: 14px; -fx-wrap-text: true;" text="Découvrez une atmosphère chaleureuse qui allie modernité et tradition caféière." />
                        </VBox>
                    </HBox>
                </VBox>
            </children>
        </VBox>
    </children>
</AnchorPane>
