package service;

import entity.Commentaire;
import entity.Post;
import entity.User;
import tools.MyDataBase;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

public class CommentaireService implements IServiceCommentaire<Commentaire> {
    Connection cnx;
    String sql;
    private Statement st;
    private PreparedStatement ste;

    public CommentaireService() {
        cnx = MyDataBase.getInstance().getCnx();
    }

    public Connection getCnx() {
        return cnx;
    }

    @Override
    public void ajouterCommentaire(Commentaire commentaire) throws SQLException {
        sql = "INSERT INTO commentaire (post_id, user_id, contenu, date_commentaire, nbr_like_commentaire) VALUES (?, ?, ?, ?, ?)";
        ste = cnx.prepareStatement(sql);
        ste.setInt(1, commentaire.getPost_id());
        ste.setInt(2, commentaire.getUser().getId());
        ste.setString(3, commentaire.getContenu());
        ste.setString(4, commentaire.getDate_commentaire());
        ste.setInt(5, commentaire.getNbr_like_commentaire());
        ste.executeUpdate();
        System.out.println("Commentaire ajouté avec succès !");
    }

    @Override
    public void modifierCommentaire(int id, String contenu) throws SQLException {
        sql = "UPDATE commentaire SET contenu=?, date_commentaire = CURRENT_DATE WHERE id=?";
        ste = cnx.prepareStatement(sql);
        ste.setString(1, contenu);
        ste.setInt(2, id);
        ste.executeUpdate();
        System.out.println("Commentaire modifié avec succès !");
    }

    @Override
    public void supprimerCommentaire(Commentaire commentaire) throws SQLException {
        sql = "DELETE FROM commentaire WHERE id = ?";
        ste = cnx.prepareStatement(sql);
        ste.setInt(1, commentaire.getId());
        ste.executeUpdate();
        System.out.println("Commentaire supprimé avec succès !");
    }

    @Override
    public List<Commentaire> recupererCommentaire() throws SQLException {
        List<Commentaire> commentaires = new ArrayList<>();
        sql = "SELECT c.*, u.id as user_id, u.nom_user, u.prenom_user, u.email_user, u.telephone_user, " +
                "u.adresse, u.role_user, u.photo_user, u.date_naissance_user " +
                "FROM commentaire c " +
                "JOIN user u ON c.user_id = u.id " +
                "ORDER BY c.date_commentaire DESC";
        st = cnx.createStatement();
        ResultSet rs = st.executeQuery(sql);

        while (rs.next()) {
            // Création de l'utilisateur
            User user = new User();
            user.setId(rs.getInt("user_id"));
            user.setNom_user(rs.getString("nom_user"));
            user.setPrenom_user(rs.getString("prenom_user"));
            user.setEmail_user(rs.getString("email_user"));
            user.setTelephone_user(rs.getInt("telephone_user"));
            user.setAdresse(rs.getString("adresse"));
            user.setRole_user(rs.getString("role_user"));
            user.setPhoto_user(rs.getString("photo_user"));
            user.setDate_naissance_user(rs.getDate("date_naissance_user"));

            // Création du commentaire avec l'utilisateur
            Commentaire c = new Commentaire(
                    rs.getInt("id"),
                    rs.getInt("post_id"),
                    user,
                    rs.getString("contenu"),
                    rs.getString("date_commentaire"),
                    rs.getInt("nbr_like_commentaire")
            );
            commentaires.add(c);
        }
        return commentaires;
    }

    public List<Commentaire> recupererCommentaireParPost(int postId) throws SQLException {
        List<Commentaire> commentaires = new ArrayList<>();
        sql = "SELECT c.*, u.id as user_id, u.nom_user, u.prenom_user, u.email_user, u.telephone_user, " +
                "u.adresse, u.role_user, u.photo_user, u.date_naissance_user " +
                "FROM commentaire c " +
                "JOIN user u ON c.user_id = u.id " +
                "WHERE c.post_id = ? " +
                "ORDER BY c.date_commentaire DESC";
        ste = cnx.prepareStatement(sql);
        ste.setInt(1, postId);
        ResultSet rs = ste.executeQuery();

        while (rs.next()) {
            // Création de l'utilisateur
            User user = new User();
            user.setId(rs.getInt("user_id"));
            user.setNom_user(rs.getString("nom_user"));
            user.setPrenom_user(rs.getString("prenom_user"));
            user.setEmail_user(rs.getString("email_user"));
            user.setTelephone_user(rs.getInt("telephone_user"));
            user.setAdresse(rs.getString("adresse"));
            user.setRole_user(rs.getString("role_user"));
            user.setPhoto_user(rs.getString("photo_user"));
            user.setDate_naissance_user(rs.getDate("date_naissance_user"));

            // Création du commentaire avec l'utilisateur
            Commentaire c = new Commentaire(
                    rs.getInt("id"),
                    rs.getInt("post_id"),
                    user,
                    rs.getString("contenu"),
                    rs.getString("date_commentaire"),
                    rs.getInt("nbr_like_commentaire")
            );
            commentaires.add(c);
        }
        return commentaires;
    }
}