package service;

import entity.Produit;
import entity.User;
import javax.mail.*;
import javax.mail.internet.*;
import java.util.Properties;

public class EmailService1 {
    private static final String GMAIL = "<EMAIL>";
    private static final String APP_PASSWORD = "qycb tuyt bhie rlnl";
    private static final String APP_NAME = "Cafecul";

    public static void sendProductUpdateNotification(User recipient, Produit produit) {
        try {
            String subject = "☕ Modification de produit - " + produit.getNom_produit();
            String htmlBody = createEmailBody(produit, recipient);

            sendEmail(recipient.getEmail_user(), subject, htmlBody);
        } catch (Exception e) {
            throw new RuntimeException("Échec d'envoi de la notification", e);
        }
    }

    private static String createEmailBody(Produit produit, User recipient) {
        return "<html>"
                + "<body style='font-family: \"Segoe UI\", Tahoma, Geneva, Verdana, sans-serif; background-color: #f4f4f4; padding: 40px;'>"
                + "<div style='max-width: 600px; margin: auto; background-color: #ffffff; border-radius: 10px; box-shadow: 0 4px 10px rgba(0,0,0,0.1); overflow: hidden;'>"

                + "<div style='background-color: #3e2723; padding: 20px;'>"
                + "<h2 style='color: #fff; margin: 0; text-align: center;'>☕ Cafecul</h2>"
                + "</div>"

                + "<div style='padding: 30px;'>"
                + "<p style='font-size: 16px; color: #333;'>Bonjour <strong>" + recipient.getNom_user() + "</strong>,</p>"

                + "<p style='font-size: 15px; color: #555;'>Nous souhaitons vous informer que votre produit a été mis à jour avec succès. Voici les nouveaux détails :</p>"

                + "<table style='width: 100%; border-collapse: collapse; margin-top: 20px;'>"
                + "  <tr><td style='padding: 8px; border-bottom: 1px solid #ddd;'><strong>Nom du produit</strong></td><td style='padding: 8px; border-bottom: 1px solid #ddd;'>" + produit.getNom_produit() + "</td></tr>"
                + "  <tr><td style='padding: 8px; border-bottom: 1px solid #ddd;'><strong>Prix</strong></td><td style='padding: 8px; border-bottom: 1px solid #ddd;'>" + produit.getPrix_produit() + " TND</td></tr>"
                + "  <tr><td style='padding: 8px; border-bottom: 1px solid #ddd;'><strong>Stock</strong></td><td style='padding: 8px; border-bottom: 1px solid #ddd;'>" + produit.getStock_produit() + " unités</td></tr>"
                + "  <tr><td style='padding: 8px;'><strong>État</strong></td><td style='padding: 8px;'>" + (produit.getEtat_produit() == 1 ? "Disponible" : "Indisponible") + "</td></tr>"
                + "</table>"

                + "<p style='font-size: 14px; color: #777; margin-top: 25px;'>Ces informations ont été mises à jour dans notre système. Vous n'avez rien d'autre à faire.</p>"

                + "<p style='font-size: 15px; color: #333;'>Merci pour votre confiance,</p>"
                + "<p style='font-size: 15px; font-weight: bold; color: #3e2723;'>L'équipe Cafecul</p>"
                + "</div>"

                + "<div style='background-color: #efebe9; text-align: center; padding: 15px;'>"
                + "<p style='font-size: 13px; color: #6d4c41;'>☕ Cafecul - L'art du café depuis 2023</p>"
                + "</div>"

                + "</div>"
                + "</body>"
                + "</html>";
    }



    public static void sendEmail(String toEmail, String subject, String htmlBody) {
        System.out.println("Tentative d'envoi à: " + toEmail);

        Properties props = new Properties();
        props.put("mail.smtp.auth", "true");
        props.put("mail.smtp.starttls.enable", "true");
        props.put("mail.smtp.host", "smtp.gmail.com");
        props.put("mail.smtp.port", "587");
        props.put("mail.smtp.ssl.protocols", "TLSv1.2");
        props.put("mail.debug", "true");

        Session session = Session.getInstance(props, new Authenticator() {
            protected PasswordAuthentication getPasswordAuthentication() {
                return new PasswordAuthentication(GMAIL, APP_PASSWORD.replace(" ", ""));
            }
        });

        try {
            MimeMessage message = new MimeMessage(session);
            message.setFrom(new InternetAddress(GMAIL, APP_NAME));
            message.setRecipient(Message.RecipientType.TO, new InternetAddress(toEmail));
            message.setSubject(subject, "UTF-8");
            message.setContent(htmlBody, "text/html; charset=UTF-8");

            Transport.send(message);
            System.out.println("Email envoyé avec succès à: " + toEmail);
        } catch (Exception e) {
            System.err.println("Échec d'envoi à " + toEmail);
            e.printStackTrace();
            throw new RuntimeException("Erreur d'envoi", e);
        }
    }
}